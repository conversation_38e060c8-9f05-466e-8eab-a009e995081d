[workspace]
members = [
	"logic_engine_rust/rust_logicEngine",
	"request_rust/request_rust",
	"resource_rust/rust_resource",
	"storage_rust/rust_storage",
	"task_manager_rust/task_manager_rust",
	"updevice_rust/rust_updevice",
	"uplus_rust/rust_uplus",
	"userdomain_rust/rust_userdomain",
]
resolver = "2"

[profile.release]
debug = true

[workspace.dependencies]
flutter_rust_bridge = "=2.5.0"
security-framework = "=2.10.0"
flatbuffers = "=24.3.25"
jni = "0.21"
android_logger = "0.14"
napi-ohos = "1.0"
napi-derive-ohos = "1.0"
napi-build-ohos = "1.0"
napi-derive-backend-ohos = "=1.0.3"
napi-sys-ohos = "=1.0.3"
cc = "1.1"
serde = "1.0"
serde_json = "1.0"
log = "0.4"
parking_lot = "0.12"
reqwest = "0.12"
getset = "0.1"
sha2 = "0.10"
once_cell = "1.19"
md5 = "0.7"
uuid = "1.11"
thiserror = "2.0"
async-trait = "0.1"
async-std = "1.13"
diesel = "=2.2.6"
r2d2 = "0.8"
zip = "2.2"
tokio = "1.41"
mockall = "0.13.1"
mry = "0.10"
cucumber = "0.21"
chrono = "0.4"
futures = "0.3"
env_logger = "0.11"
dashmap = "6.1"
indextree = "4.6"
rust_decimal = "1.35"
hashlink = "0.9"
rand = "0.8"
bigdecimal = "0.4"
hex = "0.4"
regex = "1.11"
async-recursion = "1.1"
libc = "0.2"
base64 = "0.22"
openssl = "0.10"
backtrace = "0.3"
libsqlite3-sys = "0.26"
derive_builder = "=0.20.2"
