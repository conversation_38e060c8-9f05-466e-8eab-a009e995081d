{"rustc": 4013192585442940105, "features": "[\"default\"]", "declared_features": "[\"android\", \"default\", \"ios\", \"jni\", \"ohos\"]", "target": 4739343213943193933, "profile": 16589926208341333925, "path": 14025713043231864471, "deps": [[403209440722721830, "flatbuffers", false, 13916929875747166848], [902078471074753561, "async_trait", false, 6924848964811851287], [999248096845961758, "mockall", false, 17560902145970919481], [1782259520642170691, "chrono", false, 1077314309708374630], [2191969058794865154, "zip", false, 5924156494227961833], [2686471584615676929, "md5", false, 15008688995471098384], [3579365597313141177, "env_logger", false, 253409535395011925], [4405842667467550883, "mry", false, 13427038115491663384], [4851543524687823405, "uuid", false, 11537323708930761369], [6192600005060040995, "request_rust", false, 13134258364555540247], [6547034975550318241, "rust_resource", false, 5454030714055447077], [6547034975550318241, "build_script_build", false, 1104223443115062071], [7552645550685376377, "r2d2", false, 948387402328788936], [8244776183334334055, "once_cell", false, 3103360348312128811], [9768049606658611202, "task_manager", false, 5004363255588502914], [10633404241517405153, "serde", false, 3760103645878741667], [12509852874546367857, "serde_json", false, 7832552837259932300], [12594350831980718847, "async_std", false, 12395745490813235615], [12809794537881357198, "thiserror", false, 5136432431861168746], [13088276697883888127, "cucumber", false, 10749161952513323285], [14707977045625059585, "diesel", false, 12382295746640125858], [15399619262696441677, "log", false, 678606382585774568], [15447401961974210701, "futures", false, 16900544321409691916], [16466393719188463918, "reqwest", false, 18041989578603999367], [17333407209009474459, "tokio", false, 347251518126286229]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rust_resource-4ed6d486f3ec4ef0/dep-test-integration-test-cucumber"}}], "rustflags": [], "metadata": 2070643619040027225, "config": 2202906307356721367, "compile_kind": 0}