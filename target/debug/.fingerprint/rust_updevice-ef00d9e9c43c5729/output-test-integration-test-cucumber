{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::device_attribute::UpDeviceAttribute`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":277,"byte_end":334,"line_start":7,"line_end":7,"column_start":5,"column_end":62,"is_primary":true,"text":[{"text":"use logic_engine::device::device_attribute::UpDeviceAttribute;","highlight_start":5,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":273,"byte_end":336,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::device_attribute::UpDeviceAttribute;","highlight_start":1,"highlight_end":63},{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::device_attribute::UpDeviceAttribute`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::device_attribute::UpDeviceAttribute;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::device_caution::UpDeviceCaution`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":340,"byte_end":393,"line_start":8,"line_end":8,"column_start":5,"column_end":58,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":5,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":336,"byte_end":395,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":1,"highlight_end":59},{"text":"use rust_updevice::api::device_filter::UpDeviceFilter;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::device_caution::UpDeviceCaution`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::device_caution::UpDeviceCaution;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `convert_attribute_from` and `convert_caution_from`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":1423,"byte_end":1445,"line_start":25,"line_end":25,"column_start":41,"column_end":63,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":41,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":1447,"byte_end":1467,"line_start":25,"line_end":25,"column_start":65,"column_end":85,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":65,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":1383,"byte_end":1470,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":1,"highlight_end":87},{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{uhsd_usr_dev_connect_state_e, uhsd_usr_dev_online_state_e, uhsd_usr_device_bind_state_e, uhsd_usr_device_offline_cause_e, uhsd_usr_device_sleep_state_e, DeviceInfo};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `convert_attribute_from` and `convert_caution_from`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:25:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `cucumber::gherkin::Step`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/toolkit_steps.rs","byte_start":33,"byte_end":56,"line_start":2,"line_end":2,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/toolkit_steps.rs","byte_start":29,"byte_end":58,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `cucumber::gherkin::Step`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/toolkit_steps.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::gherkin::Step;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `convert_attribute_from` and `convert_caution_from`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":849,"byte_end":871,"line_start":18,"line_end":18,"column_start":41,"column_end":63,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":41,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":873,"byte_end":893,"line_start":18,"line_end":18,"column_start":65,"column_end":85,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":65,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":809,"byte_end":896,"line_start":18,"line_end":19,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};","highlight_start":1,"highlight_end":87},{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `convert_attribute_from` and `convert_caution_from`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:18:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::utils::convert_vec::{convert_attribute_from, convert_caution_from};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UhsdPairList`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":951,"byte_end":963,"line_start":19,"line_end":19,"column_start":56,"column_end":68,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":56,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":949,"byte_end":963,"line_start":19,"line_end":19,"column_start":54,"column_end":68,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":54,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":940,"byte_end":941,"line_start":19,"line_end":19,"column_start":45,"column_end":46,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":45,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":963,"byte_end":964,"line_start":19,"line_end":19,"column_start":68,"column_end":69,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};","highlight_start":68,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UhsdPairList`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:19:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_usdk::toolkit_ffi::uhsd_usr_model::{UhsdPair, UhsdPairList};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary braces around method argument","code":{"code":"unused_braces","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29354,"byte_end":29356,"line_start":722,"line_end":722,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29364,"byte_end":29366,"line_start":722,"line_end":722,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_braces)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove these braces","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29354,"byte_end":29356,"line_start":722,"line_end":722,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29364,"byte_end":29366,"line_start":722,"line_end":722,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unnecessary braces around method argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:722:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_braces)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these braces\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(\u001b[0m\u001b[0m\u001b[38;5;9m{ \u001b[0m\u001b[0mArc::new\u001b[0m\u001b[0m\u001b[38;5;9m }\u001b[0m\u001b[0m).collect();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m722\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(Arc::new).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unnecessary braces around method argument","code":{"code":"unused_braces","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29783,"byte_end":29785,"line_start":730,"line_end":730,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29793,"byte_end":29795,"line_start":730,"line_end":730,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove these braces","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29783,"byte_end":29785,"line_start":730,"line_end":730,"column_start":53,"column_end":55,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":53,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":29793,"byte_end":29795,"line_start":730,"line_end":730,"column_start":63,"column_end":65,"is_primary":true,"text":[{"text":"    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();","highlight_start":63,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unnecessary braces around method argument\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:730:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map({ Arc::new }).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove these braces\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(\u001b[0m\u001b[0m\u001b[38;5;9m{ \u001b[0m\u001b[0mArc::new\u001b[0m\u001b[0m\u001b[38;5;9m }\u001b[0m\u001b[0m).collect();\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let arc_attributes = attributes.into_iter().map(Arc::new).collect();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `StepUtils`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":164,"byte_end":173,"line_start":4,"line_end":4,"column_start":48,"column_end":57,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":48,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":162,"byte_end":173,"line_start":4,"line_end":4,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":46,"highlight_end":57}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":147,"byte_end":148,"line_start":4,"line_end":4,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":173,"byte_end":174,"line_start":4,"line_end":4,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"use crate::utils::step_utils::{get_step_utils, StepUtils};","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `StepUtils`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:4:48\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::step_utils::{get_step_utils, StepUtils};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `libc::stat`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":264,"byte_end":274,"line_start":8,"line_end":8,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use libc::stat;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":260,"byte_end":276,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use libc::stat;","highlight_start":1,"highlight_end":16},{"text":"use logic_engine::device::command::Command;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `libc::stat`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse libc::stat;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::command::Command`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":280,"byte_end":318,"line_start":9,"line_end":9,"column_start":5,"column_end":43,"is_primary":true,"text":[{"text":"use logic_engine::device::command::Command;","highlight_start":5,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":276,"byte_end":320,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::command::Command;","highlight_start":1,"highlight_end":44},{"text":"use logic_engine::device::device_attribute::UpDeviceAttribute;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::command::Command`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::command::Command;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device::device_caution::UpDeviceCaution`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":387,"byte_end":440,"line_start":11,"line_end":11,"column_start":5,"column_end":58,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":5,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":383,"byte_end":442,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::device_caution::UpDeviceCaution;","highlight_start":1,"highlight_end":59},{"text":"use logic_engine::device::device_command::UpDeviceCommand;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device::device_caution::UpDeviceCaution`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::device_caution::UpDeviceCaution;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `command` and `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":528,"byte_end":532,"line_start":13,"line_end":13,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use logic_engine::device::{self, command};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":534,"byte_end":541,"line_start":13,"line_end":13,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use logic_engine::device::{self, command};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":501,"byte_end":544,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device::{self, command};","highlight_start":1,"highlight_end":43},{"text":"use logic_engine::device_config::alarm;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `command` and `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:13:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device::{self, command};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::device_config::alarm`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":548,"byte_end":582,"line_start":14,"line_end":14,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"use logic_engine::device_config::alarm;","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":544,"byte_end":584,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::device_config::alarm;","highlight_start":1,"highlight_end":40},{"text":"use logic_engine::engine::modifier::trigger::alarm_trigger;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::device_config::alarm`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::device_config::alarm;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `logic_engine::engine::modifier::trigger::alarm_trigger`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":588,"byte_end":642,"line_start":15,"line_end":15,"column_start":5,"column_end":59,"is_primary":true,"text":[{"text":"use logic_engine::engine::modifier::trigger::alarm_trigger;","highlight_start":5,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":584,"byte_end":644,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use logic_engine::engine::modifier::trigger::alarm_trigger;","highlight_start":1,"highlight_end":60},{"text":"use once_cell::sync::Lazy;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `logic_engine::engine::modifier::trigger::alarm_trigger`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse logic_engine::engine::modifier::trigger::alarm_trigger;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::api::device_injection::UpDeviceInjection`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":675,"byte_end":730,"line_start":17,"line_end":17,"column_start":5,"column_end":60,"is_primary":true,"text":[{"text":"use rust_updevice::api::device_injection::UpDeviceInjection;","highlight_start":5,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":671,"byte_end":732,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::api::device_injection::UpDeviceInjection;","highlight_start":1,"highlight_end":61},{"text":"use rust_updevice::api::error::DeviceError;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::api::device_injection::UpDeviceInjection`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::api::device_injection::UpDeviceInjection;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::models::device_connect_state::UpDeviceConnectState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":828,"byte_end":893,"line_start":20,"line_end":20,"column_start":5,"column_end":70,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_connect_state::UpDeviceConnectState;","highlight_start":5,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":824,"byte_end":895,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_updevice::models::device_connect_state::UpDeviceConnectState;","highlight_start":1,"highlight_end":71},{"text":"use rust_updevice::models::device_online_state::UpDeviceOnlineState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::models::device_connect_state::UpDeviceConnectState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::models::device_connect_state::UpDeviceConnectState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_usdk::toolkit_ffi::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1128,"byte_end":1153,"line_start":24,"line_end":24,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::*;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1124,"byte_end":1155,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::*;","highlight_start":1,"highlight_end":31},{"text":"static DEVICE_TOOLKIT: Lazy<DeviceToolkit> = Lazy::new(|| DeviceToolkit::new());","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_usdk::toolkit_ffi::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_usdk::toolkit_ffi::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UhsdPairList`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1574,"byte_end":1586,"line_start":31,"line_end":31,"column_start":140,"column_end":152,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{DeviceConnectState, DeviceCtrlRoute, DeviceFeedback, DeviceInfo, DeviceOnlineState, UhsdPair, UhsdPairList};","highlight_start":140,"highlight_end":152}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1572,"byte_end":1586,"line_start":31,"line_end":31,"column_start":138,"column_end":152,"is_primary":true,"text":[{"text":"use rust_usdk::toolkit_ffi::uhsd_usr_model::{DeviceConnectState, DeviceCtrlRoute, DeviceFeedback, DeviceInfo, DeviceOnlineState, UhsdPair, UhsdPairList};","highlight_start":138,"highlight_end":152}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `UhsdPairList`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:31:140\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mte, DeviceFeedback, DeviceInfo, DeviceOnlineState, UhsdPair, UhsdPairList};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `when`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/voice_box_device_steps.rs","byte_start":43,"byte_end":47,"line_start":1,"line_end":1,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"use cucumber::{gherkin::Step, given, then, when};","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/voice_box_device_steps.rs","byte_start":41,"byte_end":47,"line_start":1,"line_end":1,"column_start":42,"column_end":48,"is_primary":true,"text":[{"text":"use cucumber::{gherkin::Step, given, then, when};","highlight_start":42,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `when`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/voice_box_device_steps.rs:1:44\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{gherkin::Step, given, then, when};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `clear`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":20613,"byte_end":20618,"line_start":483,"line_end":483,"column_start":73,"column_end":78,"is_primary":true,"text":[{"text":"async fn execute_operate_command(_world: &mut MyWorld, command: String, clear: String) {","highlight_start":73,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":20613,"byte_end":20618,"line_start":483,"line_end":483,"column_start":73,"column_end":78,"is_primary":true,"text":[{"text":"async fn execute_operate_command(_world: &mut MyWorld, command: String, clear: String) {","highlight_start":73,"highlight_end":78}],"label":null,"suggested_replacement":"_clear","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `clear`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:483:73\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m483\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn execute_operate_command(_world: &mut MyWorld, command: String, clear: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_clear`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a mutable reference to mutable static is discouraged","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5143,"byte_end":5156,"line_start":114,"line_end":114,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":31}],"label":"mutable reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see issue #114447 <https://github.com/rust-lang/rust/issues/114447>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"this will be a hard error in the 2024 edition","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"this mutable reference has lifetime `'static`, but if the static gets accessed (read or written) by any other means, or any other reference is created, then any further use of this mutable reference is Undefined Behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `addr_of_mut!` instead to create a raw pointer","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5143,"byte_end":5148,"line_start":114,"line_end":114,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":"addr_of_mut!(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5156,"byte_end":5156,"line_start":114,"line_end":114,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a mutable reference to mutable static is discouraged\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:114:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        unsafe { &mut INSTANCE }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mmutable reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see issue #114447 <https://github.com/rust-lang/rust/issues/114447>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this will be a hard error in the 2024 edition\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this mutable reference has lifetime `'static`, but if the static gets accessed (read or written) by any other means, or any other reference is created, then any further use of this mutable reference is Undefined Behavior\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `addr_of_mut!` instead to create a raw pointer\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        unsafe { \u001b[0m\u001b[0m\u001b[38;5;10maddr_of_mut!(\u001b[0m\u001b[0mINSTANCE\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\u001b[0m }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_updevice::device::up_device::UpDevice`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":780,"byte_end":822,"line_start":19,"line_end":19,"column_start":5,"column_end":47,"is_primary":true,"text":[{"text":"use rust_updevice::device::up_device::UpDevice;","highlight_start":5,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_updevice::device::up_device::UpDevice`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_updevice::device::up_device::UpDevice;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `subscription_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":1526,"byte_end":1541,"line_start":48,"line_end":48,"column_start":46,"column_end":61,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_list_change(&self, subscription_id: &str) {","highlight_start":46,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":1526,"byte_end":1541,"line_start":48,"line_end":48,"column_start":46,"column_end":61,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_list_change(&self, subscription_id: &str) {","highlight_start":46,"highlight_end":61}],"label":null,"suggested_replacement":"_subscription_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `subscription_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs:48:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn unsubscribe_device_list_change(&self, subscription_id: &str) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_subscription_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `area`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1138,"byte_end":1142,"line_start":34,"line_end":34,"column_start":25,"column_end":29,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":25,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1138,"byte_end":1142,"line_start":34,"line_end":34,"column_start":25,"column_end":29,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":25,"highlight_end":29}],"label":null,"suggested_replacement":"_area","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `area`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_area`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_info`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1150,"byte_end":1158,"line_start":34,"line_end":34,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1150,"byte_end":1158,"line_start":34,"line_end":34,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":"_app_info","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `app_info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_app_info`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `client_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1169,"byte_end":1178,"line_start":34,"line_end":34,"column_start":56,"column_end":65,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":56,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1169,"byte_end":1178,"line_start":34,"line_end":34,"column_start":56,"column_end":65,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":56,"highlight_end":65}],"label":null,"suggested_replacement":"_client_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `client_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_client_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `enable_http_nds`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1188,"byte_end":1203,"line_start":34,"line_end":34,"column_start":75,"column_end":90,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":75,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1188,"byte_end":1203,"line_start":34,"line_end":34,"column_start":75,"column_end":90,"is_primary":true,"text":[{"text":"    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {","highlight_start":75,"highlight_end":90}],"label":null,"suggested_replacement":"_enable_http_nds","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `enable_http_nds`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:34:75\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn start_sdk(&self, area: Area, app_info: AppInfo, client_id: String, enable_http_nds: bool) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_enable_http_nds`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1287,"byte_end":1294,"line_start":38,"line_end":38,"column_start":38,"column_end":45,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":38,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1287,"byte_end":1294,"line_start":38,"line_end":38,"column_start":38,"column_end":45,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":38,"highlight_end":45}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:38:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `access_token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1304,"byte_end":1316,"line_start":38,"line_end":38,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1304,"byte_end":1316,"line_start":38,"line_end":38,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":"_access_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `access_token`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:38:55\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn connect_remote_devices(&self, user_id: String, access_token: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_access_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `access_token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1478,"byte_end":1490,"line_start":46,"line_end":46,"column_start":35,"column_end":47,"is_primary":true,"text":[{"text":"    fn update_access_token(&self, access_token: String) -> Result<()> {","highlight_start":35,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1478,"byte_end":1490,"line_start":46,"line_end":46,"column_start":35,"column_end":47,"is_primary":true,"text":[{"text":"    fn update_access_token(&self, access_token: String) -> Result<()> {","highlight_start":35,"highlight_end":47}],"label":null,"suggested_replacement":"_access_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `access_token`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:46:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn update_access_token(&self, access_token: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_access_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `listener`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1582,"byte_end":1590,"line_start":50,"line_end":50,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"    fn subscribe_device_list_change(&self, listener: FnClone<ToolkitEvent>) -> Result<String> {","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":1582,"byte_end":1590,"line_start":50,"line_end":50,"column_start":44,"column_end":52,"is_primary":true,"text":[{"text":"    fn subscribe_device_list_change(&self, listener: FnClone<ToolkitEvent>) -> Result<String> {","highlight_start":44,"highlight_end":52}],"label":null,"suggested_replacement":"_listener","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `listener`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:50:44\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn subscribe_device_list_change(&self, listener: FnClone<ToolkitEvent>) -> Result<String> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_listener`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `subscription_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2175,"byte_end":2190,"line_start":69,"line_end":69,"column_start":41,"column_end":56,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_change(&self, subscription_id: String) -> Result<()> {","highlight_start":41,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2175,"byte_end":2190,"line_start":69,"line_end":69,"column_start":41,"column_end":56,"is_primary":true,"text":[{"text":"    fn unsubscribe_device_change(&self, subscription_id: String) -> Result<()> {","highlight_start":41,"highlight_end":56}],"label":null,"suggested_replacement":"_subscription_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `subscription_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:69:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn unsubscribe_device_change(&self, subscription_id: String) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_subscription_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2292,"byte_end":2301,"line_start":75,"line_end":75,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        device_id: &str,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2292,"byte_end":2301,"line_start":75,"line_end":75,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        device_id: &str,","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:75:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        device_id: &str,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `command`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2317,"byte_end":2324,"line_start":76,"line_end":76,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        command: UpDeviceCommand,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2317,"byte_end":2324,"line_start":76,"line_end":76,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        command: UpDeviceCommand,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_command","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `command`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:76:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        command: UpDeviceCommand,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_command`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `timeout`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2351,"byte_end":2358,"line_start":77,"line_end":77,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        timeout: uhsd_u32,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2351,"byte_end":2358,"line_start":77,"line_end":77,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"        timeout: uhsd_u32,","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_timeout","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `timeout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:77:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        timeout: uhsd_u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_timeout`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `route`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2378,"byte_end":2383,"line_start":78,"line_end":78,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"        route: DeviceCtrlRoute,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2378,"byte_end":2383,"line_start":78,"line_end":78,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"        route: DeviceCtrlRoute,","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_route","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `route`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:78:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        route: DeviceCtrlRoute,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_route`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2583,"byte_end":2592,"line_start":87,"line_end":87,"column_start":41,"column_end":50,"is_primary":true,"text":[{"text":"    fn get_device_attribute_list(&self, device_id: &str) -> Result<Vec<UpDeviceAttribute>> {","highlight_start":41,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2583,"byte_end":2592,"line_start":87,"line_end":87,"column_start":41,"column_end":50,"is_primary":true,"text":[{"text":"    fn get_device_attribute_list(&self, device_id: &str) -> Result<Vec<UpDeviceAttribute>> {","highlight_start":41,"highlight_end":50}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:87:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_device_attribute_list(&self, device_id: &str) -> Result<Vec<UpDeviceAttribute>> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2689,"byte_end":2698,"line_start":91,"line_end":91,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo> {","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":2689,"byte_end":2698,"line_start":91,"line_end":91,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo> {","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:91:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m91\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_config_data_source.rs","byte_start":515,"byte_end":524,"line_start":17,"line_end":17,"column_start":39,"column_end":48,"is_primary":true,"text":[{"text":"    async fn get_device_config(&self, device_id: &str) -> Result<String, ConfigDataSourceError> {","highlight_start":39,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_config_data_source.rs","byte_start":515,"byte_end":524,"line_start":17,"line_end":17,"column_start":39,"column_end":48,"is_primary":true,"text":[{"text":"    async fn get_device_config(&self, device_id: &str) -> Result<String, ConfigDataSourceError> {","highlight_start":39,"highlight_end":48}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_config_data_source.rs:17:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_device_config(&self, device_id: &str) -> Result<String, ConfigDataSourceError> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16649,"byte_end":16655,"line_start":378,"line_end":378,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let result = identification_string_to_bool(result_str);","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16649,"byte_end":16655,"line_start":378,"line_end":378,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let result = identification_string_to_bool(result_str);","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:378:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m378\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let result = identification_string_to_bool(result_str);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mock_toolkit`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16709,"byte_end":16721,"line_start":379,"line_end":379,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let mock_toolkit = get_mock_device_toolkit_mut();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":16709,"byte_end":16721,"line_start":379,"line_end":379,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let mock_toolkit = get_mock_device_toolkit_mut();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_mock_toolkit","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mock_toolkit`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:379:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m379\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mock_toolkit = get_mock_device_toolkit_mut();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mock_toolkit`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":30982,"byte_end":31000,"line_start":744,"line_end":744,"column_start":21,"column_end":39,"is_primary":true,"text":[{"text":"        if let Some(mut filters_holder) =","highlight_start":21,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":30982,"byte_end":30986,"line_start":744,"line_end":744,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"        if let Some(mut filters_holder) =","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:744:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m744\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(mut filters_holder) =\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `protocol`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":32638,"byte_end":32646,"line_start":782,"line_end":782,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    protocol: String,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/device_manager_steps.rs","byte_start":32638,"byte_end":32646,"line_start":782,"line_end":782,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    protocol: String,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":"_protocol","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `protocol`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/device_manager_steps.rs:782:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m782\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    protocol: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_protocol`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":3896,"byte_end":3905,"line_start":82,"line_end":82,"column_start":52,"column_end":61,"is_primary":true,"text":[{"text":"fn when_device_status_change(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":52,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":3896,"byte_end":3905,"line_start":82,"line_end":82,"column_start":52,"column_end":61,"is_primary":true,"text":[{"text":"fn when_device_status_change(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":52,"highlight_end":61}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:82:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_device_status_change(_world: &mut MyWorld, device_id: String, step: &Step) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":5362,"byte_end":5367,"line_start":118,"line_end":118,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"fn then_user_query_device_attribute_list(world: &mut MyWorld, _device_id: String, step: &Step) {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":5362,"byte_end":5367,"line_start":118,"line_end":118,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"fn then_user_query_device_attribute_list(world: &mut MyWorld, _device_id: String, step: &Step) {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:118:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_device_attribute_list(world: &mut MyWorld, _device_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":6059,"byte_end":6064,"line_start":133,"line_end":133,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"fn then_user_query_real_device_attribute_list(world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":6059,"byte_end":6064,"line_start":133,"line_end":133,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"fn then_user_query_real_device_attribute_list(world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:133:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_real_device_attribute_list(world: &mut MyWorld, device_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":7700,"byte_end":7709,"line_start":170,"line_end":170,"column_start":60,"column_end":69,"is_primary":true,"text":[{"text":"fn then_user_query_device_alarm_list(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":60,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":7700,"byte_end":7709,"line_start":170,"line_end":170,"column_start":60,"column_end":69,"is_primary":true,"text":[{"text":"fn then_user_query_device_alarm_list(_world: &mut MyWorld, device_id: String, step: &Step) {","highlight_start":60,"highlight_end":69}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:170:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_device_alarm_list(_world: &mut MyWorld, device_id: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":9101,"byte_end":9110,"line_start":204,"line_end":204,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    device_id: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":9101,"byte_end":9110,"line_start":204,"line_end":204,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    device_id: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:204:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    device_id: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":10180,"byte_end":10189,"line_start":236,"line_end":236,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn then_user_query_device_online_state(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":10180,"byte_end":10189,"line_start":236,"line_end":236,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn then_user_query_device_online_state(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:236:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_user_query_device_online_state(_world: &mut MyWorld, device_id: String, state_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":11174,"byte_end":11183,"line_start":264,"line_end":264,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn when_device_connection_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":11174,"byte_end":11183,"line_start":264,"line_end":264,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"fn when_device_connection_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:264:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m264\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_device_connection_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":12261,"byte_end":12270,"line_start":294,"line_end":294,"column_start":58,"column_end":67,"is_primary":true,"text":[{"text":"fn when_device_online_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":58,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/up_device_steps.rs","byte_start":12261,"byte_end":12270,"line_start":294,"line_end":294,"column_start":58,"column_end":67,"is_primary":true,"text":[{"text":"fn when_device_online_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {","highlight_start":58,"highlight_end":67}],"label":null,"suggested_replacement":"_device_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/up_device_steps.rs:294:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn when_device_online_state_change(_world: &mut MyWorld, device_id: String, state_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_device_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `connect_state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28855,"byte_end":28868,"line_start":711,"line_end":711,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let connect_state = if is_ready {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28855,"byte_end":28868,"line_start":711,"line_end":711,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let connect_state = if is_ready {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_connect_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `connect_state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:711:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m711\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let connect_state = if is_ready {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_connect_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `mock_engine_device`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28992,"byte_end":29010,"line_start":716,"line_end":716,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    let mock_engine_device = UpDeviceTestHolder::get_instance().get_engine_device();","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/engine_device_steps.rs","byte_start":28992,"byte_end":29010,"line_start":716,"line_end":716,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"    let mock_engine_device = UpDeviceTestHolder::get_instance().get_engine_device();","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":"_mock_engine_device","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `mock_engine_device`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/engine_device_steps.rs:716:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m716\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mock_engine_device = UpDeviceTestHolder::get_instance().get_engine_device();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_mock_engine_device`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `uhsd_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1934,"byte_end":1946,"line_start":38,"line_end":38,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    uhsd_manager: String,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1934,"byte_end":1946,"line_start":38,"line_end":38,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    uhsd_manager: String,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"_uhsd_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `uhsd_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:38:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    uhsd_manager: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_uhsd_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `uhsd_device_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1960,"byte_end":1979,"line_start":39,"line_end":39,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    uhsd_device_manager: String,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":1960,"byte_end":1979,"line_start":39,"line_end":39,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    uhsd_device_manager: String,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":"_uhsd_device_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `uhsd_device_manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:39:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    uhsd_device_manager: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_uhsd_device_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":2688,"byte_end":2694,"line_start":58,"line_end":58,"column_start":62,"column_end":68,"is_primary":true,"text":[{"text":"fn mock_lib_uhome_subscribe_device_list(_world: &mut MyWorld,result: String) {","highlight_start":62,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":2688,"byte_end":2694,"line_start":58,"line_end":58,"column_start":62,"column_end":68,"is_primary":true,"text":[{"text":"fn mock_lib_uhome_subscribe_device_list(_world: &mut MyWorld,result: String) {","highlight_start":62,"highlight_end":68}],"label":null,"suggested_replacement":"_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:58:62\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn mock_lib_uhome_subscribe_device_list(_world: &mut MyWorld,result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `step`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":12980,"byte_end":12984,"line_start":339,"line_end":339,"column_start":72,"column_end":76,"is_primary":true,"text":[{"text":"fn check_uhsdk_login_called_times(_world: &mut MyWorld, times: String, step: &Step) {","highlight_start":72,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":12980,"byte_end":12984,"line_start":339,"line_end":339,"column_start":72,"column_end":76,"is_primary":true,"text":[{"text":"fn check_uhsdk_login_called_times(_world: &mut MyWorld, times: String, step: &Step) {","highlight_start":72,"highlight_end":76}],"label":null,"suggested_replacement":"_step","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `step`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:339:72\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m339\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn check_uhsdk_login_called_times(_world: &mut MyWorld, times: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_step`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":19909,"byte_end":19911,"line_start":519,"line_end":519,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":19909,"byte_end":19911,"line_start":519,"line_end":519,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:519:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m519\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":21112,"byte_end":21114,"line_start":549,"line_end":549,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":21112,"byte_end":21114,"line_start":549,"line_end":549,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:549:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m549\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `connect_state` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22157,"byte_end":22170,"line_start":577,"line_end":577,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"    let mut connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_DISCONNECTED;","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_connect_state` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `connect_state` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:577:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m577\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_DISCONNECTED;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_connect_state` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `connect_state` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22279,"byte_end":22292,"line_start":579,"line_end":579,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_READY;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `connect_state` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:579:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m579\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        connect_state = DeviceConnectState::UHSD_USR_DEV_CONNECT_STATE_READY;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22588,"byte_end":22590,"line_start":585,"line_end":585,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":22588,"byte_end":22590,"line_start":585,"line_end":585,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:585:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m585\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `online_state` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":23858,"byte_end":23870,"line_start":617,"line_end":617,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"    let mut online_state = UpDeviceOnlineState::Offline;","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_online_state` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `online_state` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:617:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m617\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut online_state = UpDeviceOnlineState::Offline;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `_online_state` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `online_state` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":23945,"byte_end":23957,"line_start":619,"line_end":619,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        online_state = UpDeviceOnlineState::Online;","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `online_state` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:619:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m619\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        online_state = UpDeviceOnlineState::Online;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":24227,"byte_end":24229,"line_start":625,"line_end":625,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs","byte_start":24227,"byte_end":24229,"line_start":625,"line_end":625,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"                device_id: id,","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/steps/usdk_toolkit_steps.rs:625:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m625\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                device_id: id,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `device_list` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":406,"byte_end":426,"line_start":9,"line_end":9,"column_start":12,"column_end":32,"is_primary":false,"text":[{"text":"pub struct FakeDeviceDataSource {","highlight_start":12,"highlight_end":32}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":433,"byte_end":444,"line_start":10,"line_end":10,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    device_list: RwLock<Vec<UpDeviceBaseInfo>>,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `device_list` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct FakeDeviceDataSource {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    device_list: RwLock<Vec<UpDeviceBaseInfo>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated function `new` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":480,"byte_end":505,"line_start":13,"line_end":13,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"impl FakeDeviceDataSource {","highlight_start":1,"highlight_end":26}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs","byte_start":519,"byte_end":522,"line_start":14,"line_end":14,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated function `new` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_data_source.rs:14:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl FakeDeviceDataSource {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new` and `publish_device_attribute_change_event` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":620,"byte_end":642,"line_start":14,"line_end":14,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"impl FakeDeviceToolkit {","highlight_start":1,"highlight_end":23}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":656,"byte_end":659,"line_start":15,"line_end":15,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs","byte_start":719,"byte_end":756,"line_start":19,"line_end":19,"column_start":12,"column_end":49,"is_primary":true,"text":[{"text":"    pub fn publish_device_attribute_change_event(","highlight_start":12,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new` and `publish_device_attribute_change_event` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/fake/fake_device_toolkit.rs:15:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl FakeDeviceToolkit {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn publish_device_attribute_change_event(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `0` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":2904,"byte_end":2910,"line_start":60,"line_end":60,"column_start":5,"column_end":11,"is_primary":false,"text":[{"text":"    String(String),","highlight_start":5,"highlight_end":11}],"label":"field in this variant","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":2911,"byte_end":2917,"line_start":60,"line_end":60,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    String(String),","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":2911,"byte_end":2917,"line_start":60,"line_end":60,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    String(String),","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":"()","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `0` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:60:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    String(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this variant\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    String(\u001b[0m\u001b[0m\u001b[38;5;10m()\u001b[0m\u001b[0m),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;10m~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `0` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":3582,"byte_end":3588,"line_start":79,"line_end":79,"column_start":5,"column_end":11,"is_primary":false,"text":[{"text":"    String(String),","highlight_start":5,"highlight_end":11}],"label":"field in this variant","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":3589,"byte_end":3595,"line_start":79,"line_end":79,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    String(String),","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":3589,"byte_end":3595,"line_start":79,"line_end":79,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    String(String),","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":"()","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `0` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:79:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    String(String),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this variant\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider changing the field to be of unit type to suppress this warning while preserving the field numbering, or remove the field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    String(\u001b[0m\u001b[0m\u001b[38;5;10m()\u001b[0m\u001b[0m),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[38;5;10m~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `create_device`, `set_config_data_source`, `get_device_factory`, `get_real_device_factory`, and `push_device_into_data_source` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":5051,"byte_end":5074,"line_start":112,"line_end":112,"column_start":1,"column_end":24,"is_primary":false,"text":[{"text":"impl UpDeviceTestHolder {","highlight_start":1,"highlight_end":24}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":6337,"byte_end":6350,"line_start":145,"line_end":145,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn create_device(","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":8519,"byte_end":8541,"line_start":205,"line_end":205,"column_start":12,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn set_config_data_source(&mut self, config_data_source: Box<dyn ConfigDataSource>) {","highlight_start":12,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":9125,"byte_end":9143,"line_start":218,"line_end":218,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_device_factory(&self) -> Option<&Box<dyn UpDeviceFactory>> {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":9532,"byte_end":9555,"line_start":230,"line_end":230,"column_start":12,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn get_real_device_factory(&self) -> &DeviceFactory {","highlight_start":12,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"updevice_rust/rust_updevice/tests/utils/device_test_holder.rs","byte_start":9721,"byte_end":9749,"line_start":237,"line_end":237,"column_start":12,"column_end":40,"is_primary":true,"text":[{"text":"    pub fn push_device_into_data_source(&self, device_base_info: Option<UpDeviceBaseInfo>) {","highlight_start":12,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `create_device`, `set_config_data_source`, `get_device_factory`, `get_real_device_factory`, and `push_device_into_data_source` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/tests/utils/device_test_holder.rs:145:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m112\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl UpDeviceTestHolder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn create_device(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_config_data_source(&mut self, config_data_source: Box<dyn ConfigDataSource>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_device_factory(&self) -> Option<&Box<dyn UpDeviceFactory>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_real_device_factory(&self) -> &DeviceFactory {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn push_device_into_data_source(&self, device_base_info: Option<UpDeviceBaseInfo>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"72 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 72 warnings emitted\u001b[0m\n\n"}
