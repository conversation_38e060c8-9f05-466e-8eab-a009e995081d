{"$message_type":"diagnostic","message":"no field `card_page_icon` on type `rust_userdomain::models::device_info::DeviceInfo`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `Struct<PERSON>ithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/src/models/device_info.rs","byte_start":6131,"byte_end":6145,"line_start":168,"line_end":168,"column_start":52,"column_end":66,"is_primary":true,"text":[{"text":"            .card_page_icon(userdomain_device_info.card_page_icon)","highlight_start":52,"highlight_end":66}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a field with a similar name exists","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/src/models/device_info.rs","byte_start":6131,"byte_end":6145,"line_start":168,"line_end":168,"column_start":52,"column_end":66,"is_primary":true,"text":[{"text":"            .card_page_icon(userdomain_device_info.card_page_icon)","highlight_start":52,"highlight_end":66}],"label":null,"suggested_replacement":"card_page_img","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `card_page_icon` on type `rust_userdomain::models::device_info::DeviceInfo`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/models/device_info.rs:168:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .card_page_icon(userdomain_device_info.card_page_icon)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a field with a similar name exists\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m168\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m            .card_page_icon(userdomain_device_info.\u001b[0m\u001b[0m\u001b[38;5;10mcard_page_img\u001b[0m\u001b[0m)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `small_card_sort` on type `rust_userdomain::models::device_info::DeviceInfo`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/src/models/device_info.rs","byte_start":6264,"byte_end":6279,"line_start":170,"line_end":170,"column_start":53,"column_end":68,"is_primary":true,"text":[{"text":"            .small_card_sort(userdomain_device_info.small_card_sort)","highlight_start":53,"highlight_end":68}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `device_id`, `device_name`, `dev_name`, `device_type`, `family_id` ... and 45 others","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `small_card_sort` on type `rust_userdomain::models::device_info::DeviceInfo`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/models/device_info.rs:170:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .small_card_sort(userdomain_device_info.small_card_sort)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `device_id`, `device_name`, `dev_name`, `device_type`, `family_id` ... and 45 others\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `large_card_sort` on type `rust_userdomain::models::device_info::DeviceInfo`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"updevice_rust/rust_updevice/src/models/device_info.rs","byte_start":6333,"byte_end":6348,"line_start":171,"line_end":171,"column_start":53,"column_end":68,"is_primary":true,"text":[{"text":"            .large_card_sort(userdomain_device_info.large_card_sort)","highlight_start":53,"highlight_end":68}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `device_id`, `device_name`, `dev_name`, `device_type`, `family_id` ... and 45 others","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m: no field `large_card_sort` on type `rust_userdomain::models::device_info::DeviceInfo`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/models/device_info.rs:171:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .large_card_sort(userdomain_device_info.large_card_sort)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `device_id`, `device_name`, `dev_name`, `device_type`, `family_id` ... and 45 others\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `device_ids`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"updevice_rust/rust_updevice/src/api/device_manager.rs","byte_start":22489,"byte_end":22499,"line_start":611,"line_end":611,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"            ToolkitEvent::DevicesRemoved {device_ids} => {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"updevice_rust/rust_updevice/src/api/device_manager.rs","byte_start":22489,"byte_end":22499,"line_start":611,"line_end":611,"column_start":43,"column_end":53,"is_primary":true,"text":[{"text":"            ToolkitEvent::DevicesRemoved {device_ids} => {","highlight_start":43,"highlight_end":53}],"label":null,"suggested_replacement":"device_ids: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `device_ids`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mupdevice_rust/rust_updevice/src/api/device_manager.rs:611:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m611\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ToolkitEvent::DevicesRemoved {device_ids} => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `device_ids: _`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 3 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 3 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0609`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0609`.\u001b[0m\n"}
