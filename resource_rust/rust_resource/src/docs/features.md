# Features模块

Features模块负责跨平台FFI接口的实现，支持Android、iOS和HarmonyOS三个平台的原生调用。该模块通过条件编译实现平台特定的功能，同时使用FlatBuffers进行跨平台数据序列化。

## 模块结构

```
src/features/
├── android.rs              # Android平台FFI接口
├── ios.rs                  # iOS平台FFI接口  
├── ohos.rs                 # HarmonyOS平台FFI接口
├── cross_platform.rs      # 跨平台序列化
├── ffi_models.rs           # FFI数据模型
└── mod.rs
```

## 平台特性

### 条件编译

```rust
#[cfg(feature = "android")]
mod android;

#[cfg(feature = "ios")]
mod ios;

#[cfg(feature = "ohos")]
mod ohos;

mod cross_platform;
mod ffi_models;
```

### 构建配置

```toml
[features]
default = []
android = ["jni"]
ios = []
ohos = ["napi-ohos", "napi-derive-ohos"]
```

## Android平台

### JNI接口

Android平台使用JNI (Java Native Interface) 提供原生接口：

```rust
#[cfg(feature = "android")]
use jni::JNIEnv;
#[cfg(feature = "android")]
use jni::objects::{JClass, JString, JObject};
#[cfg(feature = "android")]
use jni::sys::{jstring, jlong, jboolean};

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn Java_com_example_resource_ResourceManager_initResourceManager(
    env: JNIEnv,
    _class: JClass,
    platform: JString,
    app_version: JString,
    environment: JString,
    is_test_mode: jboolean,
    root_path: JString,
) -> jstring {
    let platform_str: String = env.get_string(platform).unwrap().into();
    let app_version_str: String = env.get_string(app_version).unwrap().into();
    let environment_str: String = env.get_string(environment).unwrap().into();
    let root_path_str: String = env.get_string(root_path).unwrap().into();
    
    // 调用Rust核心逻辑
    let result = init_resource_manager_internal(
        platform_str,
        app_version_str,
        environment_str,
        is_test_mode != 0,
        root_path_str,
    );
    
    // 返回结果
    match result {
        Ok(msg) => env.new_string(msg).unwrap().into_inner(),
        Err(e) => env.new_string(format!("Error: {}", e)).unwrap().into_inner(),
    }
}

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn Java_com_example_resource_ResourceManager_searchResourceList(
    env: JNIEnv,
    _class: JClass,
    condition_json: JString,
) -> jstring {
    let condition_str: String = env.get_string(condition_json).unwrap().into();
    
    // 解析查询条件
    let condition: NormalResourceCondition = match serde_json::from_str(&condition_str) {
        Ok(c) => c,
        Err(e) => {
            let error_msg = format!("Invalid condition JSON: {}", e);
            return env.new_string(error_msg).unwrap().into_inner();
        }
    };
    
    // 执行搜索
    let rt = tokio::runtime::Runtime::new().unwrap();
    let result = rt.block_on(async {
        search_resource_list_internal(condition).await
    });
    
    // 序列化结果
    let json_result = match result {
        Ok(resources) => serde_json::to_string(&resources).unwrap_or_default(),
        Err(e) => format!("{{\"error\": \"{}\"}}", e),
    };
    
    env.new_string(json_result).unwrap().into_inner()
}

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn Java_com_example_resource_ResourceManager_installResource(
    env: JNIEnv,
    _class: JClass,
    resource_json: JString,
    callback: JObject,
) -> jstring {
    let resource_str: String = env.get_string(resource_json).unwrap().into();
    
    // 解析资源信息
    let resource_info: ResourceInfo = match serde_json::from_str(&resource_str) {
        Ok(r) => r,
        Err(e) => {
            let error_msg = format!("Invalid resource JSON: {}", e);
            return env.new_string(error_msg).unwrap().into_inner();
        }
    };
    
    // 创建回调包装器
    let callback_wrapper = AndroidCallbackWrapper::new(env, callback);
    
    // 执行安装
    let result = install_resource_internal(resource_info, Some(Box::new(callback_wrapper)));
    
    match result {
        Ok(task_id) => env.new_string(task_id).unwrap().into_inner(),
        Err(e) => env.new_string(format!("Error: {}", e)).unwrap().into_inner(),
    }
}
```

### Android回调包装器

```rust
#[cfg(feature = "android")]
struct AndroidCallbackWrapper {
    callback_ref: GlobalRef,
    jvm: JavaVM,
}

#[cfg(feature = "android")]
impl AndroidCallbackWrapper {
    fn new(env: JNIEnv, callback: JObject) -> Self {
        let jvm = env.get_java_vm().unwrap();
        let callback_ref = env.new_global_ref(callback).unwrap();
        
        Self {
            callback_ref,
            jvm,
        }
    }
}

#[cfg(feature = "android")]
impl ResourceCallback for AndroidCallbackWrapper {
    fn unique_id(&self) -> String {
        format!("android_callback_{:p}", self.callback_ref.as_obj())
    }
    
    fn on_progress_changed(&self, resource: &ResourceInfo, progress: usize) {
        if let Ok(env) = self.jvm.attach_current_thread() {
            let resource_json = serde_json::to_string(resource).unwrap_or_default();
            let resource_jstring = env.new_string(resource_json).unwrap();
            
            let _ = env.call_method(
                self.callback_ref.as_obj(),
                "onProgressChanged",
                "(Ljava/lang/String;I)V",
                &[resource_jstring.into(), (progress as i32).into()],
            );
        }
    }
    
    fn on_result(&self, resource: &ResourceInfo, error_msg: Option<String>) {
        if let Ok(env) = self.jvm.attach_current_thread() {
            let resource_json = serde_json::to_string(resource).unwrap_or_default();
            let resource_jstring = env.new_string(resource_json).unwrap();
            
            let error_jstring = match error_msg {
                Some(msg) => env.new_string(msg).unwrap(),
                None => env.new_string("").unwrap(),
            };
            
            let _ = env.call_method(
                self.callback_ref.as_obj(),
                "onResult",
                "(Ljava/lang/String;Ljava/lang/String;)V",
                &[resource_jstring.into(), error_jstring.into()],
            );
        }
    }
}
```

## iOS平台

### C接口

iOS平台使用C接口和Objective-C桥接：

```rust
#[cfg(feature = "ios")]
use std::ffi::{CStr, CString};
#[cfg(feature = "ios")]
use std::os::raw::c_char;

#[cfg(feature = "ios")]
#[no_mangle]
pub extern "C" fn resource_init_manager(
    platform: *const c_char,
    app_version: *const c_char,
    environment: *const c_char,
    is_test_mode: bool,
    root_path: *const c_char,
) -> *mut c_char {
    let platform_str = unsafe { CStr::from_ptr(platform).to_str().unwrap() };
    let app_version_str = unsafe { CStr::from_ptr(app_version).to_str().unwrap() };
    let environment_str = unsafe { CStr::from_ptr(environment).to_str().unwrap() };
    let root_path_str = unsafe { CStr::from_ptr(root_path).to_str().unwrap() };
    
    // 调用核心逻辑
    let result = init_resource_manager_internal(
        platform_str.to_string(),
        app_version_str.to_string(),
        environment_str.to_string(),
        is_test_mode,
        root_path_str.to_string(),
    );
    
    // 返回C字符串
    match result {
        Ok(msg) => CString::new(msg).unwrap().into_raw(),
        Err(e) => CString::new(format!("Error: {}", e)).unwrap().into_raw(),
    }
}

#[cfg(feature = "ios")]
#[no_mangle]
pub extern "C" fn resource_search_list(
    condition_json: *const c_char,
    callback: extern "C" fn(*const c_char, *const c_char),
) {
    let condition_str = unsafe { CStr::from_ptr(condition_json).to_str().unwrap() };
    
    // 解析查询条件
    let condition: NormalResourceCondition = match serde_json::from_str(condition_str) {
        Ok(c) => c,
        Err(e) => {
            let error_msg = CString::new(format!("Invalid condition JSON: {}", e)).unwrap();
            callback(std::ptr::null(), error_msg.as_ptr());
            return;
        }
    };
    
    // 异步执行搜索
    tokio::spawn(async move {
        let result = search_resource_list_internal(condition).await;
        
        match result {
            Ok(resources) => {
                let json_result = serde_json::to_string(&resources).unwrap_or_default();
                let result_cstring = CString::new(json_result).unwrap();
                callback(result_cstring.as_ptr(), std::ptr::null());
            }
            Err(e) => {
                let error_msg = CString::new(format!("{}", e)).unwrap();
                callback(std::ptr::null(), error_msg.as_ptr());
            }
        }
    });
}

#[cfg(feature = "ios")]
#[no_mangle]
pub extern "C" fn resource_free_string(ptr: *mut c_char) {
    if !ptr.is_null() {
        unsafe {
            CString::from_raw(ptr);
        }
    }
}
```

### iOS回调管理

```rust
#[cfg(feature = "ios")]
type IOSProgressCallback = extern "C" fn(*const c_char, i32);
#[cfg(feature = "ios")]
type IOSResultCallback = extern "C" fn(*const c_char, *const c_char);

#[cfg(feature = "ios")]
struct IOSCallbackWrapper {
    progress_callback: Option<IOSProgressCallback>,
    result_callback: Option<IOSResultCallback>,
    unique_id: String,
}

#[cfg(feature = "ios")]
impl IOSCallbackWrapper {
    fn new(
        progress_callback: Option<IOSProgressCallback>,
        result_callback: Option<IOSResultCallback>,
    ) -> Self {
        Self {
            progress_callback,
            result_callback,
            unique_id: uuid::Uuid::new_v4().to_string(),
        }
    }
}

#[cfg(feature = "ios")]
impl ResourceCallback for IOSCallbackWrapper {
    fn unique_id(&self) -> String {
        self.unique_id.clone()
    }
    
    fn on_progress_changed(&self, resource: &ResourceInfo, progress: usize) {
        if let Some(callback) = self.progress_callback {
            let resource_json = serde_json::to_string(resource).unwrap_or_default();
            let resource_cstring = CString::new(resource_json).unwrap();
            callback(resource_cstring.as_ptr(), progress as i32);
        }
    }
    
    fn on_result(&self, resource: &ResourceInfo, error_msg: Option<String>) {
        if let Some(callback) = self.result_callback {
            let resource_json = serde_json::to_string(resource).unwrap_or_default();
            let resource_cstring = CString::new(resource_json).unwrap();
            
            match error_msg {
                Some(msg) => {
                    let error_cstring = CString::new(msg).unwrap();
                    callback(resource_cstring.as_ptr(), error_cstring.as_ptr());
                }
                None => {
                    callback(resource_cstring.as_ptr(), std::ptr::null());
                }
            }
        }
    }
}
```

## HarmonyOS平台

### NAPI接口

HarmonyOS平台使用Node-API (NAPI) 接口：

```rust
#[cfg(feature = "ohos")]
use napi_derive_ohos::napi;
#[cfg(feature = "ohos")]
use napi_ohos::{Env, Error, JsFunction, JsObject, JsString, Result, Status, CallContext};

#[cfg(feature = "ohos")]
#[napi]
pub fn init_resource_manager(
    platform: String,
    app_version: String,
    environment: String,
    is_test_mode: bool,
    root_path: String,
) -> Result<String> {
    // 初始化资源管理器
    match init_resource_manager_internal(platform, app_version, environment, is_test_mode, root_path) {
        Ok(result) => Ok(result),
        Err(e) => Err(Error::new(Status::GenericFailure, e.to_string())),
    }
}

#[cfg(feature = "ohos")]
#[napi]
pub async fn search_resource_list(condition_json: String) -> Result<String> {
    // 解析查询条件
    let condition: NormalResourceCondition = serde_json::from_str(&condition_json)
        .map_err(|e| Error::new(Status::InvalidArg, format!("Invalid condition JSON: {}", e)))?;
    
    // 执行搜索
    let resources = search_resource_list_internal(condition).await
        .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))?;
    
    // 序列化结果
    serde_json::to_string(&resources)
        .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))
}

#[cfg(feature = "ohos")]
#[napi]
pub fn install_resource(
    resource_json: String,
    progress_callback: Option<JsFunction>,
    result_callback: Option<JsFunction>,
) -> Result<String> {
    // 解析资源信息
    let resource_info: ResourceInfo = serde_json::from_str(&resource_json)
        .map_err(|e| Error::new(Status::InvalidArg, format!("Invalid resource JSON: {}", e)))?;
    
    // 创建回调包装器
    let callback_wrapper = if progress_callback.is_some() || result_callback.is_some() {
        Some(Box::new(OHOSCallbackWrapper::new(progress_callback, result_callback)) as Box<dyn ResourceCallback>)
    } else {
        None
    };
    
    // 执行安装
    install_resource_internal(resource_info, callback_wrapper)
        .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))
}
```

### HarmonyOS对象传递

```rust
#[cfg(feature = "ohos")]
#[napi(object)]
pub struct ResourceInfoJS {
    pub id: i64,
    pub name: String,
    pub version: String,
    pub resource_type: String,
    pub download_url: String,
    pub hash: String,
    pub is_preset: bool,
    pub is_active: bool,
}

#[cfg(feature = "ohos")]
#[napi(object)]
pub struct ResourceConditionJS {
    pub resource_type: String,
    pub resource_name: Option<String>,
    pub app_version: String,
    pub from_func: String,
}

#[cfg(feature = "ohos")]
impl From<ResourceInfo> for ResourceInfoJS {
    fn from(info: ResourceInfo) -> Self {
        Self {
            id: info.id,
            name: info.name,
            version: info.version,
            resource_type: info.resource_type.to_string(),
            download_url: info.download_url,
            hash: info.hash,
            is_preset: info.is_preset,
            is_active: info.is_active,
        }
    }
}
```

## FlatBuffers跨平台序列化

### Schema定义

```flatbuffers
// resource.fbs
namespace Resource;

table ResourceInfo {
    id: long;
    name: string;
    version: string;
    resource_type: string;
    download_url: string;
    hash: string;
    is_preset: bool;
    is_active: bool;
    create_time: long;
    update_time: long;
}

table ResourceList {
    resources: [ResourceInfo];
}

root_type ResourceList;
```

### 序列化实现

```rust
use flatbuffers::{FlatBufferBuilder, WIPOffset};

pub fn serialize_resource_list(resources: &[ResourceInfo]) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();
    
    // 构建资源列表
    let resource_offsets: Vec<WIPOffset<resource_generated::ResourceInfo>> = resources
        .iter()
        .map(|resource| serialize_resource_info(&mut builder, resource))
        .collect();
    
    let resources_vector = builder.create_vector(&resource_offsets);
    
    // 创建根对象
    let resource_list = resource_generated::ResourceList::create(&mut builder, &resource_generated::ResourceListArgs {
        resources: Some(resources_vector),
    });
    
    builder.finish(resource_list, None);
    builder.finished_data().to_vec()
}

fn serialize_resource_info(
    builder: &mut FlatBufferBuilder,
    resource: &ResourceInfo,
) -> WIPOffset<resource_generated::ResourceInfo> {
    // 序列化字符串
    let name = builder.create_string(&resource.name);
    let version = builder.create_string(&resource.version);
    let resource_type = builder.create_string(&resource.resource_type.to_string());
    let download_url = builder.create_string(&resource.download_url);
    let hash = builder.create_string(&resource.hash);
    
    resource_generated::ResourceInfo::create(builder, &resource_generated::ResourceInfoArgs {
        id: resource.id,
        name: Some(name),
        version: Some(version),
        resource_type: Some(resource_type),
        download_url: Some(download_url),
        hash: Some(hash),
        is_preset: resource.is_preset,
        is_active: resource.is_active,
        create_time: resource.create_time,
        update_time: resource.update_time,
    })
}
```

## 跨平台接口统一

### 核心逻辑抽象

```rust
// 平台无关的核心逻辑
fn init_resource_manager_internal(
    platform: String,
    app_version: String,
    environment: String,
    is_test_mode: bool,
    root_path: String,
) -> Result<String, String> {
    let platform_enum = ResourcePlatform::from_str(&platform)
        .ok_or("Invalid platform")?;
    let environment_enum = ResourceRequestEnvironment::from_str(&environment)
        .ok_or("Invalid environment")?;
    
    // 创建数据库实例
    let database_path = format!("{}/resource.db", root_path);
    let database = Box::new(ResourceDatabaseImpl::new(&database_path)
        .map_err(|e| format!("Database initialization failed: {}", e))?);
    
    // 初始化资源管理器
    ResourceManager::get_instance().init(
        platform_enum,
        app_version,
        environment_enum,
        is_test_mode,
        root_path,
        database,
    );
    
    Ok("Resource manager initialized successfully".to_string())
}

async fn search_resource_list_internal(
    condition: NormalResourceCondition,
) -> Result<Vec<ResourceInfo>, String> {
    let resource = UPResource::new();
    
    resource.search_normal_resource_list(condition, None)
        .await
        .map_err(|e| e.to_string())
}

fn install_resource_internal(
    resource_info: ResourceInfo,
    callback: Option<Box<dyn ResourceCallback>>,
) -> Result<String, String> {
    let resource = UPResource::new();
    
    resource.install(resource_info, callback)
        .map_err(|e| e.to_string())
}
```

## 错误处理

### 统一错误类型

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FFIError {
    pub code: i32,
    pub message: String,
    pub details: Option<String>,
}

impl From<ResourceError> for FFIError {
    fn from(error: ResourceError) -> Self {
        match error {
            ResourceError::ResourceNotFound(msg) => FFIError {
                code: 1001,
                message: "Resource not found".to_string(),
                details: Some(msg),
            },
            ResourceError::NetworkError(msg) => FFIError {
                code: 2001,
                message: "Network error".to_string(),
                details: Some(msg),
            },
            ResourceError::DatabaseError(e) => FFIError {
                code: 3001,
                message: "Database error".to_string(),
                details: Some(e.to_string()),
            },
            _ => FFIError {
                code: 9999,
                message: "Unknown error".to_string(),
                details: Some(error.to_string()),
            },
        }
    }
}
```

## 最佳实践

### 1. 内存安全

```rust
// 确保跨FFI边界的内存安全
#[cfg(feature = "ios")]
pub fn safe_string_to_c_char(s: String) -> *mut c_char {
    match CString::new(s) {
        Ok(c_string) => c_string.into_raw(),
        Err(_) => std::ptr::null_mut(),
    }
}

#[cfg(feature = "ios")]
pub unsafe fn safe_c_char_to_string(ptr: *const c_char) -> Option<String> {
    if ptr.is_null() {
        return None;
    }
    
    CStr::from_ptr(ptr).to_str().ok().map(|s| s.to_string())
}
```

### 2. 异步处理

```rust
// 跨平台异步接口
#[cfg(feature = "ohos")]
#[napi]
pub async fn async_operation(param: String) -> Result<String> {
    // HarmonyOS支持原生async/await
    let result = perform_async_operation(param).await;
    Ok(result)
}

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn async_operation_android(
    param: *const c_char,
    callback: extern "C" fn(*const c_char),
) {
    // Android使用回调处理异步
    let param_str = unsafe { CStr::from_ptr(param).to_str().unwrap().to_string() };
    
    tokio::spawn(async move {
        let result = perform_async_operation(param_str).await;
        let c_result = CString::new(result).unwrap();
        callback(c_result.as_ptr());
    });
}
```

### 3. 版本兼容

```rust
// API版本管理
pub const API_VERSION: &str = "1.0.0";

#[cfg(any(feature = "android", feature = "ios", feature = "ohos"))]
pub fn get_api_version() -> String {
    API_VERSION.to_string()
}

// 向后兼容的接口
#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn Java_com_example_resource_ResourceManager_searchResourceListV1(
    env: JNIEnv,
    class: JClass,
    resource_type: JString,
) -> jstring {
    // 旧版本接口实现
    // 转换为新版本调用
    let condition = build_legacy_condition(env, resource_type);
    Java_com_example_resource_ResourceManager_searchResourceList(env, class, condition)
}
```
