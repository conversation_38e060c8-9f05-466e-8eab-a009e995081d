# Models模块

Models模块位于`src/models`目录下，定义了Rust Resource库中使用的各种数据结构和模型，是整个库的数据基础。该模块包含资源信息、查询条件、回调接口等核心数据模型。

## 模块结构

```
src/models/
├── resource_info.rs         # 资源信息模型
├── resource_type.rs         # 资源类型定义
├── query_info.rs           # 查询信息模型
├── callback.rs             # 回调接口定义
├── platform.rs             # 平台定义
├── environment.rs          # 环境定义
├── error.rs                # 错误模型
└── mod.rs
```

## 核心数据模型

### 1. ResourceInfo (资源信息)

`ResourceInfo`是资源的核心信息模型，包含资源的所有基本属性和状态信息。

#### 数据结构

```rust
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ResourceInfo {
    /// 资源ID（数据库主键）
    pub id: i64,
    /// 资源名称
    pub name: String,
    /// 资源版本号
    pub version: String,
    /// 资源类型
    pub resource_type: ResourceType,
    /// 创建时间（毫秒时间戳）
    pub create_time: i64,
    /// 更新时间（毫秒时间戳）
    pub update_time: i64,
    /// 下载URL
    pub download_url: String,
    /// 本地存储路径
    pub path: Option<String>,
    /// 索引文件路径
    pub index_path: Option<String>,
    /// 是否为预置资源
    pub is_preset: bool,
    /// 是否激活状态
    pub is_active: bool,
    /// 是否为服务器最新版本
    pub is_server_latest: bool,
    /// 是否隐藏状态栏
    pub hide_status_bar: bool,
    /// 是否强制升级
    pub is_force_upgrade: bool,
    /// 资源规则JSON字符串
    pub rules_json: Option<String>,
    /// 远程页面URL
    pub remote_page_url: Option<String>,
    /// 资源状态
    pub status: ResourceStatus,
    /// 资源文件哈希值
    pub hash: String,
}
```

#### 辅助方法

```rust
impl ResourceInfo {
    /// 创建新的资源信息
    pub fn new(name: String, version: String, resource_type: ResourceType) -> Self {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as i64;
        
        Self {
            id: 0,
            name,
            version,
            resource_type,
            create_time: current_time,
            update_time: current_time,
            status: ResourceStatus::Published,
            ..Default::default()
        }
    }
    
    /// 检查是否需要解压
    pub fn is_need_unzip(&self) -> bool {
        self.download_url.ends_with(".zip") || 
        self.download_url.ends_with(".tar.gz")
    }
    
    /// 获取资源文件扩展名
    pub fn get_file_extension(&self) -> Option<&str> {
        self.download_url
            .split('.')
            .last()
            .filter(|ext| !ext.is_empty())
    }
    
    /// 检查是否为有效资源
    pub fn is_valid(&self) -> bool {
        !self.name.is_empty() && 
        !self.version.is_empty() && 
        !self.download_url.is_empty() &&
        !self.hash.is_empty()
    }
    
    /// 生成唯一标识符
    pub fn generate_unique_id(&self) -> String {
        format!("{}:{}:{}", self.resource_type, self.name, self.version)
    }
}
```

### 2. ResourceType (资源类型)

`ResourceType`定义了系统支持的各种资源类型。

#### 类型定义

```rust
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ResourceType {
    /// 设备资源包
    Device,
    /// 主题资源
    Theme,
    /// 配置资源
    Config,
    /// 语言包资源
    Language,
    /// 插件资源
    Plugin,
    /// 固件资源
    Firmware,
    /// 其他类型
    Other(String),
}

impl ResourceType {
    /// 从字符串转换
    pub fn from_str(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "device" => ResourceType::Device,
            "theme" => ResourceType::Theme,
            "config" => ResourceType::Config,
            "language" => ResourceType::Language,
            "plugin" => ResourceType::Plugin,
            "firmware" => ResourceType::Firmware,
            _ => ResourceType::Other(s.to_string()),
        }
    }
    
    /// 转换为字符串
    pub fn to_string(&self) -> String {
        match self {
            ResourceType::Device => "device".to_string(),
            ResourceType::Theme => "theme".to_string(),
            ResourceType::Config => "config".to_string(),
            ResourceType::Language => "language".to_string(),
            ResourceType::Plugin => "plugin".to_string(),
            ResourceType::Firmware => "firmware".to_string(),
            ResourceType::Other(s) => s.clone(),
        }
    }
    
    /// 获取默认存储目录
    pub fn get_default_directory(&self) -> &'static str {
        match self {
            ResourceType::Device => "devices",
            ResourceType::Theme => "themes",
            ResourceType::Config => "configs",
            ResourceType::Language => "languages",
            ResourceType::Plugin => "plugins",
            ResourceType::Firmware => "firmware",
            ResourceType::Other(_) => "others",
        }
    }
}
```

### 3. ResourceStatus (资源状态)

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceStatus {
    /// 已发布
    Published = 0,
    /// 已下架
    Offline = 1,
    /// 测试中
    Testing = 2,
    /// 已废弃
    Deprecated = 3,
}

impl ResourceStatus {
    pub fn is_available(&self) -> bool {
        matches!(self, ResourceStatus::Published | ResourceStatus::Testing)
    }
}
```

### 4. 查询条件模型

#### NormalResourceCondition (普通资源查询条件)

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NormalResourceCondition {
    /// 资源类型
    pub resource_type: ResourceType,
    /// 资源名称（可选）
    pub resource_name: Option<String>,
    /// 应用版本
    pub app_version: String,
    /// 来源函数
    pub from_func: String,
    /// 其他查询条件
    pub other_conditions: HashMap<String, String>,
}

impl NormalResourceCondition {
    /// 合并查询条件为字符串
    pub fn combine(&self) -> String {
        let mut conditions = vec![
            format!("resource_type:{}", self.resource_type.to_string()),
            format!("app_version:{}", self.app_version),
            format!("from_func:{}", self.from_func),
        ];
        
        if let Some(name) = &self.resource_name {
            conditions.push(format!("resource_name:{}", name));
        }
        
        for (key, value) in &self.other_conditions {
            conditions.push(format!("{}:{}", key, value));
        }
        
        conditions.sort();
        conditions.join("|")
    }
}
```

#### DeviceResourceCondition (设备资源查询条件)

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceResourceCondition {
    /// 设备类型ID
    pub type_id: String,
    /// 设备型号
    pub model: String,
    /// 应用版本
    pub app_version: String,
    /// 来源函数
    pub from_func: String,
    /// 设备版本（可选）
    pub device_version: Option<String>,
    /// 制造商（可选）
    pub manufacturer: Option<String>,
}

impl DeviceResourceCondition {
    pub fn combine(&self) -> String {
        let mut conditions = vec![
            format!("type_id:{}", self.type_id),
            format!("model:{}", self.model),
            format!("app_version:{}", self.app_version),
            format!("from_func:{}", self.from_func),
        ];
        
        if let Some(version) = &self.device_version {
            conditions.push(format!("device_version:{}", version));
        }
        
        if let Some(manufacturer) = &self.manufacturer {
            conditions.push(format!("manufacturer:{}", manufacturer));
        }
        
        conditions.sort();
        conditions.join("|")
    }
}
```

### 5. 平台和环境模型

#### ResourcePlatform (资源平台)

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourcePlatform {
    Android,
    iOS,
    HarmonyOS,
    Windows,
    MacOS,
    Linux,
}

impl ResourcePlatform {
    pub fn to_string(&self) -> String {
        match self {
            ResourcePlatform::Android => "android".to_string(),
            ResourcePlatform::iOS => "ios".to_string(),
            ResourcePlatform::HarmonyOS => "harmonyos".to_string(),
            ResourcePlatform::Windows => "windows".to_string(),
            ResourcePlatform::MacOS => "macos".to_string(),
            ResourcePlatform::Linux => "linux".to_string(),
        }
    }
    
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "android" => Some(ResourcePlatform::Android),
            "ios" => Some(ResourcePlatform::iOS),
            "harmonyos" => Some(ResourcePlatform::HarmonyOS),
            "windows" => Some(ResourcePlatform::Windows),
            "macos" => Some(ResourcePlatform::MacOS),
            "linux" => Some(ResourcePlatform::Linux),
            _ => None,
        }
    }
}
```

#### ResourceRequestEnvironment (请求环境)

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceRequestEnvironment {
    /// 生产环境
    Production,
    /// 测试环境
    Test,
    /// 开发环境
    Development,
}

impl ResourceRequestEnvironment {
    pub fn get_base_url(&self) -> &'static str {
        match self {
            ResourceRequestEnvironment::Production => "https://api.example.com",
            ResourceRequestEnvironment::Test => "https://test-api.example.com",
            ResourceRequestEnvironment::Development => "https://dev-api.example.com",
        }
    }
}
```

### 6. 回调接口模型

#### ResourceCallback (资源操作回调)

```rust
pub trait ResourceCallback: Send + Sync {
    /// 获取回调的唯一标识
    fn unique_id(&self) -> String;
    
    /// 进度变化回调
    fn on_progress_changed(&self, resource: &ResourceInfo, progress: usize);
    
    /// 操作结果回调
    fn on_result(&self, resource: &ResourceInfo, error_msg: Option<String>);
}

/// 资源列表回调
pub trait ResourceListCallback: Send + Sync {
    /// 列表结果回调
    fn on_list_result(&self, resources: &[ResourceInfo], error_msg: Option<String>);
}
```

#### 回调实现示例

```rust
pub struct SimpleResourceCallback {
    id: String,
}

impl SimpleResourceCallback {
    pub fn new(id: String) -> Self {
        Self { id }
    }
}

impl ResourceCallback for SimpleResourceCallback {
    fn unique_id(&self) -> String {
        self.id.clone()
    }
    
    fn on_progress_changed(&self, resource: &ResourceInfo, progress: usize) {
        println!("资源 {} 进度: {}%", resource.name, progress);
    }
    
    fn on_result(&self, resource: &ResourceInfo, error_msg: Option<String>) {
        match error_msg {
            Some(err) => println!("资源 {} 操作失败: {}", resource.name, err),
            None => println!("资源 {} 操作成功", resource.name),
        }
    }
}
```

### 7. 下载优先级

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResourceDownloadPriority {
    /// 低优先级
    Low = 0,
    /// 普通优先级
    Normal = 1,
    /// 高优先级
    High = 2,
    /// 紧急优先级
    Urgent = 3,
}

impl ResourceDownloadPriority {
    pub fn from_i32(value: i32) -> Self {
        match value {
            0 => ResourceDownloadPriority::Low,
            1 => ResourceDownloadPriority::Normal,
            2 => ResourceDownloadPriority::High,
            3 => ResourceDownloadPriority::Urgent,
            _ => ResourceDownloadPriority::Normal,
        }
    }
    
    pub fn to_i32(&self) -> i32 {
        *self as i32
    }
}
```

### 8. 服务器响应模型

#### ServerResourceInfo (服务器资源信息)

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerResourceInfo {
    /// 资源名称
    pub name: String,
    /// 资源版本
    pub version: String,
    /// 资源类型
    pub resource_type: String,
    /// 下载URL
    pub download_url: String,
    /// 资源哈希值
    pub hash: String,
    /// 文件大小（字节）
    pub file_size: Option<u64>,
    /// 是否隐藏状态栏
    pub hide_status_bar: Option<bool>,
    /// 是否强制升级
    pub is_force_upgrade: Option<bool>,
    /// 资源规则JSON
    pub rules_json: Option<String>,
    /// 远程页面URL
    pub remote_page_url: Option<String>,
    /// 发布时间
    pub publish_time: Option<i64>,
    /// 描述信息
    pub description: Option<String>,
}
```

#### ApiResponse (API响应包装)

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    /// 响应代码（0表示成功）
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 响应数据
    pub data: Option<T>,
    /// 时间戳
    pub timestamp: Option<i64>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            code: 0,
            message: "Success".to_string(),
            data: Some(data),
            timestamp: Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_millis() as i64
            ),
        }
    }
    
    pub fn error(code: i32, message: String) -> Self {
        Self {
            code,
            message,
            data: None,
            timestamp: Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_millis() as i64
            ),
        }
    }
    
    pub fn is_success(&self) -> bool {
        self.code == 0
    }
}
```

## 数据验证

### 1. 资源信息验证

```rust
impl ResourceInfo {
    /// 验证资源信息的完整性
    pub fn validate(&self) -> Result<(), ValidationError> {
        if self.name.is_empty() {
            return Err(ValidationError::EmptyField("name".to_string()));
        }
        
        if self.version.is_empty() {
            return Err(ValidationError::EmptyField("version".to_string()));
        }
        
        if self.download_url.is_empty() {
            return Err(ValidationError::EmptyField("download_url".to_string()));
        }
        
        if !self.is_valid_url(&self.download_url) {
            return Err(ValidationError::InvalidUrl(self.download_url.clone()));
        }
        
        if !self.is_valid_hash(&self.hash) {
            return Err(ValidationError::InvalidHash(self.hash.clone()));
        }
        
        Ok(())
    }
    
    fn is_valid_url(&self, url: &str) -> bool {
        url.starts_with("http://") || url.starts_with("https://")
    }
    
    fn is_valid_hash(&self, hash: &str) -> bool {
        hash.len() == 32 && hash.chars().all(|c| c.is_ascii_hexdigit())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ValidationError {
    #[error("字段不能为空: {0}")]
    EmptyField(String),
    
    #[error("无效的URL: {0}")]
    InvalidUrl(String),
    
    #[error("无效的哈希值: {0}")]
    InvalidHash(String),
    
    #[error("无效的版本号: {0}")]
    InvalidVersion(String),
}
```

### 2. 查询条件验证

```rust
impl NormalResourceCondition {
    pub fn validate(&self) -> Result<(), ValidationError> {
        if self.app_version.is_empty() {
            return Err(ValidationError::EmptyField("app_version".to_string()));
        }
        
        if self.from_func.is_empty() {
            return Err(ValidationError::EmptyField("from_func".to_string()));
        }
        
        Ok(())
    }
}
```

## 序列化支持

### 1. JSON序列化

```rust
// 自定义序列化格式
impl Serialize for ResourceType {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}

impl<'de> Deserialize<'de> for ResourceType {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Ok(ResourceType::from_str(&s))
    }
}
```

### 2. 数据库序列化

```rust
// 与数据库记录的转换
impl From<ResourceRecord> for ResourceInfo {
    fn from(record: ResourceRecord) -> Self {
        Self {
            id: record.id,
            name: record.name,
            version: record.version,
            resource_type: ResourceType::from_str(&record.resource_type),
            create_time: record.create_time,
            update_time: record.update_time,
            download_url: record.download_url,
            path: record.path,
            index_path: record.index_path,
            is_preset: record.is_preset,
            is_active: record.is_active,
            is_server_latest: record.is_server_latest,
            hide_status_bar: record.hide_status_bar,
            is_force_upgrade: record.is_force_upgrade,
            rules_json: record.rules_json,
            remote_page_url: record.remote_page_url,
            status: ResourceStatus::from_i32(record.status),
            hash: record.hash,
        }
    }
}
```

## 最佳实践

### 1. 模型创建

```rust
// 使用构建器模式创建复杂对象
let resource = ResourceInfo::new(
    "smart_light".to_string(),
    "1.0.0".to_string(),
    ResourceType::Device
);

// 验证数据完整性
resource.validate()?;
```

### 2. 类型安全

```rust
// 使用强类型避免错误
fn process_device_resource(resource: &ResourceInfo) -> Result<(), ProcessError> {
    match resource.resource_type {
        ResourceType::Device => {
            // 处理设备资源
            Ok(())
        }
        _ => Err(ProcessError::UnsupportedResourceType),
    }
}
```

### 3. 错误处理

```rust
// 优雅的错误处理
match resource.validate() {
    Ok(_) => {
        // 处理有效资源
    }
    Err(ValidationError::EmptyField(field)) => {
        log::error!("资源字段 {} 不能为空", field);
    }
    Err(e) => {
        log::error!("资源验证失败: {:?}", e);
    }
}
```

### 4. 性能优化

```rust
// 使用Arc共享不可变数据
pub type SharedResourceInfo = Arc<ResourceInfo>;

// 使用Cow避免不必要的克隆
use std::borrow::Cow;

pub fn get_resource_name(&self) -> Cow<str> {
    if self.name.is_empty() {
        Cow::Borrowed("Unknown Resource")
    } else {
        Cow::Borrowed(&self.name)
    }
}
```
