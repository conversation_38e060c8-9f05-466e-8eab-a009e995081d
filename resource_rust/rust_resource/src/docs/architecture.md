# Rust Resource 架构设计

## 概述

Rust Resource是一个跨平台的智能设备资源管理库，采用分层架构设计，基于管道模式实现资源的下载、验证、安装和管理。该架构旨在提供高效、可靠的资源管理服务，同时支持多平台部署和扩展。

## 架构原则

### 1. 分层解耦
- **接口层**: 提供统一的资源管理API，隐藏底层实现复杂性
- **处理层**: 实现具体的资源处理逻辑，支持插件化扩展
- **数据层**: 管理资源数据的存储、缓存和检索
- **基础层**: 提供通用工具和基础设施支持

### 2. 管道模式
- **阶段化处理**: 将复杂的资源处理分解为多个独立阶段
- **流水线执行**: 支持多个资源的并发处理
- **错误隔离**: 每个阶段独立处理错误，不影响其他阶段

### 3. 异步优先
- **非阻塞操作**: 所有I/O操作采用异步模式
- **并发控制**: 合理控制并发数量，避免资源竞争
- **事件驱动**: 基于事件的进度通知和状态更新

### 4. 数据一致性
- **事务支持**: 保证数据库操作的原子性
- **缓存同步**: 确保内存缓存与持久化数据的一致性
- **版本控制**: 完善的资源版本管理机制

## 整体架构

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        A1[Android App]
        A2[iOS App] 
        A3[HarmonyOS App]
    end
    
    subgraph "FFI接口层 (FFI Interface Layer)"
        F1[Android JNI]
        F2[iOS C Interface]
        F3[HarmonyOS NAPI]
        F4[FlatBuffers Serialization]
    end
    
    subgraph "API层 (API Layer)"
        API1[UPResource<br/>资源管理接口]
        API2[ResourceManager<br/>资源管理器]
        API3[ResourceCallback<br/>回调接口]
        API4[ResourceFilter<br/>资源过滤]
    end
    
    subgraph "Pipeline处理层 (Pipeline Processing Layer)"
        PIPE1[PipelineController<br/>管道控制器]
        PIPE2[PipelineSingle<br/>单资源管道]
        PIPE3[PipelineBatch<br/>批量管道]
        PIPE4[Stage System<br/>阶段系统]
    end
    
    subgraph "处理器层 (Handler Layer)"
        HAND1[DownloadHandler<br/>下载处理器]
        HAND2[InstallHandler<br/>安装处理器]
        HAND3[FileHandler<br/>文件处理器]
        HAND4[CleanHandler<br/>清理处理器]
        HAND5[ValidationHandler<br/>验证处理器]
    end
    
    subgraph "数据源层 (Data Source Layer)"
        DS1[ResourceDataSource<br/>资源数据源]
        DS2[ResourceDatabase<br/>资源数据库]
        DS3[ServerAPI<br/>服务器API]
        DS4[Cache Management<br/>缓存管理]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        INF1[Models<br/>数据模型]
        INF2[Utils<br/>工具库]
        INF3[Directory<br/>目录管理]
        INF4[EventBus<br/>事件总线]
        INF5[TaskManager<br/>任务管理]
    end
    
    A1 --> F1
    A2 --> F2
    A3 --> F3
    F1 --> API1
    F2 --> API1
    F3 --> API1
    F4 --> API1
    
    API1 --> API2
    API2 --> PIPE1
    API3 --> PIPE1
    API4 --> PIPE1
    
    PIPE1 --> PIPE2
    PIPE1 --> PIPE3
    PIPE2 --> PIPE4
    PIPE3 --> PIPE4
    
    PIPE4 --> HAND1
    PIPE4 --> HAND2
    PIPE4 --> HAND3
    PIPE4 --> HAND4
    PIPE4 --> HAND5
    
    HAND1 --> DS1
    HAND2 --> DS2
    HAND3 --> DS3
    HAND4 --> DS4
    
    DS1 --> INF1
    DS2 --> INF2
    DS3 --> INF3
    DS4 --> INF4
    PIPE1 --> INF5
```

## 核心组件架构

### 1. 资源管道架构

```mermaid
graph LR
    subgraph "管道控制"
        PC[PipelineController]
        WQ[等待队列]
        RQ[运行队列]
    end
    
    subgraph "管道类型"
        PS[PipelineSingle]
        PB[PipelineBatch]
        PP[PresetPipeline]
    end
    
    subgraph "处理阶段"
        DS[DownloadStage]
        VS[ValidateStage]
        ES[ExtractStage]
        TS[TransportStage]
        SS[ScanStage]
        US[UpdateStage]
        RS[RemoveStage]
    end
    
    PC --> WQ
    PC --> RQ
    WQ --> PS
    WQ --> PB
    WQ --> PP
    RQ --> PS
    RQ --> PB
    RQ --> PP
    PS --> DS
    PS --> VS
    PS --> ES
    PS --> TS
    PS --> SS
    PS --> US
    PB --> RS
```

### 2. 资源处理流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant API as UPResource
    participant PM as PipelineManager
    participant Pipeline as Pipeline
    participant Stage as Stage
    participant Handler as Handler
    participant DB as Database

    App->>API: install(resourceInfo)
    API->>PM: createPipeline()
    PM->>Pipeline: new(resourceInfo)
    Pipeline->>Pipeline: initStages()
    PM->>Pipeline: run()
    
    loop 执行各个阶段
        Pipeline->>Stage: execute()
        Stage->>Handler: process()
        Handler->>DB: updateStatus()
        Handler-->>Stage: result
        Stage-->>Pipeline: result
        Pipeline->>App: progressCallback()
    end
    
    Pipeline->>DB: updateFinalStatus()
    Pipeline->>App: resultCallback()
```

### 3. 数据流架构

```mermaid
graph TD
    subgraph "数据输入"
        Server[服务器API]
        Local[本地预置]
        Cache[本地缓存]
    end
    
    subgraph "数据处理"
        Parser[数据解析]
        Validator[数据验证]
        Transformer[数据转换]
    end
    
    subgraph "数据存储"
        SQLite[SQLite数据库]
        FileSystem[文件系统]
        Memory[内存缓存]
    end
    
    subgraph "数据消费"
        Pipeline[处理管道]
        Query[查询接口]
        Export[数据导出]
    end
    
    Server --> Parser
    Local --> Parser
    Cache --> Parser
    Parser --> Validator
    Validator --> Transformer
    Transformer --> SQLite
    Transformer --> FileSystem
    Transformer --> Memory
    SQLite --> Pipeline
    FileSystem --> Pipeline
    Memory --> Query
    Pipeline --> Export
```

## 设计模式应用

### 1. 管道模式 (Pipeline Pattern)
- **目的**: 将复杂的资源处理分解为多个独立阶段
- **实现**: PipelineController + Stage系统
- **优势**: 易于扩展、错误隔离、并发处理

### 2. 策略模式 (Strategy Pattern)
- **目的**: 支持不同的资源处理策略
- **实现**: Handler接口 + 具体实现类
- **应用**: 下载策略、验证策略、安装策略

### 3. 工厂模式 (Factory Pattern)
- **目的**: 根据资源类型创建相应的处理器
- **实现**: HandlerFactory + 具体Handler
- **扩展**: 支持注册自定义处理器

### 4. 观察者模式 (Observer Pattern)
- **目的**: 实现进度通知和状态更新
- **实现**: EventBus + Callback接口
- **应用**: 下载进度、安装状态、错误通知

### 5. 单例模式 (Singleton Pattern)
- **目的**: 确保全局唯一的管理器实例
- **实现**: ResourceManager单例
- **线程安全**: 使用OnceCell保证线程安全

## 并发与异步设计

### 1. 异步运行时
```rust
// 基于Tokio的异步运行时
tokio = { version = "1.0", features = ["rt-multi-thread", "macros", "sync"] }
```

### 2. 并发控制
```rust
// 限制最大并发管道数量
const MAX_PIPELINE_COUNT: usize = 5;

// 使用RwLock保护共享状态
running_waiting_lock: RwLock<()>
```

### 3. 任务调度
```rust
// 异步任务执行
get_task_manager().schedule_task(PipelineRunner::new(pipeline_id));
```

## 资源生命周期管理

### 1. 资源状态

```mermaid
stateDiagram-v2
    [*] --> Pending
    Pending --> Downloading: 开始下载
    Downloading --> Downloaded: 下载完成
    Downloading --> Failed: 下载失败
    Downloaded --> Validating: 开始验证
    Validating --> Validated: 验证通过
    Validating --> Failed: 验证失败
    Validated --> Installing: 开始安装
    Installing --> Installed: 安装完成
    Installing --> Failed: 安装失败
    Installed --> Active: 激活资源
    Active --> Inactive: 停用资源
    Inactive --> Active: 重新激活
    Active --> Uninstalling: 开始卸载
    Uninstalling --> Uninstalled: 卸载完成
    Failed --> Pending: 重试
    Uninstalled --> [*]
```

### 2. 生命周期管理

```rust
impl ResourceLifecycle {
    // 资源下载
    async fn download(&mut self) -> Result<()> {
        self.state = ResourceState::Downloading;
        // 下载逻辑
        self.state = ResourceState::Downloaded;
    }
    
    // 资源验证
    async fn validate(&mut self) -> Result<()> {
        self.state = ResourceState::Validating;
        // 验证逻辑
        self.state = ResourceState::Validated;
    }
    
    // 资源安装
    async fn install(&mut self) -> Result<()> {
        self.state = ResourceState::Installing;
        // 安装逻辑
        self.state = ResourceState::Installed;
    }
}
```

## 缓存策略

### 1. 多级缓存架构

```mermaid
graph TB
    subgraph "缓存层次"
        L1[内存缓存<br/>ResourceInfo]
        L2[数据库缓存<br/>SQLite]
        L3[文件系统缓存<br/>资源文件]
    end
    
    subgraph "缓存策略"
        LRU[LRU淘汰]
        TTL[TTL过期]
        Size[大小限制]
    end
    
    L1 --> LRU
    L2 --> TTL
    L3 --> Size
```

### 2. 缓存实现

```rust
// 内存缓存
pub struct ResourceCache {
    cache: DashMap<String, Arc<ResourceInfo>>,
    max_size: usize,
}

// 数据库缓存
pub struct DatabaseCache {
    connection_pool: r2d2::Pool<ConnectionManager<SqliteConnection>>,
}

// 文件缓存
pub struct FileCache {
    cache_dir: PathBuf,
    max_size: u64,
}
```

## 错误处理策略

### 1. 错误类型层次

```rust
#[derive(Debug, thiserror::Error)]
pub enum ResourceError {
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("文件错误: {0}")]
    FileError(#[from] SystemFileError),
    
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] DatabaseError),
    
    #[error("管道错误: {0}")]
    PipelineError(#[from] PipelineError),
    
    #[error("验证错误: {0}")]
    ValidationError(String),
}
```

### 2. 错误恢复机制

```rust
// 重试机制
async fn download_with_retry(url: &str, max_retries: u32) -> Result<()> {
    for attempt in 0..max_retries {
        match download(url).await {
            Ok(result) => return Ok(result),
            Err(e) if attempt < max_retries - 1 => {
                tokio::time::sleep(Duration::from_secs(2_u64.pow(attempt))).await;
                continue;
            }
            Err(e) => return Err(e),
        }
    }
}

// 降级策略
fn get_resource_with_fallback(name: &str) -> Option<ResourceInfo> {
    // 1. 尝试从缓存获取
    if let Some(resource) = cache.get(name) {
        return Some(resource);
    }
    
    // 2. 尝试从数据库获取
    if let Ok(resource) = database.get(name) {
        return Some(resource);
    }
    
    // 3. 返回默认资源
    get_default_resource(name)
}
```

## 性能优化

### 1. 下载优化
- **断点续传**: 支持下载中断后继续
- **并发下载**: 控制并发数量避免过载
- **压缩传输**: 支持gzip压缩减少传输量
- **CDN加速**: 支持多CDN节点选择

### 2. 存储优化
- **增量更新**: 只下载变更部分
- **压缩存储**: 本地资源压缩存储
- **清理策略**: 自动清理过期资源
- **空间管理**: 监控磁盘空间使用

### 3. 查询优化
- **索引优化**: 数据库索引优化
- **批量操作**: 减少数据库访问次数
- **连接池**: 数据库连接池管理
- **预加载**: 热点数据预加载

## 安全考虑

### 1. 数据安全
- **哈希验证**: MD5/SHA256校验资源完整性
- **签名验证**: 数字签名验证资源来源
- **加密传输**: HTTPS加密传输
- **权限控制**: 文件访问权限控制

### 2. 内存安全
- **Rust保证**: 编译时内存安全检查
- **资源管理**: RAII自动资源管理
- **并发安全**: Arc/Mutex线程安全

### 3. 输入验证
- **参数校验**: 严格的输入参数验证
- **路径检查**: 防止路径遍历攻击
- **大小限制**: 限制资源文件大小
- **格式验证**: 验证资源文件格式

## 扩展性设计

### 1. 插件机制
- **Handler扩展**: 支持自定义处理器
- **Stage扩展**: 支持自定义处理阶段
- **DataSource扩展**: 支持自定义数据源

### 2. 配置驱动
- **运行时配置**: 支持运行时配置更新
- **环境适配**: 支持不同环境配置
- **特性开关**: 条件编译控制特性

### 3. 平台扩展
- **FFI接口**: 新平台适配接口
- **序列化格式**: 标准化数据交换
- **构建系统**: 多平台构建支持
