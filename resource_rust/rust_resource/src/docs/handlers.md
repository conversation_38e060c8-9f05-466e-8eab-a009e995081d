# Handlers模块

Handlers模块位于`src/handlers`目录下，提供了资源管理的各种处理器实现。每个Handler负责特定的功能领域，如下载、文件操作、清理、安装等，采用策略模式设计，支持灵活的扩展和替换。

## 模块结构

```
src/handlers/
├── download_handler.rs           # 下载处理器
├── file_handler.rs              # 文件处理器
├── install_handler.rs           # 安装处理器
├── clean_handler.rs             # 清理处理器
├── request_handler.rs           # 请求处理器
├── relation_handler.rs          # 关系处理器
├── time_handler.rs              # 时间处理器
├── preset_resource_scan_handler.rs # 预置资源扫描处理器
└── mod.rs
```

## 核心组件

### 1. DownloadHandler (下载处理器)

`DownloadHandler`负责资源文件的网络下载，支持断点续传、进度监控和取消操作。

#### 接口定义

```rust
#[async_trait]
pub trait DownloadHandler: Any + Send + Sync {
    /// 下载资源
    async fn download(&self, resource_info: &ResourceInfo) -> Result<(), DownloadError>;
    
    /// 取消下载
    fn cancel(&self, resource_id: &str);
}
```

#### 实现特性

- **断点续传**: 支持下载中断后继续
- **进度监控**: 实时报告下载进度
- **并发控制**: 合理控制下载并发数
- **错误重试**: 网络异常时自动重试

#### 核心实现

```rust
pub struct ResourceDownloadHandler {
    cancel_flags: Arc<DashMap<String, bool>>,
}

impl ResourceDownloadHandler {
    /// 创建HTTP客户端，支持断点续传
    fn create_download_http_client(&self, start_byte: u64) -> Result<Client, DownloadError> {
        let mut headers = HeaderMap::new();
        if start_byte > 0 {
            headers.insert(
                RANGE,
                HeaderValue::from_str(&format!("bytes={}-", start_byte))?,
            );
        }
        
        Ok(Client::builder()
            .default_headers(headers)
            .timeout(Duration::from_secs(30))
            .build()?)
    }

    /// 执行下载，支持进度回调
    async fn download(
        &self,
        url: &str,
        file_path: &str,
        resource: &ResourceInfo,
    ) -> Result<(), DownloadError> {
        let mut downloaded_bytes = self.get_downloaded_bytes(file_path);
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(file_path)?;

        let client = self.create_download_http_client(downloaded_bytes)?;
        let mut response = client.get(url).send().await?;
        
        let total_bytes = downloaded_bytes + self.get_remain_bytes(&response)?;
        let task_id = PipelineSingle::generate_pipeline_id(resource);

        // 流式下载
        while let Some(chunk) = response.chunk().await? {
            // 检查取消标志
            if self.is_cancelled(&task_id) {
                return Err(DownloadError::Cancelled(task_id));
            }

            file.write_all(&chunk)?;
            downloaded_bytes += chunk.len() as u64;

            // 发送进度事件
            let progress = ((downloaded_bytes * 100) / total_bytes) as usize;
            ResourceManager::get_instance()
                .get_event_bus()
                .publish(
                    &PipelineSingle::get_event_name(resource.id),
                    ResourceEvent::InstallProgress(task_id.clone(), progress),
                );
        }

        Ok(())
    }
}
```

#### 下载流程

```mermaid
sequenceDiagram
    participant Stage as DownloadStage
    participant Handler as DownloadHandler
    participant Client as HttpClient
    participant File as FileSystem
    participant Event as EventBus

    Stage->>Handler: download(resource_info)
    Handler->>File: 检查已下载字节数
    File-->>Handler: downloaded_bytes
    Handler->>Client: 创建HTTP客户端(Range头)
    Handler->>Client: 发送GET请求
    Client-->>Handler: 响应流

    loop 流式下载
        Handler->>Handler: 检查取消标志
        Handler->>File: 写入数据块
        Handler->>Event: 发送进度事件
    end

    Handler-->>Stage: 下载完成
```

### 2. FileHandler (文件处理器)

`FileHandler`提供完整的文件系统操作功能，包括文件创建、删除、压缩解压等。

#### 接口定义

```rust
pub trait FileHandler: Any + Send + Sync {
    /// 判断文件是否存在
    fn exists(&self, path: &str) -> Result<bool, SystemFileError>;
    
    /// 创建文件
    fn create_file(&self, path: &str) -> Result<(), SystemFileError>;
    
    /// 创建目录
    fn create_directory(&self, path: &str) -> Result<(), SystemFileError>;
    
    /// 删除文件或目录
    fn delete_path(&self, path: &str) -> Result<(), SystemFileError>;
    
    /// 复制文件
    fn copy_file(&self, source: &str, target: &str) -> Result<(), SystemFileError>;
    
    /// 移动文件
    fn move_file(&self, source: &str, target: &str) -> Result<(), SystemFileError>;
    
    /// 解压ZIP文件
    fn unzip(&self, source_path: &str, target_path: &str) -> Result<bool, SystemFileError>;
    
    /// 计算文件MD5
    fn calculate_md5(&self, file_path: &str) -> Result<String, SystemFileError>;
    
    /// 获取文件大小
    fn get_file_size(&self, path: &str) -> Result<u64, SystemFileError>;
}
```

#### 核心功能实现

##### ZIP解压

```rust
impl FileHandler for ResourceFileHandler {
    fn unzip(&self, source_path: &str, target_path: &str) -> Result<bool, SystemFileError> {
        let file = File::open(Path::new(source_path))?;
        let mut archive = ZipArchive::new(file)?;
        
        // 遍历并解压文件
        for i in 0..archive.len() {
            let mut file = archive.by_index(i)?;
            let outpath = Path::new(target_path).join(file.name());
            
            if file.name().ends_with('/') {
                // 创建目录
                std::fs::create_dir_all(&outpath)?;
            } else {
                // 创建文件
                if let Some(p) = outpath.parent() {
                    if !p.exists() {
                        std::fs::create_dir_all(p)?;
                    }
                }
                let mut outfile = File::create(&outpath)?;
                std::io::copy(&mut file, &mut outfile)?;
            }
        }
        Ok(true)
    }
}
```

##### MD5计算

```rust
impl FileHandler for ResourceFileHandler {
    fn calculate_md5(&self, file_path: &str) -> Result<String, SystemFileError> {
        let mut file = File::open(file_path)?;
        let mut hasher = md5::Md5::new();
        let mut buffer = [0; 8192];
        
        loop {
            let bytes_read = file.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        Ok(format!("{:x}", hasher.finalize()))
    }
}
```

### 3. CleanHandler (清理处理器)

`CleanHandler`负责资源的清理和垃圾回收，支持选择性保留和批量清理。

#### 接口定义

```rust
#[async_trait]
pub trait CleanHandler: Any + Send + Sync {
    /// 检查是否正在清理
    fn is_cleaning(&self) -> bool;
    
    /// 清理资源
    async fn clean(
        &self,
        ignore_res: Vec<ResourceInfo>,
        delete_temp: bool,
    ) -> Result<bool, ResourceError>;
}
```

#### 清理策略

```rust
impl ResourceCleanHandler {
    /// 获取需要保留的资源列表
    fn get_inner_keep_resource_list(&self) -> Vec<ResourceInfo> {
        let mut keep_list = Vec::new();
        
        // 保留激活的资源
        if let Ok(active_resources) = self.get_active_resources() {
            keep_list.extend(active_resources);
        }
        
        // 保留最新的服务器资源
        if let Ok(latest_resources) = self.get_latest_server_resources() {
            keep_list.extend(latest_resources);
        }
        
        keep_list
    }

    /// 执行清理操作
    async fn try_clean_data(
        &self,
        keep_list: Vec<ResourceInfo>,
        delete_temp: bool,
    ) -> Result<bool, ResourceError> {
        let resource_manager = ResourceManager::get_instance();
        
        // 清理数据库
        let db_success = if !keep_list.is_empty() {
            self.delete_database_without_keep_list(&keep_list).is_ok()
        } else {
            resource_manager.get_repository().clean_all().is_ok()
        };

        if db_success {
            // 清理文件系统
            self.delete_dir_after_db_clean(&keep_list, delete_temp)
        } else {
            Err(ResourceError::Unknown("Database clean failed".to_string()))
        }
    }
}
```

#### 清理流程

```mermaid
sequenceDiagram
    participant API as UPResource
    participant Handler as CleanHandler
    participant DB as Database
    participant FS as FileSystem

    API->>Handler: clean(ignore_list, delete_temp)
    Handler->>Handler: 设置清理状态
    Handler->>Handler: 构建保留列表
    Handler->>DB: 删除非保留资源记录
    DB-->>Handler: 删除结果
    Handler->>FS: 清理文件目录
    FS-->>Handler: 清理结果
    Handler->>Handler: 重置清理状态
    Handler-->>API: 清理结果
```

### 4. InstallHandler (安装处理器)

`InstallHandler`负责资源的安装流程管理，创建和执行安装管道。

#### 接口定义

```rust
#[async_trait]
pub trait InstallHandler: Send + Sync {
    /// 安装资源
    fn install(&self, resource: &ResourceInfo) -> Result<String, ResourceError>;
}
```

#### 实现

```rust
impl InstallHandler for UPResourceInstallHandler {
    fn install(&self, resource: &ResourceInfo) -> Result<String, ResourceError> {
        let pipeline_id = PipelineSingle::generate_pipeline_id(resource);
        
        // 获取或创建管道
        let pipeline = PipelineController::get_instance()
            .get_pipeline(&pipeline_id)
            .ok_or_else(|| ResourceError::Unknown("Pipeline not found".to_string()))?;
        
        // 执行管道
        pipeline.run()?;
        
        Ok(pipeline_id)
    }
}
```

### 5. RequestHandler (请求处理器)

`RequestHandler`负责与服务器的API交互，获取资源列表和元数据。

#### 接口定义

```rust
#[async_trait]
pub trait RequestHandler: Any + Send + Sync {
    /// 请求普通资源列表
    async fn request_normal_resource_list(
        &self,
        resource_condition: NormalResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError>;
    
    /// 请求设备资源列表
    async fn request_device_resource_list(
        &self,
        resource_condition: DeviceResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError>;
}
```

#### 请求流程

```rust
impl RequestHandler for ResourceRequestHandler {
    async fn request_normal_resource_list(
        &self,
        resource_condition: NormalResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError> {
        // 1. 构建查询对象
        let mut query = ResourceQuery::default();
        query.from_func = resource_condition.from_func;
        query.app_version = resource_condition.app_version;
        query.condition = resource_condition.combine();
        
        // 2. 查询本地缓存
        let local_query_map = ResourceManager::get_instance()
            .get_repository()
            .search_query(&query.condition)?;
        
        // 3. 请求服务器数据
        let server_resources = self.request_from_server(&query).await?;
        
        // 4. 合并和去重
        let merged_resources = self.merge_resources(local_query_map, server_resources);
        
        // 5. 更新本地缓存
        self.update_local_cache(&merged_resources, &query).await?;
        
        Ok(merged_resources)
    }
}
```

### 6. RelationHandler (关系处理器)

`RelationHandler`管理资源之间的关系和查询记录。

#### 功能

- **资源关联**: 建立资源与查询的关联关系
- **查询记录**: 记录资源查询历史
- **数据同步**: 同步服务器和本地数据

#### 实现

```rust
impl RelationHandler for ResourceRelationHandler {
    fn relations(
        &self,
        resource_list: &Vec<ResourceInfo>,
        query_info: &ResourceQuery,
        is_preset: bool,
    ) -> Result<Vec<ResourceInfo>, ResourceError> {
        let mut result_resources = Vec::new();
        
        for resource in resource_list {
            // 插入或更新资源记录
            let resource_id = self.insert_or_update_resource_info(resource, is_preset)?;
            
            // 建立查询关联
            self.insert_resource_query(query_info, resource_id)?;
            
            // 更新资源ID
            let mut updated_resource = resource.clone();
            updated_resource.id = resource_id;
            result_resources.push(updated_resource);
        }
        
        Ok(result_resources)
    }
}
```

### 7. TimeHandler (时间处理器)

`TimeHandler`提供统一的时间服务，支持测试时的时间模拟。

#### 接口定义

```rust
pub trait TimeHandler: Any + Send + Sync {
    /// 获取当前时间戳（毫秒）
    fn current_time_millis(&self) -> u128;
}
```

#### 实现

```rust
impl TimeHandler for ResourceTimeHandler {
    fn current_time_millis(&self) -> u128 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis()
    }
}
```

## 错误处理

### 错误类型定义

```rust
// 下载错误
#[derive(Error, Debug)]
pub enum DownloadError {
    #[error("无效的下载URL: {0}")]
    InvalidUrl(String),
    
    #[error("网络请求错误: {0}")]
    RequestError(#[from] reqwest::Error),
    
    #[error("响应状态码错误: {0}")]
    ResponseError(StatusCode),
    
    #[error("用户取消: {0}")]
    Cancelled(String),
    
    #[error("文件错误: {0}")]
    FileError(#[from] SystemFileError),
}

// 文件系统错误
#[derive(Error, Debug)]
pub enum SystemFileError {
    #[error("路径为空")]
    PathIsEmpty,
    
    #[error("路径无效: {0}")]
    PathInvalid(String),
    
    #[error("IO错误: {0}")]
    IOError(#[from] std::io::Error),
    
    #[error("ZIP错误: {0}")]
    ZipError(#[from] zip::result::ZipError),
}
```

## 最佳实践

### 1. Handler注册

```rust
// 在ResourceManager中注册Handler
ResourceManager::get_instance().set_download_handler(
    Box::new(ResourceDownloadHandler::new())
);
ResourceManager::get_instance().set_file_handler(
    Box::new(ResourceFileHandler::new())
);
```

### 2. 自定义Handler

```rust
pub struct CustomDownloadHandler {
    // 自定义字段
}

#[async_trait]
impl DownloadHandler for CustomDownloadHandler {
    async fn download(&self, resource_info: &ResourceInfo) -> Result<(), DownloadError> {
        // 自定义下载逻辑
        Ok(())
    }
    
    fn cancel(&self, resource_id: &str) {
        // 自定义取消逻辑
    }
}
```

### 3. 错误处理

```rust
// Handler中的错误处理
async fn download(&self, resource_info: &ResourceInfo) -> Result<(), DownloadError> {
    match self.do_download(resource_info).await {
        Ok(result) => Ok(result),
        Err(e) => {
            log::error!("下载失败: {:?}", e);
            self.cleanup_on_error(resource_info);
            Err(e)
        }
    }
}
```

### 4. 资源清理

```rust
// 及时清理临时资源
impl Drop for ResourceDownloadHandler {
    fn drop(&mut self) {
        // 清理下载临时文件
        self.cleanup_temp_files();
    }
}
```
