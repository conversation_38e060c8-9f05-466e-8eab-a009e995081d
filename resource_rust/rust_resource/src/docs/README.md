# Rust Resource 代码库知识文档

## 概述

Rust Resource是一个跨平台的智能设备资源管理库，基于Rust语言开发，支持Android、iOS和HarmonyOS三个平台。该库提供了完整的资源包管理功能，包括资源下载、安装、验证、缓存和生命周期管理等核心功能。

## 整体架构

```mermaid
graph TB
    subgraph "应用层"
        A[Android App] 
        B[iOS App]
        C[HarmonyOS App]
    end
    
    subgraph "FFI接口层"
        D[Android JNI]
        E[iOS C Interface] 
        F[HarmonyOS NAPI]
    end
    
    subgraph "API层"
        G[UPResource]
        H[ResourceManager]
        I[ResourceCallback]
        J[ResourceFilter]
    end
    
    subgraph "Pipeline处理层"
        K[PipelineController]
        L[PipelineSingle]
        M[PipelineBatch]
        N[Stage Pipeline]
    end
    
    subgraph "处理器层"
        O[DownloadHandler]
        P[InstallHandler]
        Q[FileHandler]
        R[CleanHandler]
        S[ValidationHandler]
    end
    
    subgraph "数据源层"
        T[ResourceDataSource]
        U[ResourceDatabase]
        V[ServerAPI]
        W[Cache Management]
    end
    
    subgraph "基础设施层"
        X[Models]
        Y[Utils]
        Z[Directory]
        AA[EventBus]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> G
    F --> G
    G --> H
    H --> K
    K --> L
    K --> M
    L --> N
    M --> N
    N --> O
    N --> P
    N --> Q
    N --> R
    N --> S
    H --> T
    T --> U
    T --> V
    T --> W
    O --> X
    P --> Y
    Q --> Z
    R --> AA
```

## 核心特性

### 资源管理
- **多类型资源支持**: 支持设备资源包、主题资源、配置资源等多种类型
- **版本控制**: 完整的资源版本管理和升级机制
- **智能缓存**: 多级缓存策略，提高资源访问效率
- **增量更新**: 支持资源的增量下载和更新

### 跨平台支持
- **统一核心**: Rust实现的核心逻辑在所有平台共享
- **平台适配**: 通过FFI接口适配Android、iOS、HarmonyOS
- **数据序列化**: 使用FlatBuffers实现跨平台数据交换

### 管道处理
- **阶段化处理**: 将资源处理分解为多个独立的阶段
- **并发控制**: 支持多个资源的并发下载和处理
- **错误恢复**: 完善的错误处理和恢复机制
- **进度监控**: 实时的下载和安装进度反馈

### 数据持久化
- **SQLite数据库**: 使用Diesel ORM进行数据库操作
- **事务支持**: 保证数据操作的原子性和一致性
- **查询优化**: 高效的资源查询和过滤机制

## 核心模块

### 1. [API模块](./api.md)
- **UPResource**: 资源管理的主要接口
- **ResourceManager**: 资源管理器，管理各种组件的生命周期
- **ResourceCallback**: 资源操作的回调接口
- **ResourceFilter**: 资源过滤和查询功能

### 2. [Pipeline模块](./pipelines.md)
- **PipelineController**: 管道控制器，管理资源处理流程
- **PipelineSingle**: 单个资源的处理管道
- **PipelineBatch**: 批量资源的处理管道
- **Stage系统**: 阶段化的资源处理流程

### 3. [Handlers模块](./handlers.md)
- **DownloadHandler**: 资源下载处理器
- **InstallHandler**: 资源安装处理器
- **FileHandler**: 文件操作处理器
- **CleanHandler**: 资源清理处理器

### 4. [Cache模块](./cache.md)
- **ResourceDatabase**: 资源数据库管理
- **ResourceRecord**: 资源记录模型
- **查询优化**: 高效的数据库查询机制

### 5. [DataSource模块](./data_source.md)
- **ResourceDataSource**: 资源数据源抽象
- **ServerAPI**: 服务器API接口
- **本地缓存**: 本地数据缓存管理

### 6. [Models模块](./models.md)
- **ResourceInfo**: 资源信息模型
- **ResourceType**: 资源类型定义
- **QueryInfo**: 查询信息模型

### 7. [Utils模块](./utils.md)
- **Directory**: 目录管理工具
- **FileUtils**: 文件操作工具
- **NetworkUtils**: 网络工具

### 8. [Features模块](./features.md)
- **跨平台FFI**: Android、iOS、HarmonyOS的原生接口
- **FlatBuffers**: 跨平台数据序列化
- **平台适配**: 各平台特定的实现

## 技术特性

### 异步编程
- 基于Tokio异步运行时
- 支持并发资源下载和处理
- 异步事件处理机制

### 内存安全
- Rust语言的内存安全保证
- Arc/Mutex等线程安全机制
- 零拷贝数据传输

### 错误处理
- 完善的错误类型定义
- 错误传播和恢复机制
- 用户友好的错误信息

### 性能优化
- 断点续传下载
- 并发下载控制
- 智能缓存策略
- 资源压缩和解压

## 依赖关系

```mermaid
graph LR
    A[rust_resource] --> B[request_rust]
    A --> C[task_manager]
    A --> D[tokio]
    A --> E[diesel]
    A --> F[reqwest]
    A --> G[serde]
    A --> H[flatbuffers]
    A --> I[zip]
    A --> J[md5]
    A --> K[uuid]
```

## 快速开始

### 初始化
```rust
use rust_resource::api::resource::{UPResource, ResourcePlatform, ResourceRequestEnvironment};
use rust_resource::api::resource_manager::ResourceManager;

// 初始化资源管理器
ResourceManager::get_instance().init(
    ResourcePlatform::Android,
    "1.0.0".to_string(),
    ResourceRequestEnvironment::Production,
    false, // is_test_mode
    "/path/to/resource/root".to_string(),
    Box::new(database_instance)
);

// 获取资源管理实例
let resource = UPResource::new();
```

### 预置资源
```rust
// 预置资源包
let result = resource.preset_resource_list(
    "/path/to/preset/bundles".to_string(),
    Some(Box::new(callback))
);
```

### 搜索和安装资源
```rust
// 搜索资源
let resources = resource.search_normal_resource_list(
    resource_condition,
    Some(Box::new(callback))
).await?;

// 安装资源
let task_id = resource.install(
    resource_info,
    Some(Box::new(callback))
)?;
```

### 获取已安装资源
```rust
// 获取最新已安装资源
let latest_resource = resource.get_latest_installed_resource(
    resource_type,
    resource_name
)?;
```

## 测试

项目使用Cucumber进行BDD测试，测试文件位于`tests/`目录：

```bash
# 运行所有测试
cargo test

# 运行特定feature测试
cargo test --test cucumber
```

## 构建

支持多平台构建：

```bash
# Android
cargo build --features android

# iOS  
cargo build --features ios

# HarmonyOS
cargo build --features ohos
```

## 文档导航

### 架构设计
- [整体架构设计](./architecture.md) - 系统架构、设计模式、性能优化等

### 模块详解
- [API模块详解](./api.md) - 资源管理接口、回调机制、过滤功能
- [Pipeline模块详解](./pipelines.md) - 管道处理、阶段化流程、并发控制
- [Handlers模块详解](./handlers.md) - 各种处理器、文件操作、下载管理
- [Cache模块详解](./cache.md) - 数据库管理、缓存策略、查询优化
- [DataSource模块详解](./data_source.md) - 数据源抽象、服务器API、本地缓存
- [Models模块详解](./models.md) - 数据模型、类型定义、序列化
- [Utils模块详解](./utils.md) - 工具函数、目录管理、文件操作
- [Features模块详解](./features.md) - 跨平台FFI、序列化、平台适配

### 开发工具
- [代码库知识文档生成提示词模板](./代码库知识文档生成提示词模板.md) - 用于生成其他代码库文档的AI提示词模板
