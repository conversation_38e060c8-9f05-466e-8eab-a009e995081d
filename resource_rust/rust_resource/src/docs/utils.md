# Utils模块

Utils模块位于`src/utils`目录下，提供了Rust Resource库中使用的各种工具函数和辅助组件，为其他模块提供通用的功能支持。

## 模块结构

```
src/utils/
├── directory.rs             # 目录管理工具
├── file_utils.rs           # 文件操作工具
├── network_utils.rs        # 网络工具
├── time_utils.rs           # 时间工具
├── string_utils.rs         # 字符串工具
├── validation_utils.rs     # 验证工具
└── mod.rs
```

## 核心工具

### 1. Directory (目录管理工具)

`Directory`提供了统一的目录管理功能，负责资源文件的路径规划和目录创建。

#### 接口定义

```rust
pub trait Directory: Any + Send + Sync {
    /// 设置根目录路径
    fn set_root_path(&self, root_path: String);
    
    /// 获取根目录路径
    fn get_root_path(&self) -> String;
    
    /// 获取下载临时文件路径
    fn get_download_temp_file_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError>;
    
    /// 获取解压临时目录路径
    fn get_extract_temp_dir_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError>;
    
    /// 获取资源存储目录路径
    fn get_resource_storage_dir_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError>;
    
    /// 获取资源最终路径
    fn get_resource_final_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError>;
}
```

#### 实现类

```rust
pub struct ResourceDirectory {
    root_path: RwLock<String>,
}

impl ResourceDirectory {
    pub fn new() -> Self {
        Self {
            root_path: RwLock::new(String::new()),
        }
    }
    
    /// 构建安全的文件路径
    fn build_safe_path(&self, components: &[&str]) -> Result<String, DirectoryError> {
        let root = self.root_path.read().clone();
        if root.is_empty() {
            return Err(DirectoryError::RootPathNotSet);
        }
        
        let mut path = PathBuf::from(root);
        for component in components {
            // 防止路径遍历攻击
            if component.contains("..") || component.contains("/") || component.contains("\\") {
                return Err(DirectoryError::InvalidPathComponent(component.to_string()));
            }
            path.push(component);
        }
        
        Ok(path.to_string_lossy().to_string())
    }
}

impl Directory for ResourceDirectory {
    fn get_download_temp_file_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError> {
        let filename = format!("{}_{}.tmp", resource.name, resource.version);
        self.build_safe_path(&["temp", "downloads", &filename])
    }
    
    fn get_extract_temp_dir_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError> {
        let dirname = format!("{}_{}", resource.name, resource.version);
        self.build_safe_path(&["temp", "extract", &dirname])
    }
    
    fn get_resource_storage_dir_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError> {
        let type_dir = resource.resource_type.get_default_directory();
        self.build_safe_path(&["resources", type_dir, &resource.name])
    }
    
    fn get_resource_final_path(&self, resource: &ResourceInfo) -> Result<String, DirectoryError> {
        let storage_dir = self.get_resource_storage_dir_path(resource)?;
        let filename = format!("{}_{}", resource.version, resource.name);
        Ok(format!("{}/{}", storage_dir, filename))
    }
}
```

#### 目录结构规划

```mermaid
graph TB
    subgraph "根目录"
        Root[resource_root/]
    end
    
    subgraph "资源目录"
        Resources[resources/]
        Devices[devices/]
        Themes[themes/]
        Configs[configs/]
    end
    
    subgraph "临时目录"
        Temp[temp/]
        Downloads[downloads/]
        Extract[extract/]
    end
    
    subgraph "缓存目录"
        Cache[cache/]
        Database[database/]
        Logs[logs/]
    end
    
    Root --> Resources
    Root --> Temp
    Root --> Cache
    Resources --> Devices
    Resources --> Themes
    Resources --> Configs
    Temp --> Downloads
    Temp --> Extract
    Cache --> Database
    Cache --> Logs
```

### 2. FileUtils (文件操作工具)

`FileUtils`提供了扩展的文件操作功能，补充标准库的文件操作。

#### 核心功能

```rust
pub struct FileUtils;

impl FileUtils {
    /// 安全地创建目录
    pub fn create_dir_all_safe(path: &str) -> Result<(), FileError> {
        let path = Path::new(path);
        
        // 检查路径是否安全
        if Self::is_safe_path(path)? {
            std::fs::create_dir_all(path)
                .map_err(|e| FileError::IOError(e))?;
        } else {
            return Err(FileError::UnsafePath(path.to_string_lossy().to_string()));
        }
        
        Ok(())
    }
    
    /// 计算目录大小
    pub fn calculate_dir_size(path: &str) -> Result<u64, FileError> {
        let mut total_size = 0u64;
        
        for entry in WalkDir::new(path) {
            let entry = entry.map_err(|e| FileError::WalkDirError(e.to_string()))?;
            if entry.file_type().is_file() {
                let metadata = entry.metadata()
                    .map_err(|e| FileError::IOError(e))?;
                total_size += metadata.len();
            }
        }
        
        Ok(total_size)
    }
    
    /// 清理空目录
    pub fn cleanup_empty_dirs(path: &str) -> Result<usize, FileError> {
        let mut removed_count = 0;
        
        for entry in WalkDir::new(path).contents_first(true) {
            let entry = entry.map_err(|e| FileError::WalkDirError(e.to_string()))?;
            
            if entry.file_type().is_dir() {
                if Self::is_empty_dir(entry.path())? {
                    std::fs::remove_dir(entry.path())
                        .map_err(|e| FileError::IOError(e))?;
                    removed_count += 1;
                }
            }
        }
        
        Ok(removed_count)
    }
    
    /// 安全地复制文件
    pub fn copy_file_safe(source: &str, target: &str) -> Result<u64, FileError> {
        let source_path = Path::new(source);
        let target_path = Path::new(target);
        
        // 验证路径安全性
        if !Self::is_safe_path(source_path)? || !Self::is_safe_path(target_path)? {
            return Err(FileError::UnsafePath("Invalid path".to_string()));
        }
        
        // 创建目标目录
        if let Some(parent) = target_path.parent() {
            Self::create_dir_all_safe(&parent.to_string_lossy())?;
        }
        
        // 复制文件
        std::fs::copy(source_path, target_path)
            .map_err(|e| FileError::IOError(e))
    }
    
    /// 检查路径是否安全
    fn is_safe_path(path: &Path) -> Result<bool, FileError> {
        let path_str = path.to_string_lossy();
        
        // 检查路径遍历攻击
        if path_str.contains("..") {
            return Ok(false);
        }
        
        // 检查绝对路径（根据需要调整）
        if path.is_absolute() {
            // 可以根据具体需求决定是否允许绝对路径
        }
        
        Ok(true)
    }
    
    /// 检查目录是否为空
    fn is_empty_dir(path: &Path) -> Result<bool, FileError> {
        let mut entries = std::fs::read_dir(path)
            .map_err(|e| FileError::IOError(e))?;
        Ok(entries.next().is_none())
    }
}
```

### 3. NetworkUtils (网络工具)

`NetworkUtils`提供网络相关的工具函数。

#### 功能实现

```rust
pub struct NetworkUtils;

impl NetworkUtils {
    /// 检查URL是否可访问
    pub async fn is_url_accessible(url: &str) -> Result<bool, NetworkError> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .map_err(|e| NetworkError::ClientError(e.to_string()))?;
        
        match client.head(url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }
    
    /// 获取URL的内容长度
    pub async fn get_content_length(url: &str) -> Result<Option<u64>, NetworkError> {
        let client = reqwest::Client::new();
        let response = client.head(url).send().await
            .map_err(|e| NetworkError::RequestError(e.to_string()))?;
        
        Ok(response.headers()
            .get(reqwest::header::CONTENT_LENGTH)
            .and_then(|value| value.to_str().ok())
            .and_then(|s| s.parse().ok()))
    }
    
    /// 验证URL格式
    pub fn is_valid_url(url: &str) -> bool {
        url::Url::parse(url).is_ok()
    }
    
    /// 提取文件名从URL
    pub fn extract_filename_from_url(url: &str) -> Option<String> {
        url::Url::parse(url).ok()
            .and_then(|parsed_url| {
                parsed_url.path_segments()
                    .and_then(|segments| segments.last())
                    .filter(|name| !name.is_empty())
                    .map(|name| name.to_string())
            })
    }
    
    /// 构建查询参数
    pub fn build_query_string(params: &HashMap<String, String>) -> String {
        params.iter()
            .map(|(key, value)| format!("{}={}", 
                urlencoding::encode(key), 
                urlencoding::encode(value)))
            .collect::<Vec<_>>()
            .join("&")
    }
}
```

### 4. TimeUtils (时间工具)

`TimeUtils`提供统一的时间处理功能。

#### 实现

```rust
pub struct TimeUtils;

impl TimeUtils {
    /// 获取当前时间戳（毫秒）
    pub fn current_timestamp_millis() -> u128 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis()
    }
    
    /// 获取当前时间戳（秒）
    pub fn current_timestamp_secs() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
    
    /// 格式化时间戳为可读字符串
    pub fn format_timestamp(timestamp: i64) -> String {
        let datetime = DateTime::<Utc>::from_timestamp(timestamp / 1000, 0)
            .unwrap_or_default();
        datetime.format("%Y-%m-%d %H:%M:%S UTC").to_string()
    }
    
    /// 计算时间差（毫秒）
    pub fn time_diff_millis(start: i64, end: i64) -> i64 {
        end - start
    }
    
    /// 检查时间戳是否过期
    pub fn is_expired(timestamp: i64, ttl_millis: i64) -> bool {
        let current = Self::current_timestamp_millis() as i64;
        current > timestamp + ttl_millis
    }
    
    /// 格式化持续时间
    pub fn format_duration(duration_millis: u64) -> String {
        let seconds = duration_millis / 1000;
        let minutes = seconds / 60;
        let hours = minutes / 60;
        
        if hours > 0 {
            format!("{}小时{}分钟", hours, minutes % 60)
        } else if minutes > 0 {
            format!("{}分钟{}秒", minutes, seconds % 60)
        } else {
            format!("{}秒", seconds)
        }
    }
}
```

### 5. StringUtils (字符串工具)

`StringUtils`提供字符串处理的辅助功能。

#### 实现

```rust
pub struct StringUtils;

impl StringUtils {
    /// 安全截取字符串
    pub fn safe_substring(s: &str, start: usize, len: usize) -> &str {
        let end = std::cmp::min(start + len, s.len());
        let start = std::cmp::min(start, s.len());
        &s[start..end]
    }
    
    /// 检查字符串是否为空或仅包含空白字符
    pub fn is_empty_or_whitespace(s: &str) -> bool {
        s.trim().is_empty()
    }
    
    /// 清理文件名中的非法字符
    pub fn sanitize_filename(filename: &str) -> String {
        filename.chars()
            .map(|c| match c {
                '<' | '>' | ':' | '"' | '/' | '\\' | '|' | '?' | '*' => '_',
                _ => c,
            })
            .collect()
    }
    
    /// 生成随机字符串
    pub fn generate_random_string(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789";
        let mut rng = rand::thread_rng();
        
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }
    
    /// 计算字符串的MD5哈希
    pub fn calculate_md5(input: &str) -> String {
        format!("{:x}", md5::compute(input.as_bytes()))
    }
    
    /// 格式化文件大小
    pub fn format_file_size(size_bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = size_bytes as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        if unit_index == 0 {
            format!("{} {}", size_bytes, UNITS[unit_index])
        } else {
            format!("{:.2} {}", size, UNITS[unit_index])
        }
    }
}
```

### 6. ValidationUtils (验证工具)

`ValidationUtils`提供数据验证的通用功能。

#### 实现

```rust
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证MD5哈希格式
    pub fn is_valid_md5(hash: &str) -> bool {
        hash.len() == 32 && hash.chars().all(|c| c.is_ascii_hexdigit())
    }
    
    /// 验证版本号格式
    pub fn is_valid_version(version: &str) -> bool {
        let version_regex = regex::Regex::new(r"^\d+(\.\d+)*$").unwrap();
        version_regex.is_match(version)
    }
    
    /// 验证资源名称
    pub fn is_valid_resource_name(name: &str) -> bool {
        !name.is_empty() && 
        name.len() <= 100 && 
        name.chars().all(|c| c.is_alphanumeric() || c == '_' || c == '-')
    }
    
    /// 验证文件路径
    pub fn is_valid_file_path(path: &str) -> bool {
        !path.is_empty() && 
        !path.contains("..") && 
        Path::new(path).is_relative()
    }
    
    /// 验证URL格式
    pub fn is_valid_http_url(url: &str) -> bool {
        if let Ok(parsed) = url::Url::parse(url) {
            matches!(parsed.scheme(), "http" | "https")
        } else {
            false
        }
    }
    
    /// 验证文件扩展名
    pub fn is_valid_file_extension(filename: &str, allowed_extensions: &[&str]) -> bool {
        if let Some(extension) = Path::new(filename).extension() {
            if let Some(ext_str) = extension.to_str() {
                return allowed_extensions.contains(&ext_str.to_lowercase().as_str());
            }
        }
        false
    }
}
```

## 错误处理

### 错误类型定义

```rust
#[derive(Debug, thiserror::Error)]
pub enum DirectoryError {
    #[error("根目录未设置")]
    RootPathNotSet,
    
    #[error("无效的路径组件: {0}")]
    InvalidPathComponent(String),
    
    #[error("路径创建失败: {0}")]
    PathCreationFailed(String),
}

#[derive(Debug, thiserror::Error)]
pub enum FileError {
    #[error("IO错误: {0}")]
    IOError(#[from] std::io::Error),
    
    #[error("不安全的路径: {0}")]
    UnsafePath(String),
    
    #[error("目录遍历错误: {0}")]
    WalkDirError(String),
}

#[derive(Debug, thiserror::Error)]
pub enum NetworkError {
    #[error("客户端错误: {0}")]
    ClientError(String),
    
    #[error("请求错误: {0}")]
    RequestError(String),
    
    #[error("超时错误")]
    TimeoutError,
}
```

## 最佳实践

### 1. 目录管理

```rust
// 初始化目录管理器
let directory = ResourceDirectory::new();
directory.set_root_path("/path/to/resource/root".to_string());

// 获取安全的文件路径
let download_path = directory.get_download_temp_file_path(&resource_info)?;
```

### 2. 文件操作

```rust
// 安全的文件操作
FileUtils::create_dir_all_safe(&target_dir)?;
let copied_bytes = FileUtils::copy_file_safe(&source_file, &target_file)?;

// 清理临时文件
FileUtils::cleanup_empty_dirs(&temp_dir)?;
```

### 3. 网络工具

```rust
// 检查URL可访问性
if NetworkUtils::is_url_accessible(&download_url).await? {
    // 获取文件大小
    if let Some(size) = NetworkUtils::get_content_length(&download_url).await? {
        println!("文件大小: {}", StringUtils::format_file_size(size));
    }
}
```

### 4. 数据验证

```rust
// 验证资源信息
if !ValidationUtils::is_valid_resource_name(&resource.name) {
    return Err(ValidationError::InvalidResourceName);
}

if !ValidationUtils::is_valid_md5(&resource.hash) {
    return Err(ValidationError::InvalidHash);
}
```

### 5. 时间处理

```rust
// 记录操作时间
let start_time = TimeUtils::current_timestamp_millis() as i64;
// ... 执行操作
let end_time = TimeUtils::current_timestamp_millis() as i64;
let duration = TimeUtils::time_diff_millis(start_time, end_time);
println!("操作耗时: {}", TimeUtils::format_duration(duration as u64));
```
