# Pipeline模块

Pipeline模块位于`src/pipelines`目录下，是Rust Resource库的核心处理引擎，实现了基于管道模式的资源处理流程。该模块将复杂的资源操作分解为多个独立的阶段，支持并发处理和错误恢复。

## 模块结构

```
src/pipelines/
├── pipeline.rs              # 管道抽象
├── pipeline_single.rs       # 单资源管道
├── pipeline_batch.rs        # 批量管道
├── pipeline_controller.rs   # 管道控制器
├── pipeline_runner.rs       # 管道执行器
├── preset_resource_loader.rs # 预置资源加载器
├── error.rs                 # 管道错误定义
├── stages/                  # 处理阶段
│   ├── stage.rs            # 阶段抽象接口
│   ├── download.rs         # 下载阶段
│   ├── validate.rs         # 验证阶段
│   ├── extract.rs          # 解压阶段
│   ├── transport.rs        # 传输阶段
│   ├── scan.rs             # 扫描阶段
│   ├── update.rs           # 更新阶段
│   ├── remove.rs           # 删除阶段
│   └── preset_download.rs  # 预置下载阶段
└── mod.rs
```

## 核心组件

### 1. Pipeline架构

```mermaid
graph TB
    subgraph "管道类型"
        PS[PipelineSingle<br/>单资源管道]
        PB[PipelineBatch<br/>批量管道]
        PP[PresetPipeline<br/>预置管道]
    end
    
    subgraph "管道控制"
        PC[PipelineController<br/>管道控制器]
        WQ[等待队列]
        RQ[运行队列]
    end
    
    subgraph "执行引擎"
        PR[PipelineRunner<br/>单管道执行器]
        BR[BatchPipelineRunner<br/>批量执行器]
        TM[TaskManager<br/>任务管理器]
    end
    
    subgraph "处理阶段"
        DS[DownloadStage]
        VS[ValidateStage]
        ES[ExtractStage]
        TS[TransportStage]
        SS[ScanStage]
        US[UpdateStage]
        RS[RemoveStage]
    end
    
    PS --> PC
    PB --> PC
    PP --> PC
    PC --> WQ
    PC --> RQ
    WQ --> PR
    WQ --> BR
    RQ --> PR
    RQ --> BR
    PR --> TM
    BR --> TM
    PS --> DS
    PS --> VS
    PS --> ES
    PS --> TS
    PS --> SS
    PS --> US
    PB --> RS
```

### 2. PipelineController (管道控制器)

`PipelineController`是管道系统的核心控制器，负责管道的调度、执行和生命周期管理。

#### 核心功能

- **管道队列管理**: 维护等待队列和运行队列
- **并发控制**: 限制同时运行的管道数量
- **优先级调度**: 支持高优先级资源的优先处理
- **生命周期管理**: 管理管道的创建、执行和销毁

#### 实现细节

```rust
pub struct PipelineController {
    /// 正在运行的管道
    running_pipelines: HashMap<String, Pipeline>,
    /// 等待执行的管道
    waiting_pipelines: Vec<Pipeline>,
    /// 读写锁保护并发访问
    running_waiting_lock: RwLock<()>,
    /// 同步执行的管道
    sync_pipelines: HashMap<String, Pipeline>,
}

impl PipelineController {
    /// 添加管道到队列
    pub fn add_pipeline(&mut self, pipeline: Pipeline) -> &mut Pipeline {
        let id = pipeline.identifier().to_string();
        
        // 同步管道直接执行
        if pipeline.running_mode() == &RunningMode::Sync {
            self.sync_pipelines.insert(id.clone(), pipeline);
            return self.sync_pipelines.get_mut(&id).unwrap();
        }

        // 异步管道加入队列
        let _guard = self.running_waiting_lock.write();
        if pipeline.download_priority() == &ResourceDownloadPriority::High {
            // 高优先级插入队列前端
            self.waiting_pipelines.insert(0, pipeline);
            self.waiting_pipelines.first_mut().unwrap()
        } else {
            // 普通优先级追加到队列末尾
            self.waiting_pipelines.push(pipeline);
            self.waiting_pipelines.last_mut().unwrap()
        }
    }

    /// 执行下一个管道
    pub fn run_next(&mut self) -> Result<(), PipelineError> {
        let _guard = self.running_waiting_lock.write();

        // 检查并发限制
        if self.running_pipelines.len() >= MAX_PIPELINE_COUNT || self.waiting_pipelines.is_empty() {
            return Ok(());
        }

        // 取出队列头部的管道
        let pipeline = self.waiting_pipelines.remove(0);
        let pipeline_id = pipeline.identifier().to_string();
        let is_batch = matches!(&pipeline, Pipeline::Batch(_));
        
        // 添加到运行队列
        self.running_pipelines.insert(pipeline_id.clone(), pipeline);

        // 启动异步执行任务
        if is_batch {
            get_task_manager().schedule_task(BatchPipelineRunner::new(pipeline_id));
        } else {
            get_task_manager().schedule_task(PipelineRunner::new(pipeline_id));
        }

        Ok(())
    }
}
```

### 3. PipelineSingle (单资源管道)

`PipelineSingle`处理单个资源的完整生命周期，支持安装、卸载和预置三种类型。

#### 管道类型

```rust
#[derive(Debug, PartialEq)]
pub enum PipelineType {
    Install,    // 安装管道
    Uninstall,  // 卸载管道
    Preset,     // 预置管道
}

#[derive(Debug, PartialEq)]
pub enum RunningMode {
    Async,      // 异步执行
    Sync,       // 同步执行
}
```

#### 阶段初始化

```rust
impl PipelineSingle {
    fn init_stages(&mut self) {
        match self.pipeline_type {
            PipelineType::Install => {
                // 安装管道阶段
                self.add_stage(Box::new(DownloadStage::new()));
                self.add_stage(Box::new(ValidateStage::new()));
                self.add_stage(Box::new(ExtractStage::new()));
                self.add_stage(Box::new(TransportStage::new()));
                self.add_stage(Box::new(ScanStage::new()));
                self.add_stage(Box::new(UpdateStage::new()));
            }
            PipelineType::Uninstall => {
                // 卸载管道阶段
                self.add_stage(Box::new(UpdateStage::new()));
                self.add_stage(Box::new(RemoveStage::new()));
            }
            PipelineType::Preset => {
                // 预置管道阶段
                self.add_stage(Box::new(PresetDownloadStage::new()));
                
                // 根据资源类型动态添加阶段
                if self.resource().is_need_unzip() {
                    self.add_stage(Box::new(ExtractStage::new()));
                    self.add_stage(Box::new(TransportStage::new()));
                    self.add_stage(Box::new(ScanStage::new()));
                }
                
                self.add_stage(Box::new(UpdateStage::new()));
            }
        }
    }
}
```

#### 执行流程

```mermaid
sequenceDiagram
    participant PC as PipelineController
    participant PS as PipelineSingle
    participant Stage as Stage
    participant Handler as Handler
    participant Callback as Callback

    PC->>PS: execute_stages()
    PS->>PS: init_stages()
    
    loop 遍历所有阶段
        PS->>Stage: execute(resource_info)
        Stage->>Handler: 具体处理逻辑
        Handler-->>Stage: 处理结果
        Stage-->>PS: 阶段结果
        
        alt 阶段失败
            PS->>PS: 中断执行
        end
    end
    
    loop 清理资源
        PS->>Stage: recycle()
    end
    
    PS->>Callback: notify_result()
    PS-->>PC: 执行结果
```

### 4. Stage系统 (处理阶段)

每个Stage代表资源处理的一个独立阶段，具有明确的职责和接口。

#### Stage接口

```rust
#[async_trait]
pub trait Stage: Send + Sync {
    /// 执行阶段处理
    async fn execute(&mut self, resource_info: &mut ResourceInfo) -> Result<(), PipelineError>;
    
    /// 清理阶段资源
    fn recycle(&self);
    
    /// 取消阶段执行
    fn cancel(&mut self, pipeline_id: &str) -> Result<(), PipelineError>;
}
```

#### 主要阶段实现

##### 1. DownloadStage (下载阶段)

```rust
pub struct DownloadStage {
    base_stage: BaseStage,
}

#[async_trait]
impl Stage for DownloadStage {
    async fn execute(&mut self, resource_info: &mut ResourceInfo) -> Result<(), PipelineError> {
        debug!("Pipeline Stage: DownloadStage executing...");
        
        // 获取下载处理器
        let download_handler = ResourceManager::get_instance().get_download_handler();
        
        // 执行下载
        download_handler.download(resource_info).await
            .map_err(|e| PipelineError::DownloadError(e))?;
        
        debug!("Pipeline Stage: DownloadStage finish");
        Ok(())
    }
}
```

##### 2. ValidateStage (验证阶段)

```rust
pub struct ValidateStage {
    base_stage: BaseStage,
}

impl ValidateStage {
    /// 验证资源哈希值
    fn validate_resource_hash(
        &self,
        file_path: &str,
        resource_info: &ResourceInfo,
    ) -> Result<(), PipelineError> {
        let file_handler = ResourceManager::get_instance().get_file_handler();
        
        // 计算文件哈希
        let calculated_hash = file_handler.calculate_md5(file_path)
            .map_err(|e| PipelineError::ValidationError(e.to_string()))?;
        
        // 验证哈希值
        if calculated_hash != resource_info.hash {
            return Err(PipelineError::ValidationError(
                format!("Hash mismatch: expected {}, got {}", resource_info.hash, calculated_hash)
            ));
        }
        
        Ok(())
    }
}
```

##### 3. ExtractStage (解压阶段)

```rust
pub struct ExtractStage {
    base_stage: BaseStage,
}

#[async_trait]
impl Stage for ExtractStage {
    async fn execute(&mut self, resource_info: &mut ResourceInfo) -> Result<(), PipelineError> {
        debug!("Pipeline Stage: ExtractStage executing...");
        
        let file_handler = ResourceManager::get_instance().get_file_handler();
        let directory = ResourceManager::get_instance().get_directory();
        
        // 获取源文件和目标目录
        let source_file = directory.get_download_temp_file_path(resource_info)?;
        let target_dir = directory.get_extract_temp_dir_path(resource_info)?;
        
        // 创建目标目录
        file_handler.create_directory(&target_dir)
            .map_err(|e| PipelineError::FileError(e))?;
        
        // 解压文件
        file_handler.unzip(&source_file, &target_dir)
            .map_err(|e| PipelineError::FileError(e))?;
        
        debug!("Pipeline Stage: ExtractStage finish");
        Ok(())
    }
}
```

### 5. 错误处理和恢复

#### 错误类型

```rust
#[derive(Debug, thiserror::Error)]
pub enum PipelineError {
    #[error("下载错误: {0}")]
    DownloadError(#[from] DownloadError),
    
    #[error("验证错误: {0}")]
    ValidationError(String),
    
    #[error("文件错误: {0}")]
    FileError(#[from] SystemFileError),
    
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] DatabaseError),
    
    #[error("管道已取消: {0}")]
    Cancelled(String),
    
    #[error("未知错误: {0}")]
    Unknown(String),
}
```

#### 错误恢复机制

```rust
impl PipelineSingle {
    pub async fn execute_stages(&mut self) -> Result<ResourceInfo, PipelineError> {
        let mut result: Result<(), PipelineError> = Ok(());
        
        // 顺序执行所有阶段
        for stage in &mut self.stages {
            result = stage.execute(&mut self.resource_info).await;
            if result.is_err() {
                break; // 遇到错误立即停止
            }
        }

        // 清理所有阶段资源（无论成功失败）
        for stage in self.stages.iter().rev() {
            stage.recycle();
        }

        // 处理执行结果
        match result {
            Ok(_) => {
                self.notify_result(None);
                Ok(self.resource_info.clone())
            }
            Err(e) => {
                self.notify_result(Some(e.to_string()));
                Err(e)
            }
        }
    }
}
```

### 6. 进度监控和事件通知

#### 进度事件

```rust
#[derive(Debug, Clone)]
pub enum ResourceEvent {
    /// 安装进度
    InstallProgress(String, usize), // pipeline_id, progress
    /// 下载进度
    DownloadProgress(String, usize), // pipeline_id, progress
}
```

#### 事件订阅

```rust
impl PipelineSingle {
    fn init_stages(&mut self) {
        if self.pipeline_type == PipelineType::Install {
            // 订阅进度事件
            self.observer_id = Some(
                ResourceManager::get_instance()
                    .get_event_bus()
                    .observe(&Self::get_event_name(self.resource().id), |event| {
                        if let ResourceEvent::InstallProgress(pipeline_id, progress) = event {
                            Self::notify_progress(&pipeline_id, progress);
                        }
                    })
            );
        }
    }
    
    fn notify_progress(pipeline_id: &str, progress: usize) {
        if let Some(pipeline) = PipelineController::get_instance().get_pipeline(pipeline_id) {
            pipeline.notify_download_progress(progress);
        }
    }
}
```

## 性能优化

### 1. 并发控制

```rust
// 限制最大并发管道数量
const MAX_PIPELINE_COUNT: usize = 5;

// 优先级队列管理
if pipeline.download_priority() == &ResourceDownloadPriority::High {
    self.waiting_pipelines.insert(0, pipeline); // 高优先级插入队列前端
} else {
    self.waiting_pipelines.push(pipeline); // 普通优先级追加到队列末尾
}
```

### 2. 资源管理

```rust
impl Stage for ExtractStage {
    fn recycle(&self) {
        // 清理临时文件
        if let Ok(temp_dir) = self.get_temp_directory() {
            let _ = std::fs::remove_dir_all(temp_dir);
        }
    }
}
```

### 3. 异步执行

```rust
// 异步任务调度
get_task_manager().schedule_task(PipelineRunner::new(pipeline_id));

// 异步阶段执行
async fn execute_stages(&mut self) -> Result<ResourceInfo, PipelineError> {
    for stage in &mut self.stages {
        stage.execute(&mut self.resource_info).await?;
    }
    Ok(self.resource_info.clone())
}
```

## 最佳实践

### 1. 管道创建

```rust
// 创建安装管道
let mut pipeline = PipelineSingle::new(resource_info, PipelineType::Install);
pipeline.add_callback(Box::new(callback));

// 添加到控制器
let pipeline_ref = PipelineController::get_instance()
    .add_pipeline(Pipeline::Single(Box::new(pipeline)));

// 启动执行
pipeline_ref.run()?;
```

### 2. 自定义阶段

```rust
pub struct CustomStage {
    base_stage: BaseStage,
}

#[async_trait]
impl Stage for CustomStage {
    async fn execute(&mut self, resource_info: &mut ResourceInfo) -> Result<(), PipelineError> {
        // 自定义处理逻辑
        Ok(())
    }
    
    fn recycle(&self) {
        // 清理资源
    }
    
    fn cancel(&mut self, _pipeline_id: &str) -> Result<(), PipelineError> {
        // 取消处理
        Ok(())
    }
}
```

### 3. 错误处理

```rust
// 阶段级错误处理
async fn execute(&mut self, resource_info: &mut ResourceInfo) -> Result<(), PipelineError> {
    match self.do_work(resource_info).await {
        Ok(result) => Ok(result),
        Err(e) => {
            log::error!("阶段执行失败: {:?}", e);
            self.cleanup_on_error();
            Err(PipelineError::from(e))
        }
    }
}

// 管道级错误处理
match pipeline.execute_stages().await {
    Ok(resource) => {
        log::info!("管道执行成功: {}", resource.name);
    }
    Err(e) => {
        log::error!("管道执行失败: {:?}", e);
        // 执行错误恢复逻辑
    }
}
```
