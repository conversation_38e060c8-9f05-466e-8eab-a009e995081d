# DataSource模块

DataSource模块位于`src/data_source`目录下，负责资源数据的获取、处理和管理。该模块抽象了数据来源，支持从服务器API、本地缓存等多种数据源获取资源信息。

## 模块结构

```
src/data_source/
├── resource_data_source.rs   # 资源数据源接口
├── server_api.rs            # 服务器API接口
├── error.rs                 # 数据源错误定义
└── mod.rs
```

## 核心组件

### 1. ResourceDataSource (资源数据源)

`ResourceDataSource`定义了资源数据获取的抽象接口，支持多种数据源的统一访问。

#### 接口定义

```rust
#[async_trait]
pub trait ResourceDataSource: Any + Send + Sync {
    /// 获取普通资源列表
    async fn get_normal_resource_list(
        &self,
        resource_condition: NormalResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError>;
    
    /// 获取设备资源列表
    async fn get_device_resource_list(
        &self,
        resource_condition: DeviceResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError>;
    
    /// 获取资源详情
    async fn get_resource_detail(
        &self,
        resource_id: &str,
    ) -> Result<Option<ResourceInfo>, ResourceError>;
}
```

#### 实现类

```rust
pub struct UpResourceDataSource {
    platform: ResourcePlatform,
    server_api: Box<dyn ServerAPI>,
    cache: Arc<DashMap<String, Vec<ResourceInfo>>>,
}

impl UpResourceDataSource {
    pub fn new(platform: ResourcePlatform) -> Self {
        Self {
            platform,
            server_api: Box::new(ResourceServerAPI::new()),
            cache: Arc::new(DashMap::new()),
        }
    }
}
```

### 2. 数据获取流程

#### 普通资源获取

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant DS as DataSource
    participant Cache as 本地缓存
    participant API as 服务器API
    participant DB as 数据库

    Client->>DS: get_normal_resource_list()
    DS->>Cache: 检查内存缓存
    Cache-->>DS: 缓存结果
    
    alt 缓存未命中
        DS->>DB: 查询本地数据库
        DB-->>DS: 本地资源列表
        DS->>API: 请求服务器数据
        API-->>DS: 服务器资源列表
        DS->>DS: 合并和去重
        DS->>DB: 更新本地缓存
        DS->>Cache: 更新内存缓存
    end
    
    DS-->>Client: 最终资源列表
```

#### 实现细节

```rust
#[async_trait]
impl ResourceDataSource for UpResourceDataSource {
    async fn get_normal_resource_list(
        &self,
        resource_condition: NormalResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError> {
        let cache_key = self.generate_cache_key(&resource_condition);
        
        // 1. 检查内存缓存
        if let Some(cached_resources) = self.cache.get(&cache_key) {
            if !self.is_cache_expired(&cache_key) {
                return Ok(cached_resources.clone());
            }
        }
        
        // 2. 从服务器获取数据
        let server_resources = self.server_api
            .get_normal_resource_list(resource_condition.clone())
            .await?;
        
        // 3. 处理和转换数据
        let processed_resources = self.process_server_resources(server_resources)?;
        
        // 4. 更新缓存
        self.cache.insert(cache_key, processed_resources.clone());
        
        Ok(processed_resources)
    }
}
```

### 3. ServerAPI (服务器API接口)

`ServerAPI`负责与远程服务器的HTTP通信，获取资源元数据和列表。

#### 接口定义

```rust
#[async_trait]
pub trait ServerAPI: Send + Sync {
    /// 获取普通资源列表
    async fn get_normal_resource_list(
        &self,
        condition: NormalResourceCondition,
    ) -> Result<Vec<ServerResourceInfo>, ServerError>;
    
    /// 获取设备资源列表
    async fn get_device_resource_list(
        &self,
        condition: DeviceResourceCondition,
    ) -> Result<Vec<ServerResourceInfo>, ServerError>;
    
    /// 获取资源下载URL
    async fn get_resource_download_url(
        &self,
        resource_id: &str,
    ) -> Result<String, ServerError>;
}
```

#### HTTP客户端实现

```rust
pub struct ResourceServerAPI {
    client: reqwest::Client,
    base_url: String,
    timeout: Duration,
}

impl ResourceServerAPI {
    pub fn new() -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");
        
        Self {
            client,
            base_url: Self::get_base_url(),
            timeout: Duration::from_secs(30),
        }
    }
    
    fn get_base_url() -> String {
        let setting = ResourceManager::get_instance().get_resource_setting();
        match setting.environment {
            ResourceRequestEnvironment::Production => "https://api.example.com".to_string(),
            ResourceRequestEnvironment::Test => "https://test-api.example.com".to_string(),
            ResourceRequestEnvironment::Development => "https://dev-api.example.com".to_string(),
        }
    }
}
```

#### API请求实现

```rust
#[async_trait]
impl ServerAPI for ResourceServerAPI {
    async fn get_normal_resource_list(
        &self,
        condition: NormalResourceCondition,
    ) -> Result<Vec<ServerResourceInfo>, ServerError> {
        let url = format!("{}/api/v1/resources/normal", self.base_url);
        
        // 构建请求参数
        let mut params = HashMap::new();
        params.insert("resource_type", condition.resource_type.to_string());
        params.insert("app_version", condition.app_version);
        params.insert("from_func", condition.from_func);
        
        if let Some(name) = condition.resource_name {
            params.insert("resource_name", name);
        }
        
        // 添加平台信息
        let platform = ResourceManager::get_instance().get_resource_setting().platform;
        params.insert("platform", platform.to_string());
        
        // 发送HTTP请求
        let response = self.client
            .get(&url)
            .query(&params)
            .timeout(self.timeout)
            .send()
            .await
            .map_err(|e| ServerError::NetworkError(e.to_string()))?;
        
        // 检查响应状态
        if !response.status().is_success() {
            return Err(ServerError::HttpError(response.status().as_u16()));
        }
        
        // 解析响应数据
        let api_response: ApiResponse<Vec<ServerResourceInfo>> = response
            .json()
            .await
            .map_err(|e| ServerError::ParseError(e.to_string()))?;
        
        match api_response.code {
            0 => Ok(api_response.data.unwrap_or_default()),
            _ => Err(ServerError::ApiError(api_response.code, api_response.message)),
        }
    }
}
```

### 4. 数据处理和转换

#### 服务器数据转换

```rust
impl UpResourceDataSource {
    fn process_server_resources(
        &self,
        server_resources: Vec<ServerResourceInfo>,
    ) -> Result<Vec<ResourceInfo>, ResourceError> {
        let mut processed_resources = Vec::new();
        
        for server_resource in server_resources {
            // 转换数据格式
            let resource_info = self.convert_server_resource(server_resource)?;
            
            // 验证资源信息
            if self.validate_resource_info(&resource_info)? {
                processed_resources.push(resource_info);
            }
        }
        
        // 排序和去重
        self.sort_and_deduplicate(&mut processed_resources);
        
        Ok(processed_resources)
    }
    
    fn convert_server_resource(
        &self,
        server_resource: ServerResourceInfo,
    ) -> Result<ResourceInfo, ResourceError> {
        let current_time = ResourceManager::get_instance()
            .get_time_handler()
            .current_time_millis() as i64;
        
        Ok(ResourceInfo {
            id: 0, // 将在数据库插入时分配
            name: server_resource.name,
            version: server_resource.version,
            resource_type: server_resource.resource_type,
            create_time: current_time,
            update_time: current_time,
            download_url: server_resource.download_url,
            path: None,
            index_path: None,
            is_preset: false,
            is_active: false,
            is_server_latest: true,
            hide_status_bar: server_resource.hide_status_bar.unwrap_or(false),
            is_force_upgrade: server_resource.is_force_upgrade.unwrap_or(false),
            rules_json: server_resource.rules_json,
            remote_page_url: server_resource.remote_page_url,
            status: ResourceStatus::Published as i32,
            hash: server_resource.hash,
        })
    }
}
```

### 5. 缓存管理

#### 缓存策略

```rust
impl UpResourceDataSource {
    const CACHE_TTL: Duration = Duration::from_secs(300); // 5分钟缓存
    
    fn generate_cache_key(&self, condition: &NormalResourceCondition) -> String {
        format!(
            "{}:{}:{}:{}",
            condition.resource_type,
            condition.resource_name.as_deref().unwrap_or("*"),
            condition.app_version,
            condition.from_func
        )
    }
    
    fn is_cache_expired(&self, cache_key: &str) -> bool {
        // 检查缓存时间戳
        if let Some(timestamp) = self.cache_timestamps.get(cache_key) {
            let elapsed = timestamp.elapsed();
            elapsed > Self::CACHE_TTL
        } else {
            true
        }
    }
    
    fn invalidate_cache(&self, pattern: &str) {
        // 清除匹配模式的缓存
        self.cache.retain(|key, _| !key.contains(pattern));
        self.cache_timestamps.retain(|key, _| !key.contains(pattern));
    }
}
```

#### 缓存更新策略

```rust
impl UpResourceDataSource {
    /// 主动刷新缓存
    pub async fn refresh_cache(&self, condition: NormalResourceCondition) -> Result<(), ResourceError> {
        let cache_key = self.generate_cache_key(&condition);
        
        // 强制从服务器获取最新数据
        let fresh_resources = self.server_api
            .get_normal_resource_list(condition)
            .await?;
        
        let processed_resources = self.process_server_resources(fresh_resources)?;
        
        // 更新缓存
        self.cache.insert(cache_key.clone(), processed_resources);
        self.cache_timestamps.insert(cache_key, Instant::now());
        
        Ok(())
    }
    
    /// 预加载热点数据
    pub async fn preload_popular_resources(&self) -> Result<(), ResourceError> {
        let popular_conditions = self.get_popular_resource_conditions();
        
        for condition in popular_conditions {
            if let Err(e) = self.get_normal_resource_list(condition).await {
                log::warn!("预加载资源失败: {:?}", e);
            }
        }
        
        Ok(())
    }
}
```

### 6. 错误处理和重试

#### 错误类型

```rust
#[derive(Debug, thiserror::Error)]
pub enum ServerError {
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("HTTP错误: {0}")]
    HttpError(u16),
    
    #[error("API错误: 代码 {0}, 消息 {1}")]
    ApiError(i32, String),
    
    #[error("解析错误: {0}")]
    ParseError(String),
    
    #[error("超时错误")]
    TimeoutError,
}
```

#### 重试机制

```rust
impl ResourceServerAPI {
    async fn request_with_retry<T, F, Fut>(
        &self,
        operation: F,
        max_retries: u32,
    ) -> Result<T, ServerError>
    where
        F: Fn() -> Fut,
        Fut: Future<Output = Result<T, ServerError>>,
    {
        let mut last_error = None;
        
        for attempt in 0..=max_retries {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(e) => {
                    last_error = Some(e);
                    
                    if attempt < max_retries {
                        // 指数退避
                        let delay = Duration::from_millis(100 * 2_u64.pow(attempt));
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap())
    }
}
```

### 7. 数据验证

#### 资源信息验证

```rust
impl UpResourceDataSource {
    fn validate_resource_info(&self, resource: &ResourceInfo) -> Result<bool, ResourceError> {
        // 基本字段验证
        if resource.name.is_empty() {
            return Ok(false);
        }
        
        if resource.version.is_empty() {
            return Ok(false);
        }
        
        if resource.download_url.is_empty() {
            return Ok(false);
        }
        
        // URL格式验证
        if !self.is_valid_url(&resource.download_url) {
            return Ok(false);
        }
        
        // 哈希值验证
        if !self.is_valid_hash(&resource.hash) {
            return Ok(false);
        }
        
        // 版本号格式验证
        if !self.is_valid_version(&resource.version) {
            return Ok(false);
        }
        
        Ok(true)
    }
    
    fn is_valid_url(&self, url: &str) -> bool {
        url.starts_with("http://") || url.starts_with("https://")
    }
    
    fn is_valid_hash(&self, hash: &str) -> bool {
        hash.len() == 32 && hash.chars().all(|c| c.is_ascii_hexdigit())
    }
    
    fn is_valid_version(&self, version: &str) -> bool {
        // 简单的版本号格式检查
        !version.is_empty() && version.chars().any(|c| c.is_ascii_digit())
    }
}
```

## 性能优化

### 1. 并发请求

```rust
impl UpResourceDataSource {
    async fn get_multiple_resource_lists(
        &self,
        conditions: Vec<NormalResourceCondition>,
    ) -> Result<Vec<Vec<ResourceInfo>>, ResourceError> {
        let futures: Vec<_> = conditions
            .into_iter()
            .map(|condition| self.get_normal_resource_list(condition))
            .collect();
        
        let results = futures::future::try_join_all(futures).await?;
        Ok(results)
    }
}
```

### 2. 数据压缩

```rust
impl ResourceServerAPI {
    async fn get_compressed_resource_list(
        &self,
        condition: NormalResourceCondition,
    ) -> Result<Vec<ServerResourceInfo>, ServerError> {
        let response = self.client
            .get(&self.build_url("/api/v1/resources/normal"))
            .header("Accept-Encoding", "gzip, deflate")
            .query(&self.build_params(condition))
            .send()
            .await?;
        
        // 自动解压缩响应
        let decompressed_data = response.bytes().await?;
        let api_response: ApiResponse<Vec<ServerResourceInfo>> = 
            serde_json::from_slice(&decompressed_data)?;
        
        Ok(api_response.data.unwrap_or_default())
    }
}
```

### 3. 连接池管理

```rust
impl ResourceServerAPI {
    pub fn new_with_pool() -> Self {
        let client = reqwest::Client::builder()
            .pool_max_idle_per_host(10)
            .pool_idle_timeout(Duration::from_secs(30))
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");
        
        Self {
            client,
            base_url: Self::get_base_url(),
            timeout: Duration::from_secs(30),
        }
    }
}
```

## 最佳实践

### 1. 数据源初始化

```rust
// 在ResourceManager中初始化数据源
let data_source = Box::new(UpResourceDataSource::new(platform));
ResourceManager::get_instance().set_resource_data_source(data_source);
```

### 2. 缓存使用

```rust
// 合理使用缓存
let resources = data_source.get_normal_resource_list(condition).await?;

// 主动刷新缓存
data_source.refresh_cache(condition).await?;

// 清除特定缓存
data_source.invalidate_cache("device_resources");
```

### 3. 错误处理

```rust
// 优雅的错误处理
match data_source.get_normal_resource_list(condition).await {
    Ok(resources) => {
        // 处理成功结果
    }
    Err(ResourceError::NetworkError(_)) => {
        // 网络错误，尝试使用缓存数据
        let cached_resources = self.get_cached_resources(condition)?;
    }
    Err(e) => {
        log::error!("获取资源列表失败: {:?}", e);
        return Err(e);
    }
}
```

### 4. 性能监控

```rust
// 添加性能监控
impl UpResourceDataSource {
    async fn get_normal_resource_list_with_metrics(
        &self,
        condition: NormalResourceCondition,
    ) -> Result<Vec<ResourceInfo>, ResourceError> {
        let start_time = Instant::now();
        
        let result = self.get_normal_resource_list(condition).await;
        
        let elapsed = start_time.elapsed();
        log::info!("资源列表获取耗时: {:?}", elapsed);
        
        result
    }
}
```
