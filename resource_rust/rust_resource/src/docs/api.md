# API模块

API模块位于`src/api`目录下，是Rust Resource库的核心接口层，为上层应用提供统一、简洁的资源管理API。

## 模块结构

```
src/api/
├── resource.rs              # 资源管理主接口
├── resource_manager.rs      # 资源管理器
├── resource_callback.rs     # 回调接口定义
├── resource_list_callback.rs # 资源列表回调
├── error.rs                 # 错误定义
└── mod.rs                   # 模块声明
```

## 核心组件

### 1. UPResource (资源管理主接口)

`UPResource`是资源管理的核心类，提供了完整的资源生命周期管理功能。

#### 主要功能

- **预置资源管理**: 预置资源包的安装和管理
- **资源搜索**: 支持多种条件的资源查询
- **资源安装**: 异步资源下载和安装
- **资源卸载**: 资源的清理和卸载
- **状态查询**: 资源状态和信息查询

#### 核心方法

```rust
impl UPResource {
    /// 预置资源列表
    pub fn preset_resource_list(
        &self,
        preset_bundle_dir: String,
        callback: Option<Box<dyn ResourceListCallback>>,
    ) -> Result<(), ResourceError>;
    
    /// 搜索普通资源列表
    pub async fn search_normal_resource_list(
        &self,
        resource_condition: NormalResourceCondition,
        callback: Option<Box<dyn ResourceListCallback>>,
    ) -> Result<Vec<ResourceInfo>, ResourceError>;
    
    /// 搜索设备资源列表
    pub async fn search_device_resource_list(
        &self,
        resource_condition: DeviceResourceCondition,
        callback: Option<Box<dyn ResourceListCallback>>,
    ) -> Result<Vec<ResourceInfo>, ResourceError>;
    
    /// 安装资源包
    pub fn install(
        &self,
        resource_info: ResourceInfo,
        resource_callback: Option<Box<dyn ResourceCallback>>,
    ) -> Result<String, ResourceError>;
    
    /// 卸载资源包
    pub fn uninstall(
        &self,
        resource_info: ResourceInfo,
        resource_callback: Option<Box<dyn ResourceCallback>>,
    ) -> Result<String, ResourceError>;
    
    /// 获取最新资源
    pub fn get_latest_resource(
        &self,
        resource_type: ResourceType,
        resource_name: String,
    ) -> Result<Option<ResourceInfo>, ResourceError>;
    
    /// 获取最新已安装资源
    pub fn get_latest_installed_resource(
        &self,
        resource_type: ResourceType,
        resource_name: String,
    ) -> Result<Option<ResourceInfo>, ResourceError>;
}
```

#### 资源安装流程

```mermaid
sequenceDiagram
    participant App as 应用层
    participant UPR as UPResource
    participant PM as PipelineManager
    participant Pipeline as Pipeline
    participant Stage as Stage
    participant DB as Database

    App->>UPR: install(resourceInfo, callback)
    UPR->>UPR: 检查清理状态
    UPR->>PM: 创建安装管道
    PM->>Pipeline: new(resourceInfo, Install)
    Pipeline->>Pipeline: 初始化阶段
    
    Note over Pipeline: 下载阶段
    Pipeline->>Stage: DownloadStage.execute()
    Stage->>App: 进度回调
    
    Note over Pipeline: 验证阶段
    Pipeline->>Stage: ValidateStage.execute()
    
    Note over Pipeline: 解压阶段
    Pipeline->>Stage: ExtractStage.execute()
    
    Note over Pipeline: 传输阶段
    Pipeline->>Stage: TransportStage.execute()
    
    Note over Pipeline: 扫描阶段
    Pipeline->>Stage: ScanStage.execute()
    
    Note over Pipeline: 更新阶段
    Pipeline->>Stage: UpdateStage.execute()
    Stage->>DB: 更新资源记录
    
    Pipeline->>App: 结果回调
```

### 2. ResourceManager (资源管理器)

`ResourceManager`是一个单例管理器，负责管理所有核心组件的生命周期。

#### 管理的组件

- **UPResource**: 资源管理主接口
- **ResourceDataSource**: 资源数据源
- **各种Handler**: 安装、下载、文件、清理等处理器
- **ResourceDatabase**: 资源数据库
- **EventBus**: 事件总线

#### 初始化流程

```rust
impl ResourceManager {
    pub fn init(
        &self,
        platform: ResourcePlatform,
        app_version: String,
        environment: ResourceRequestEnvironment,
        is_test_mode: bool,
        resource_root_path: String,
        data_base: Box<dyn ResourceDatabase>,
    ) {
        // 1. 设置资源配置
        self.set_resource_setting(platform, app_version, environment, is_test_mode);
        
        // 2. 设置根目录
        self.directory.set_root_path(resource_root_path);
        
        // 3. 创建核心组件
        self.resource.set(UPResource::new()).unwrap_or(());
        self.set_resource_data_source(Box::new(UpResourceDataSource::new(platform)));
        self.set_event_bus(EventBus::new());
        
        // 4. 初始化各种处理器
        self.set_clean_handler(Box::new(ResourceCleanHandler::new()));
        self.set_request_handler(Box::new(ResourceRequestHandler::new()));
        self.set_relation_handler(Box::new(ResourceRelationHandler::new()));
        self.set_download_handler(Box::new(ResourceDownloadHandler::new()));
        self.set_install_handler(Box::new(UPResourceInstallHandler::new()));
        self.set_file_handler(Box::new(ResourceFileHandler::new()));
        
        // 5. 设置数据库
        self.set_data_base(data_base);
    }
}
```

### 3. ResourceCallback (回调接口)

资源操作的回调接口，提供进度通知和结果反馈。

#### 回调接口定义

```rust
pub trait ResourceCallback: Send + Sync {
    /// 获取唯一标识
    fn unique_id(&self) -> String;
    
    /// 进度变化回调
    fn on_progress_changed(&self, resource: &ResourceInfo, progress: usize);
    
    /// 操作结果回调
    fn on_result(&self, resource: &ResourceInfo, error_msg: Option<String>);
}

pub trait ResourceListCallback: Send + Sync {
    /// 列表结果回调
    fn on_list_result(&self, resources: &[ResourceInfo], error_msg: Option<String>);
}
```

#### 事件类型

```rust
#[derive(Debug, Clone)]
pub enum ResourceEvent {
    /// 安装进度事件
    InstallProgress(String, usize), // pipeline_id, progress
    
    /// 下载进度事件
    DownloadProgress(String, usize), // pipeline_id, progress
    
    /// 安装完成事件
    InstallComplete(String, Option<String>), // pipeline_id, error_msg
    
    /// 卸载完成事件
    UninstallComplete(String, Option<String>), // pipeline_id, error_msg
}
```

### 4. 资源搜索和过滤

#### 搜索条件

```rust
#[derive(Debug, Clone)]
pub struct NormalResourceCondition {
    /// 资源类型
    pub resource_type: ResourceType,
    /// 资源名称
    pub resource_name: Option<String>,
    /// 应用版本
    pub app_version: String,
    /// 来源函数
    pub from_func: String,
    /// 其他条件
    pub other_conditions: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub struct DeviceResourceCondition {
    /// 设备类型ID
    pub type_id: String,
    /// 设备型号
    pub model: String,
    /// 应用版本
    pub app_version: String,
    /// 来源函数
    pub from_func: String,
}
```

#### 搜索流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant UPR as UPResource
    participant RH as RequestHandler
    participant DS as DataSource
    participant DB as Database
    participant Server as 服务器

    App->>UPR: search_normal_resource_list()
    UPR->>RH: request_normal_resource_list()
    RH->>DB: 查询本地缓存
    DB-->>RH: 本地资源列表
    RH->>Server: 请求服务器资源
    Server-->>RH: 服务器资源列表
    RH->>RH: 合并和去重
    RH->>DB: 更新本地缓存
    RH-->>UPR: 最终资源列表
    UPR->>App: 回调通知结果
```

### 5. 资源状态管理

#### 资源状态

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum ResourceStatus {
    /// 发布中
    Published = 0,
    /// 已下架
    Offline = 1,
}

#[derive(Debug, Clone)]
pub struct ResourceState {
    /// 是否激活
    pub is_active: bool,
    /// 是否为最新服务器资源
    pub is_server_latest: bool,
    /// 是否预置资源
    pub is_preset: bool,
    /// 下载优先级
    pub download_priority: ResourceDownloadPriority,
}
```

#### 状态查询方法

```rust
impl UPResource {
    /// 检查是否正在清理
    pub fn is_cleaning(&self) -> bool {
        ResourceManager::get_instance()
            .get_clean_handler()
            .is_cleaning()
    }
    
    /// 获取资源状态
    pub fn get_resource_status(&self, resource_id: i64) -> Option<ResourceState> {
        // 从数据库查询资源状态
    }
    
    /// 检查资源是否已安装
    pub fn is_resource_installed(&self, resource_name: &str, resource_type: ResourceType) -> bool {
        self.get_latest_installed_resource(resource_type, resource_name.to_string())
            .map(|r| r.is_some())
            .unwrap_or(false)
    }
}
```

## 错误处理

### 错误类型定义

```rust
#[derive(Debug, thiserror::Error)]
pub enum ResourceError {
    #[error("缓存正在清理中")]
    CacheIsBeingCleaned,
    
    #[error("资源未找到: {0}")]
    ResourceNotFound(String),
    
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("文件错误: {0}")]
    FileError(#[from] SystemFileError),
    
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] DatabaseError),
    
    #[error("管道错误: {0}")]
    PipelineError(#[from] PipelineError),
    
    #[error("未知错误: {0}")]
    Unknown(String),
}
```

### 错误处理策略

```rust
impl UPResource {
    pub fn install(
        &self,
        resource_info: ResourceInfo,
        resource_callback: Option<Box<dyn ResourceCallback>>,
    ) -> Result<String, ResourceError> {
        // 1. 检查清理状态
        if self.is_cleaning() {
            if let Some(cb) = resource_callback {
                cb.on_result(
                    &resource_info,
                    Some("Cache is being cleaned, operation cannot be performed".to_string()),
                );
            }
            return Err(ResourceError::CacheIsBeingCleaned);
        }
        
        // 2. 执行安装逻辑
        let result = Installer::install(resource_info, resource_callback);
        
        // 3. 处理结果
        match result {
            Ok(task_id) => Ok(task_id),
            Err(e) => {
                log::error!("资源安装失败: {:?}", e);
                Err(e)
            }
        }
    }
}
```

## 最佳实践

### 1. 初始化顺序

```rust
// 1. 先初始化资源管理器
ResourceManager::get_instance().init(
    ResourcePlatform::Android,
    "1.0.0".to_string(),
    ResourceRequestEnvironment::Production,
    false,
    "/path/to/resource/root".to_string(),
    Box::new(database_instance)
);

// 2. 获取资源管理实例
let resource = UPResource::new();

// 3. 开始使用资源管理功能
```

### 2. 回调处理

```rust
struct MyResourceCallback {
    id: String,
}

impl ResourceCallback for MyResourceCallback {
    fn unique_id(&self) -> String {
        self.id.clone()
    }
    
    fn on_progress_changed(&self, resource: &ResourceInfo, progress: usize) {
        println!("资源 {} 下载进度: {}%", resource.name, progress);
    }
    
    fn on_result(&self, resource: &ResourceInfo, error_msg: Option<String>) {
        match error_msg {
            Some(err) => println!("资源 {} 安装失败: {}", resource.name, err),
            None => println!("资源 {} 安装成功", resource.name),
        }
    }
}
```

### 3. 异步操作

```rust
// 异步搜索资源
let resources = resource.search_normal_resource_list(
    condition,
    Some(Box::new(callback))
).await?;

// 批量安装资源
for resource_info in resources {
    let task_id = resource.install(
        resource_info,
        Some(Box::new(MyResourceCallback::new()))
    )?;
    println!("启动安装任务: {}", task_id);
}
```

### 4. 资源管理

```rust
// 检查资源状态
if resource.is_cleaning() {
    println!("资源正在清理中，请稍后再试");
    return;
}

// 获取最新资源
if let Ok(Some(latest)) = resource.get_latest_resource(
    ResourceType::Device,
    "smart_light".to_string()
) {
    println!("最新资源版本: {}", latest.version);
}

// 清理过期资源
resource.clean(vec![], true).await?;
```

### 5. 错误恢复

```rust
// 带重试的资源安装
async fn install_with_retry(
    resource: &UPResource,
    resource_info: ResourceInfo,
    max_retries: u32,
) -> Result<String, ResourceError> {
    for attempt in 0..max_retries {
        match resource.install(resource_info.clone(), None) {
            Ok(task_id) => return Ok(task_id),
            Err(ResourceError::CacheIsBeingCleaned) => {
                // 等待清理完成
                tokio::time::sleep(Duration::from_secs(5)).await;
                continue;
            }
            Err(e) if attempt < max_retries - 1 => {
                log::warn!("安装失败，重试中: {:?}", e);
                tokio::time::sleep(Duration::from_secs(2_u64.pow(attempt))).await;
                continue;
            }
            Err(e) => return Err(e),
        }
    }
    unreachable!()
}
```
