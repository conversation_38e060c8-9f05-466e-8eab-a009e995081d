# Cache模块

Cache模块位于`src/cache`目录下，负责资源数据的持久化存储和缓存管理。该模块使用SQLite数据库作为存储引擎，通过Diesel ORM提供类型安全的数据库操作。

## 模块结构

```
src/cache/
├── database.rs              # 数据库接口和实现
├── resource_record.rs       # 资源记录模型
├── query_record.rs          # 查询记录模型
├── error.rs                 # 数据库错误定义
└── mod.rs
```

## 核心组件

### 1. ResourceDatabase (资源数据库)

`ResourceDatabase`定义了资源数据持久化的抽象接口，支持资源的增删改查操作。

#### 接口定义

```rust
#[mry::mry]
pub trait ResourceDatabase: Any + Send + Sync {
    /// 插入资源记录
    fn insert_resource(&self, resource: &InsertableResource) -> Result<i64, DatabaseError>;
    
    /// 更新资源记录
    fn update_resource(&self, resource: &ResourceRecord) -> Result<(), DatabaseError>;
    
    /// 删除资源记录
    fn delete_resource(&self, resource_id: i64) -> Result<(), DatabaseError>;
    
    /// 根据ID查询资源
    fn get_resource_by_id(&self, resource_id: i64) -> Result<Option<ResourceRecord>, DatabaseError>;
    
    /// 根据名称和类型查询资源
    fn get_resource_by_name_and_type(
        &self,
        name: &str,
        resource_type: &str,
    ) -> Result<Vec<ResourceRecord>, DatabaseError>;
    
    /// 查询所有资源
    fn get_all_resources(&self) -> Result<Vec<ResourceRecord>, DatabaseError>;
    
    /// 清空所有资源
    fn clean_all(&self) -> Result<(), DatabaseError>;
    
    /// 插入查询记录
    fn insert_query(&self, query: &ResourceQuery) -> Result<(), DatabaseError>;
    
    /// 搜索查询记录
    fn search_query(&self, condition: &str) -> Result<HashMap<String, ResourceQuery>, DatabaseError>;
}
```

#### 实现类

```rust
pub struct ResourceDatabaseImpl {
    connection_pool: r2d2::Pool<ConnectionManager<SqliteConnection>>,
}

impl ResourceDatabaseImpl {
    pub fn new(database_url: &str) -> Result<Self, DatabaseError> {
        let manager = ConnectionManager::<SqliteConnection>::new(database_url);
        let pool = r2d2::Pool::builder()
            .max_size(10)
            .build(manager)
            .map_err(|e| DatabaseError::ConnectionError(e.to_string()))?;
        
        Ok(Self {
            connection_pool: pool,
        })
    }
    
    fn get_connection(&self) -> Result<PooledConnection<ConnectionManager<SqliteConnection>>, DatabaseError> {
        self.connection_pool.get()
            .map_err(|e| DatabaseError::ConnectionError(e.to_string()))
    }
}
```

### 2. ResourceRecord (资源记录模型)

`ResourceRecord`定义了资源在数据库中的存储结构。

#### 数据模型

```rust
#[derive(Debug, Clone, Default, Queryable, AsChangeset)]
#[diesel(table_name = resources)]
pub struct ResourceRecord {
    /// 资源ID（主键）
    pub id: i64,
    /// 资源名称
    pub name: String,
    /// 资源版本号
    pub version: String,
    /// 资源类型
    pub resource_type: String,
    /// 创建时间
    pub create_time: i64,
    /// 更新时间
    pub update_time: i64,
    /// 下载URL
    pub download_url: String,
    /// 本地路径
    pub path: Option<String>,
    /// 索引文件路径
    pub index_path: Option<String>,
    /// 是否预置资源
    pub is_preset: bool,
    /// 是否激活
    pub is_active: bool,
    /// 是否服务器最新
    pub is_server_latest: bool,
    /// 是否隐藏状态栏
    pub hide_status_bar: bool,
    /// 是否强制升级
    pub is_force_upgrade: bool,
    /// 资源规则JSON
    pub rules_json: Option<String>,
    /// 远程页面URL
    pub remote_page_url: Option<String>,
    /// 资源状态
    pub status: i32,
    /// 资源哈希值
    pub hash: String,
}
```

#### 数据库表结构

```rust
diesel::table! {
    resources (id) {
        id -> BigInt,
        name -> Text,
        version -> Text,
        resource_type -> Text,
        create_time -> BigInt,
        update_time -> BigInt,
        download_url -> Text,
        path -> Nullable<Text>,
        index_path -> Nullable<Text>,
        is_preset -> Bool,
        is_active -> Bool,
        is_server_latest -> Bool,
        hide_status_bar -> Bool,
        is_force_upgrade -> Bool,
        rules_json -> Nullable<Text>,
        remote_page_url -> Nullable<Text>,
        status -> Integer,
        hash -> Text,
    }
}
```

### 3. 数据库操作实现

#### 资源插入

```rust
impl ResourceDatabase for ResourceDatabaseImpl {
    fn insert_resource(&self, resource: &InsertableResource) -> Result<i64, DatabaseError> {
        use crate::cache::resource_record::resources::dsl::*;
        
        let mut conn = self.get_connection()?;
        
        conn.transaction(|conn| {
            diesel::insert_into(resources)
                .values(resource)
                .execute(conn)?;
            
            // 获取插入的ID
            let inserted_id: i64 = diesel::select(last_insert_rowid)
                .first(conn)?;
            
            Ok(inserted_id)
        }).map_err(|e| DatabaseError::QueryError(e.to_string()))
    }
}
```

#### 资源查询

```rust
impl ResourceDatabase for ResourceDatabaseImpl {
    fn get_resource_by_name_and_type(
        &self,
        name: &str,
        resource_type: &str,
    ) -> Result<Vec<ResourceRecord>, DatabaseError> {
        use crate::cache::resource_record::resources::dsl::*;
        
        let mut conn = self.get_connection()?;
        
        let results = resources
            .filter(name.eq(name))
            .filter(resource_type.eq(resource_type))
            .order(version.desc())
            .load::<ResourceRecord>(&mut conn)
            .map_err(|e| DatabaseError::QueryError(e.to_string()))?;
        
        Ok(results)
    }
}
```

#### 批量操作

```rust
impl ResourceDatabase for ResourceDatabaseImpl {
    fn clean_all(&self) -> Result<(), DatabaseError> {
        use crate::cache::resource_record::resources::dsl::*;
        use crate::cache::query_record::queries::dsl::*;
        
        let mut conn = self.get_connection()?;
        
        conn.transaction(|conn| {
            // 清空资源表
            diesel::delete(resources).execute(conn)?;
            
            // 清空查询表
            diesel::delete(queries).execute(conn)?;
            
            Ok(())
        }).map_err(|e| DatabaseError::QueryError(e.to_string()))
    }
}
```

### 4. 查询记录管理

#### QueryRecord模型

```rust
#[derive(Debug, Clone, Queryable, Insertable)]
#[diesel(table_name = queries)]
pub struct ResourceQuery {
    /// 查询ID
    pub id: Option<i64>,
    /// 关联的资源ID
    pub resource_id: i64,
    /// 查询条件
    pub condition: String,
    /// 来源函数
    pub from_func: String,
    /// 应用版本
    pub app_version: String,
    /// 创建时间
    pub create_time: i64,
    /// 更新时间
    pub update_time: i64,
}
```

#### 查询操作

```rust
impl ResourceDatabase for ResourceDatabaseImpl {
    fn search_query(&self, condition: &str) -> Result<HashMap<String, ResourceQuery>, DatabaseError> {
        use crate::cache::query_record::queries::dsl::*;
        
        let mut conn = self.get_connection()?;
        
        let results = queries
            .filter(condition.eq(condition))
            .load::<ResourceQuery>(&mut conn)
            .map_err(|e| DatabaseError::QueryError(e.to_string()))?;
        
        let mut query_map = HashMap::new();
        for query in results {
            query_map.insert(query.condition.clone(), query);
        }
        
        Ok(query_map)
    }
}
```

## 缓存策略

### 1. 多级缓存架构

```mermaid
graph TB
    subgraph "应用层"
        App[应用请求]
    end
    
    subgraph "内存缓存"
        MC[ResourceInfo缓存]
        QC[查询结果缓存]
    end
    
    subgraph "数据库缓存"
        DB[SQLite数据库]
        RT[resources表]
        QT[queries表]
    end
    
    subgraph "文件系统"
        FS[资源文件]
        TF[临时文件]
    end
    
    App --> MC
    MC --> DB
    DB --> RT
    DB --> QT
    DB --> FS
    FS --> TF
```

### 2. 缓存更新策略

```rust
impl ResourceDatabaseImpl {
    /// 更新资源缓存
    pub fn update_resource_cache(&self, resource: &ResourceRecord) -> Result<(), DatabaseError> {
        // 1. 更新数据库记录
        self.update_resource(resource)?;
        
        // 2. 更新内存缓存
        if let Some(cache) = self.get_memory_cache() {
            cache.insert(resource.id, resource.clone());
        }
        
        // 3. 标记相关查询缓存失效
        self.invalidate_query_cache(&resource.name, &resource.resource_type)?;
        
        Ok(())
    }
    
    /// 批量更新资源
    pub fn batch_update_resources(&self, resources: &[ResourceRecord]) -> Result<(), DatabaseError> {
        let mut conn = self.get_connection()?;
        
        conn.transaction(|conn| {
            for resource in resources {
                diesel::update(resources::table.find(resource.id))
                    .set(resource)
                    .execute(conn)?;
            }
            Ok(())
        }).map_err(|e| DatabaseError::QueryError(e.to_string()))
    }
}
```

### 3. 缓存清理策略

```rust
impl ResourceDatabaseImpl {
    /// 清理过期资源
    pub fn cleanup_expired_resources(&self, retention_days: i64) -> Result<usize, DatabaseError> {
        use crate::cache::resource_record::resources::dsl::*;
        
        let mut conn = self.get_connection()?;
        let cutoff_time = self.get_current_time() - (retention_days * 24 * 60 * 60 * 1000);
        
        let deleted_count = diesel::delete(
            resources.filter(
                update_time.lt(cutoff_time)
                    .and(is_active.eq(false))
                    .and(is_preset.eq(false))
            )
        ).execute(&mut conn)
        .map_err(|e| DatabaseError::QueryError(e.to_string()))?;
        
        Ok(deleted_count)
    }
    
    /// 清理孤立的查询记录
    pub fn cleanup_orphaned_queries(&self) -> Result<usize, DatabaseError> {
        use crate::cache::query_record::queries::dsl::*;
        
        let mut conn = self.get_connection()?;
        
        let deleted_count = diesel::delete(
            queries.filter(
                resource_id.not_in(
                    resources::table.select(resources::id)
                )
            )
        ).execute(&mut conn)
        .map_err(|e| DatabaseError::QueryError(e.to_string()))?;
        
        Ok(deleted_count)
    }
}
```

## 数据迁移

### 1. 数据库初始化

```rust
impl ResourceDatabaseImpl {
    pub fn initialize_database(&self) -> Result<(), DatabaseError> {
        let mut conn = self.get_connection()?;
        
        // 创建资源表
        diesel::sql_query(
            r#"
            CREATE TABLE IF NOT EXISTS resources (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                version TEXT NOT NULL,
                resource_type TEXT NOT NULL,
                create_time INTEGER NOT NULL,
                update_time INTEGER NOT NULL,
                download_url TEXT NOT NULL,
                path TEXT,
                index_path TEXT,
                is_preset BOOLEAN NOT NULL DEFAULT 0,
                is_active BOOLEAN NOT NULL DEFAULT 0,
                is_server_latest BOOLEAN NOT NULL DEFAULT 0,
                hide_status_bar BOOLEAN NOT NULL DEFAULT 0,
                is_force_upgrade BOOLEAN NOT NULL DEFAULT 0,
                rules_json TEXT,
                remote_page_url TEXT,
                status INTEGER NOT NULL DEFAULT 0,
                hash TEXT NOT NULL
            )
            "#
        ).execute(&mut conn)
        .map_err(|e| DatabaseError::QueryError(e.to_string()))?;
        
        // 创建查询表
        diesel::sql_query(
            r#"
            CREATE TABLE IF NOT EXISTS queries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                resource_id INTEGER NOT NULL,
                condition TEXT NOT NULL,
                from_func TEXT NOT NULL,
                app_version TEXT NOT NULL,
                create_time INTEGER NOT NULL,
                update_time INTEGER NOT NULL,
                FOREIGN KEY (resource_id) REFERENCES resources (id)
            )
            "#
        ).execute(&mut conn)
        .map_err(|e| DatabaseError::QueryError(e.to_string()))?;
        
        // 创建索引
        self.create_indexes(&mut conn)?;
        
        Ok(())
    }
    
    fn create_indexes(&self, conn: &mut SqliteConnection) -> Result<(), DatabaseError> {
        // 资源表索引
        diesel::sql_query("CREATE INDEX IF NOT EXISTS idx_resources_name_type ON resources (name, resource_type)")
            .execute(conn)?;
        diesel::sql_query("CREATE INDEX IF NOT EXISTS idx_resources_active ON resources (is_active)")
            .execute(conn)?;
        diesel::sql_query("CREATE INDEX IF NOT EXISTS idx_resources_preset ON resources (is_preset)")
            .execute(conn)?;
        
        // 查询表索引
        diesel::sql_query("CREATE INDEX IF NOT EXISTS idx_queries_condition ON queries (condition)")
            .execute(conn)?;
        diesel::sql_query("CREATE INDEX IF NOT EXISTS idx_queries_resource_id ON queries (resource_id)")
            .execute(conn)?;
        
        Ok(())
    }
}
```

### 2. 数据库版本管理

```rust
impl ResourceDatabaseImpl {
    const CURRENT_VERSION: i32 = 2;
    
    pub fn migrate_database(&self) -> Result<(), DatabaseError> {
        let current_version = self.get_database_version()?;
        
        if current_version < Self::CURRENT_VERSION {
            self.perform_migration(current_version, Self::CURRENT_VERSION)?;
        }
        
        Ok(())
    }
    
    fn perform_migration(&self, from_version: i32, to_version: i32) -> Result<(), DatabaseError> {
        let mut conn = self.get_connection()?;
        
        conn.transaction(|conn| {
            for version in (from_version + 1)..=to_version {
                match version {
                    2 => self.migrate_to_v2(conn)?,
                    _ => return Err(DatabaseError::MigrationError(
                        format!("Unknown migration version: {}", version)
                    )),
                }
            }
            
            self.set_database_version(to_version, conn)?;
            Ok(())
        }).map_err(|e| DatabaseError::MigrationError(e.to_string()))
    }
    
    fn migrate_to_v2(&self, conn: &mut SqliteConnection) -> Result<(), diesel::result::Error> {
        // 添加新字段
        diesel::sql_query("ALTER TABLE resources ADD COLUMN hash TEXT DEFAULT ''")
            .execute(conn)?;
        
        // 更新现有记录的哈希值
        diesel::sql_query("UPDATE resources SET hash = '' WHERE hash IS NULL")
            .execute(conn)?;
        
        Ok(())
    }
}
```

## 错误处理

### 错误类型定义

```rust
#[derive(Debug, thiserror::Error)]
pub enum DatabaseError {
    #[error("连接错误: {0}")]
    ConnectionError(String),
    
    #[error("查询错误: {0}")]
    QueryError(String),
    
    #[error("事务错误: {0}")]
    TransactionError(String),
    
    #[error("迁移错误: {0}")]
    MigrationError(String),
    
    #[error("数据验证错误: {0}")]
    ValidationError(String),
}
```

## 最佳实践

### 1. 连接池管理

```rust
// 合理配置连接池
let pool = r2d2::Pool::builder()
    .max_size(10)                    // 最大连接数
    .min_idle(Some(2))               // 最小空闲连接
    .connection_timeout(Duration::from_secs(30))  // 连接超时
    .idle_timeout(Some(Duration::from_secs(600))) // 空闲超时
    .build(manager)?;
```

### 2. 事务使用

```rust
// 使用事务保证数据一致性
conn.transaction(|conn| {
    // 多个相关操作
    self.insert_resource(resource, conn)?;
    self.insert_query(query, conn)?;
    self.update_cache(resource, conn)?;
    Ok(())
})?;
```

### 3. 批量操作

```rust
// 批量插入提高性能
pub fn batch_insert_resources(&self, resources: &[InsertableResource]) -> Result<(), DatabaseError> {
    let mut conn = self.get_connection()?;
    
    conn.transaction(|conn| {
        diesel::insert_into(resources::table)
            .values(resources)
            .execute(conn)?;
        Ok(())
    }).map_err(|e| DatabaseError::QueryError(e.to_string()))
}
```

### 4. 查询优化

```rust
// 使用索引优化查询
fn get_active_resources_by_type(&self, resource_type: &str) -> Result<Vec<ResourceRecord>, DatabaseError> {
    use crate::cache::resource_record::resources::dsl::*;
    
    let mut conn = self.get_connection()?;
    
    // 利用复合索引
    let results = resources
        .filter(resource_type.eq(resource_type))
        .filter(is_active.eq(true))
        .order(update_time.desc())
        .load::<ResourceRecord>(&mut conn)?;
    
    Ok(results)
}
```
