namespace com.haier.uhome.uplus.rust.updevice.fbs;

table Int32Wrapper {
    value:int;
}

table BoolWrapper {
    value:bool;
}

table StrWrapper {
    value:string;
}

table NoneWrapper {
}

union UpDeviceContainer {
    Int32Wrapper,
    <PERSON>r<PERSON>rapper,
    <PERSON><PERSON><PERSON>rapper,
    NoneWrapper,
}

table UpDeviceFlat {
    container:UpDeviceContainer;
    code:string;
    error: string;
}

root_type UpDeviceFlat;