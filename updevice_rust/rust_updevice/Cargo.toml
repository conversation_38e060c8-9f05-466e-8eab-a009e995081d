[package]
name = "rust_updevice"
version = "0.1.0"
edition = "2021"

[lib]
name = "rust_updevice"
crate-type = ["cdylib", "staticlib", "rlib"]

[features]
default = []
usdk = []
android = ["rust_usdk/android"]
ohos = ["dep:napi-ohos", "dep:napi-derive-ohos", "rust_usdk/ohos"]
ios = ["rust_usdk/ios"]

[dependencies]
napi-ohos = { workspace = true, features = ["napi4", "compat-mode", "tokio_rt"], optional = true }
napi-derive-ohos = { workspace = true, optional = true }
once_cell = { workspace = true }
parking_lot = { workspace = true }
uuid = { workspace = true, features = ["v4"] }
log = { workspace = true }
tokio = { workspace = true, features = [
    "rt",
    "rt-multi-thread",
    "parking_lot",
    "macros",
    "time",
    "sync"
] }
derive_builder = { workspace = true }
async-trait = { workspace = true }
thiserror = { workspace = true }
libc = { workspace = true }
dashmap = { workspace = true }
chrono = { workspace = true }
mry = { workspace = true }
mockall = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
async-recursion = { workspace = true }
regex = { workspace = true }
flatbuffers = { workspace = true }
logic_engine = { path = "../../logic_engine_rust/rust_logicEngine" }
task_manager = { path = "../../task_manager_rust/task_manager_rust" }
rust_storage = { path = "../../storage_rust/rust_storage" }
rust_userdomain = { path = "../../userdomain_rust/rust_userdomain" }
rust_resource = { path = "../../resource_rust/rust_resource" }
rust_usdk = { path = "../../usdk_rust/rust_usdk" }

[build-dependencies]
napi-build-ohos = { workspace = true }
cc = { workspace = true }

[dev-dependencies]
cucumber = { workspace = true }
futures = { workspace = true }
env_logger = { workspace = true }

[[test]]
name = "cucumber" # this should be the same as the filename of your test target
harness = false
