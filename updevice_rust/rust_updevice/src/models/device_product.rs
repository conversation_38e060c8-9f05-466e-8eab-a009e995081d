use std::collections::HashMap;
use derive_builder::Builder;
use serde::{Deserialize, Serialize};

#[derive(Default, Clone, Debug, Serialize, Deserialize, Builder)]
#[serde(rename_all = "camelCase", default)]
pub struct UpDeviceProduct {
    #[builder(default)]
    bar_code: Option<String>,
    #[builder(default)]
    brand: String,
    #[builder(default)]
    category: String,
    #[builder(default)]
    category_code: String,
    #[builder(default)]
    category_grouping: String,
    #[builder(default)]
    two_grouping_name: String,
    #[builder(default)]
    device_type: Option<String>,
    #[builder(default)]
    image_url: String,
    #[builder(default)]
    bind_type: Option<String>,
    #[builder(default)]
    access_type: Option<String>,
    #[builder(default)]
    communication_mode: Option<String>,
    #[builder(default)]
    config_type: String,
    #[builder(default)]
    app_type_icon: String,
    #[builder(default = false)]
    no_keep_alive: bool,
    #[builder(default)]
    card_page_icon: String,
    #[builder(default)]
    card_page_img: String,
    #[builder(default = 0)]
    small_card_sort: u64,
    #[builder(default = 0)]
    large_card_sort: u64,
    #[builder(default = 0)]
    card_sort:u64,
    #[builder(default = 0)]
    card_status:u64,
    #[builder(default)]
    aggregation_parent_id:String,
    #[builder(default = 0)]
    support_aggregation_flag:u64,
    #[builder(default = false)]
    shared_device_flag:bool,
    #[builder(default = HashMap::new())]
    card_info_map:HashMap<String, UpDeviceShareCardInfo>,
    #[builder(default = false)]
    support_shared:bool,
    #[builder(default = 0)]
    re_bind:u64,
}

impl UpDeviceProduct {
    pub fn empty() -> Self {
        UpDeviceProduct::default()
    }
    pub fn bar_code(&self) -> Option<String> {
        self.bar_code.clone()
    }
    pub fn brand(&self) -> String {
        self.brand.clone()
    }
    pub fn category(&self) -> String {
        self.category.clone()
    }
    pub fn category_code(&self) -> String {
        self.category_code.clone()
    }
    pub fn category_grouping(&self) -> String {
        self.category_grouping.clone()
    }
    pub fn device_type(&self) -> Option<String> {
        self.device_type.clone()
    }
    pub fn image_url(&self) -> String {
        self.image_url.clone()
    }
    pub fn bind_type(&self) -> Option<String> {
        self.bind_type.clone()
    }
    pub fn access_type(&self) -> Option<String> {
        self.access_type.clone()
    }
    pub fn communication_mode(&self) -> Option<String> {
        self.communication_mode.clone()
    }
    pub fn config_type(&self) -> String {
        self.config_type.clone()
    }
    pub fn app_type_icon(&self) -> String {
        self.app_type_icon.clone()
    }
    pub fn no_keep_alive(&self) -> bool {
        self.no_keep_alive
    }
    pub fn card_sort(&self) -> u64 {
        self.card_sort
    }
    pub fn card_status(&self) -> u64 {
        self.card_status
    }
    pub fn aggregation_parent_id(&self) -> String {
        self.aggregation_parent_id.clone()
    }
    pub fn support_aggregation_flag(&self) -> u64 {
        self.support_aggregation_flag
    }
    pub fn shared_device_flag(&self) -> bool { self.shared_device_flag }
    pub fn card_info_map(&self) -> HashMap<String, UpDeviceShareCardInfo> { self.card_info_map.clone() }
    pub fn support_shared_device_flag(&self) -> bool { self.support_shared }
    pub fn re_bind(&self) -> u64 { self.re_bind}
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Default)]
#[serde(rename_all = "camelCase", default)]
pub struct UpDeviceShareCardInfo {
    pub family_id: String,
    pub card_sort: u64,
    pub card_status: u64,
}