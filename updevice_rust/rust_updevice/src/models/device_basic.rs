use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct UpDeviceBasic {
    display_name: String,
    app_type_name: String,
    app_type_code: String,
    room_name: String,
    room_id: String,
    online: bool,
    sub_device_ids: Vec<String>,
    device_role: Option<String>,
    device_role_type: Option<String>,
    device_net_type: Option<String>,
    device_group_id: Option<String>,
    device_group_type: Option<String>,
    bind_time: String,
    device_aggregate_type:String,
    attachment_sort_code:u64,
}

impl UpDeviceBasic {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        display_name: String,
        app_type_name: String,
        app_type_code: String,
        room_name: String,
        room_id: String,
        online: bool,
        sub_device_ids: Vec<String>,
        device_role: Option<String>,
        device_role_type: Option<String>,
        device_net_type: Option<String>,
        device_group_id: Option<String>,
        device_group_type: Option<String>,
        bind_time: String,
        device_aggregate_type:String,
        attachment_sort_code:u64,
    ) -> Self {
        Self {
            display_name,
            app_type_name,
            app_type_code,
            room_name,
            room_id,
            online,
            sub_device_ids,
            device_role,
            device_role_type,
            device_net_type,
            device_group_id,
            device_group_type,
            bind_time,
            device_aggregate_type,
            attachment_sort_code,
        }
    }
    
    pub fn empty() -> Self {
        Self {
            display_name: "".to_string(),
            app_type_name: "".to_string(),
            app_type_code: "".to_string(),
            room_name: "".to_string(),
            room_id: "".to_string(),
            online: false,
            sub_device_ids: vec![],
            device_role: None,
            device_role_type: None,
            device_net_type: None,
            device_group_id: None,
            device_group_type: None,
            bind_time: "".to_string(),
            device_aggregate_type: "".to_string(),
            attachment_sort_code: 0,
        }
    }

    pub fn display_name(&self) -> String {
        self.display_name.clone()
    }
    pub fn app_type_name(&self) -> String {
        self.app_type_name.clone()
    }
    pub fn app_type_code(&self) -> String {
        self.app_type_code.clone()
    }
    pub fn room_name(&self) -> String {
        self.room_name.clone()
    }
    pub fn room_id(&self) -> String {
        self.room_id.clone()
    }
    pub fn online(&self) -> bool {
        self.online
    }
    pub fn sub_device_ids(&self) -> Vec<String> {
        self.sub_device_ids.clone()
    }
    pub fn device_role(&self) -> Option<String> {
        self.device_role.clone()
    }
    pub fn device_role_type(&self) -> Option<String> {
        self.device_role_type.clone()
    }
    pub fn device_net_type(&self) -> Option<String> {
        self.device_net_type.clone()
    }
    pub fn device_group_id(&self) -> Option<String> {
        self.device_group_id.clone()
    }
    pub fn device_group_type(&self) -> Option<String> {
        self.device_group_type.clone()
    }
    pub fn bind_time(&self) -> String {
        self.bind_time.clone()
    }
    pub fn device_aggregate_type(&self) -> String {
        self.device_aggregate_type.clone()
    }
}