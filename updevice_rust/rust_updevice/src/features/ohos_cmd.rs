use log::{debug, info};
use std::collections::HashMap;
use std::sync::Arc;
use std::vec;

use logic_engine::device::command::Command;
use logic_engine::device::device_attribute::UpDeviceAttribute;
use logic_engine::device::device_command::UpDeviceCommand;
use logic_engine::device_config::business_attr::BusinessAttr;
use logic_engine::device_config::modifier::Modifier;
use logic_engine::engine::attribute::LEAttribute;
use logic_engine::engine::caution::LECaution;
use napi_ohos::bindgen_prelude::Function;
use napi_ohos::threadsafe_function::{ThreadsafeFunction, ThreadsafeFunctionCallMode};
use napi_ohos::{Env, Error, JsObject, JsString, JsUnknown, Result, Status};
use rust_usdk::features::models::CreateDeviceGroupResult;
use rust_usdk::toolkit_ffi::safe::CError;
use rust_usdk::toolkit_ffi::uhsd_device_group::device_group_model::{GroupDeviceAddDelReqInfo, GroupDeviceBaseReqInfo, GroupDeviceReqInfo};
use rust_usdk::toolkit_ffi::uhsd_manager::UhsdManager;
use serde::Serialize;
use serde_json::Value;
use task_manager::common_runtime::get_runtime;
use task_manager::task_manager::get_task_manager;
use tokio::runtime;
use rust_usdk::toolkit_ffi::uhsd_usr_model::DeviceFeedback;
use rust_usdk::usdk_toolkit::error::ToolkitError;
use rust_usdk::usdk_utils::constants::{DEVICE_GROUP_LOG_PREFIX, DEVICE_GROUP_TIMEOUT_ADD_DEL_DEVICES, UHSD_DEFAULT_GROUP_DEVICE_TIMEOUT};
use rust_userdomain::api::device::Device;
use rust_userdomain::api::user_domain_manager::UserDomainManager;
use crate::api::device_filter::DeviceFilterByDeviceIds;
use crate::api::device_injection::UpDeviceInjection;
use crate::api::device_manager::UpDeviceManager;
use crate::api::event::UpDeviceEvent;
use crate::device::up_device::UpDevice;
use crate::device::extend_api::ExtendApi;
use crate::features::constant::platform_code;
use crate::features::ffi_models::{UpDeviceModel, RESULT_BOOLEAN_VALUE, RESULT_STRING_VALUE};
use crate::models::device_basic::UpDeviceBasic;
use crate::models::device_connect_state::UpDeviceConnectState;
use crate::models::device_info::UpDeviceInfo;
use crate::models::device_offline_cause::UpDeviceOfflineCause;
use crate::models::device_online_state::UpDeviceOnlineState;
use crate::models::device_permission::UpDevicePermission;
use crate::models::device_product::UpDeviceProduct;
use crate::models::device_relation::UpDeviceRelation;
use crate::models::device_sleep_state::UpDeviceSleepState;
use crate::utils::fn_clone::FnClone;

impl From<&Arc<dyn UpDevice>> for UpDeviceModel {
    fn from(it: &Arc<dyn UpDevice>) -> Self {
        it.as_ref().into()
    }
}

impl From<Arc<dyn UpDevice>> for UpDeviceModel {
    fn from(it: Arc<dyn UpDevice>) -> Self {
        it.as_ref().into()
    }
}

impl From<&dyn UpDevice> for UpDeviceModel {
    fn from(it: &dyn UpDevice) -> Self {
        UpDeviceModel {
            protocol: it.protocol(),
            device_id: it.device_id(),
            device_info: it.get_info(),
            attribute_list: it.get_attribute_list(),
            caution_list: it.get_caution_list(),
            engine_attribute_list: vec![],
            engine_caution_list: vec![],
            config_state: it.get_config_state(),
            connect_state: it.get_connect_state(),
            ble_state: it.get_ble_state(),
            online_state: it.get_online_state(),
            sleep_state: it.get_sleep_state(),
            state_code: it.get_state_code(),
            offline_cause: it.get_offline_cause(),
            offline_days: it.get_offline_days(),
        }
    }
}

pub fn cmd_get_device_list_by_family_id(env: Env, args: JsObject) -> Result<JsObject> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let with_engine_attributes: bool = args
        .get("with_engine_attributes")?
        .unwrap_or_else(|| true);
    debug!("cmd_get_device_list_by_family_id with_engine_attributes: {}", with_engine_attributes);
    let list: Vec<UpDeviceModel> = get_device_manager()
        .get_device_list_by_family_id(family_id)
        .iter()
        .map(|it| {
            let mut model: UpDeviceModel = it.into();
            if with_engine_attributes {
                append_engine_attributes(&mut model, it.get_extend_api());
                append_engine_cautions(&mut model, it.get_extend_api());
            }
            model
        })
        .collect();

    to_js_object_str(env, to_json(&list)?.as_str())
}

pub fn cmd_get_device_info_list_by_family_id(env: Env, args: JsObject) -> Result<JsObject> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let info = get_device_manager().get_device_info_list(family_id.as_str());
    to_js_object_str(env, to_json(&info)?.as_str())
}

pub fn cmd_get_device_by_id(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let with_engine_attributes: bool = args
        .get("with_engine_attributes")?
        .unwrap_or_else(|| true);
    debug!("cmd_get_device_by_id with_engine_attributes: {}", with_engine_attributes);
    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api: Arc<dyn ExtendApi> = it.get_extend_api();
            let mut model: UpDeviceModel = it.into();
            if with_engine_attributes {
                append_engine_attributes(&mut model, extend_api.clone());
                append_engine_cautions(&mut model, extend_api.clone());
            }
            to_js_object_str(env, to_json(&model)?.as_str())
        }
    }
}

pub fn cmd_get_device_info_by_id(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let device_info = get_device_manager().get_device_info(device_id.as_str());
    match device_info {
        None => new_js_object(env),
        Some(it) => to_js_object_str(env, to_json(&it)?.as_str()),
    }
}

pub fn cmd_get_sub_device_list_by_parent_id(env: Env, args: JsObject) -> Result<JsObject> {
    let parent_id: String = args
        .get("parent_id")?
        .ok_or(Error::new(Status::InvalidArg, "need parent_id"))?;
    let list: Vec<UpDeviceModel> = get_device_manager()
        .get_sub_device_list_by_parent_id(parent_id.as_str())
        .into_iter()
        .map(|it| it.into())
        .collect();
    to_js_object_str(env, to_json(&list)?.as_str())
}

pub fn cmd_get_sub_device_list_by_sub_device_id(env: Env, args: JsObject) -> Result<JsObject> {
    let sub_device_id: String = args
        .get("sub_device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need sub_device_id"))?;
    let list: Vec<UpDeviceModel> = get_device_manager()
        .get_sub_device_list_by_sub_device_id(sub_device_id.as_str())
        .into_iter()
        .map(|it| it.into())
        .collect();
    to_js_object_str(env, to_json(&list)?.as_str())
}

pub fn cmd_subscribe_device_list_change(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    
    if !UpDeviceInjection::get_instance().is_initialized() {
        debug!("uhsd: cmd_subscribe_device_list_change method can't called before initializing");
        return to_js_object_str(env, "");
    }
    
    let immediate: bool = args
        .get("immediate")?
        .ok_or(Error::new(Status::InvalidArg, "need immediate"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    let id = get_device_manager().subscribe_device_list_change(immediate, move |event| {
        match event {
            UpDeviceEvent::DeviceListChanged(_) => {
                tsfn.call(
                    vec!["0".to_owned(), "[]".to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            _ => {}
        }
    });
    to_js_object_str(env, id.as_str())
}

pub fn cmd_unsubscribe_device_list_change(env: Env, args: JsObject) -> Result<JsObject> {
    let subscription_id: String = args
        .get("subscription_id")?
        .ok_or(Error::new(Status::InvalidArg, "need subscription_id"))?;
    get_device_manager().unsubscribe_device_list_change(subscription_id.as_str());
    to_js_object_bool(env, true)
}

pub fn cmd_subscribe_device_change(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    let subscribe_list = vec![device_id];
    let id = get_device_manager()
        .subscribe_device_change_by_devices(subscribe_list, create_device_listener(tsfn));
    to_js_object_str(env, id.as_str())
}

pub fn cmd_unsubscribe_device_change(env: Env, args: JsObject) -> Result<JsObject> {
    let subscription_id: String = args
        .get("subscription_id")?
        .ok_or(Error::new(Status::InvalidArg, "need subscription_id"))?;
    get_device_manager().unsubscribe_device_change_by_devices(subscription_id.as_str());
    to_js_object_bool(env, true)
}

pub fn cmd_subscribe_device_change_by_family_id(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    let id = get_device_manager()
        .subscribe_device_change_by_family_id(family_id, create_device_listener(tsfn));
    to_js_object_str(env, id.as_str())
}

fn create_device_listener(
    tsfn: ThreadsafeFunction<Vec<String>, JsUnknown, Vec<String>, false>,
) -> Box<dyn Fn(UpDeviceEvent) + Send + Sync> {
    Box::new(move |event| {
        let action = event.action().to_string();
        match event {
            UpDeviceEvent::AttributeChanged { device_id } => {
                let list = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(vec![], |it| it.get_attribute_list());
                match to_json(&list) {
                    Ok(json) => {
                        tsfn.call(
                            vec![action, platform_code::SUCCESS.to_string(), device_id, json],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Err(e) => {
                        tsfn.call(
                            vec![
                                action,
                                platform_code::SERDE_JSON_ERR.to_string(),
                                device_id,
                                e.to_string(),
                            ],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                }
            }
            UpDeviceEvent::CautionChanged { device_id } => {
                let list = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(vec![], |it| it.get_caution_list());
                match to_json(&list) {
                    Ok(json) => {
                        tsfn.call(
                            vec![action, platform_code::SUCCESS.to_string(), device_id, json],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Err(e) => {
                        tsfn.call(
                            vec![
                                action,
                                platform_code::SERDE_JSON_ERR.to_string(),
                                device_id,
                                e.to_string(),
                            ],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                }
            }
            UpDeviceEvent::ConnectStateChanged { device_id } => {
                let state = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceConnectState::default(), |it| it.get_connect_state());
                tsfn.call(
                    vec![action, device_id, state.to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            UpDeviceEvent::OnlineStateChanged { device_id } => {
                let state = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceOnlineState::default(), |it| it.get_online_state());
                tsfn.call(
                    vec![action, device_id, state.to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            UpDeviceEvent::BaseInfoChanged { device_id } => {
                let info = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceInfo::default(), |it| it.get_info());
                match to_json(&info) {
                    Ok(json) => {
                        tsfn.call(
                            vec![action, platform_code::SUCCESS.to_string(), device_id, json],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Err(e) => {
                        tsfn.call(
                            vec![
                                action,
                                platform_code::SERDE_JSON_ERR.to_string(),
                                device_id,
                                e.to_string(),
                            ],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                }
            }
            UpDeviceEvent::SleepStateChanged { device_id} => {
                let state = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceSleepState::default(), |it| it.get_sleep_state());
                tsfn.call(
                    vec![action, device_id, state.to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            UpDeviceEvent::OfflineCauseChanged { device_id } => {
                let cause = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(UpDeviceOfflineCause::default(), |it| it.get_offline_cause());
                tsfn.call(
                    vec![action, device_id, cause.to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            UpDeviceEvent::OfflineDaysChanged { device_id } => {
                let days = get_device_manager()
                    .get_device(device_id.as_str())
                    .map_or(0, |it| it.get_offline_days());
                tsfn.call(
                    vec![action, device_id, days.to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            _ => {}
        };
    })
}

pub fn cmd_unsubscribe_device_change_by_family_id(env: Env, args: JsObject) -> Result<JsObject> {
    let subscription_id: String = args
        .get("subscription_id")?
        .ok_or(Error::new(Status::InvalidArg, "need subscription_id"))?;
    get_device_manager().unsubscribe_device_change_by_family_id(subscription_id.as_str());
    to_js_object_bool(env, true)
}

pub fn cmd_subscribe_device_change_by_device_ids(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let device_ids: Vec<String> = args
        .get("device_ids")?
        .ok_or(Error::new(Status::InvalidArg, "need device_ids"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    let id = get_device_manager()
        .subscribe_device_change_by_devices(device_ids, create_device_listener(tsfn));
    to_js_object_str(env, id.as_str())
}

pub fn cmd_unsubscribe_device_change_by_device_ids(env: Env, args: JsObject) -> Result<JsObject> {
    let subscription_id: String = args
        .get("subscription_id")?
        .ok_or(Error::new(Status::InvalidArg, "need subscription_id"))?;
    get_device_manager().unsubscribe_device_change_by_devices(subscription_id.as_str());
    to_js_object_bool(env, true)
}

pub fn cmd_update_device_list(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let is_use_cache: bool = args
        .get("is_use_cache")?
        .ok_or(Error::new(Status::InvalidArg, "need is_use_cache"))?;
    let family_id: Option<String> = args
        .get("family_id")?
        .unwrap_or_else(|| None);
    let with_engine_attributes: bool = args
        .get("with_engine_attributes")?
        .unwrap_or_else(|| true);
    info!("cmd_update_device_list by family_id: {:?} with_engine_attributes: {}", family_id, with_engine_attributes);
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match get_device_manager().update_device_list(is_use_cache).await {
            Ok(it) => {
                let mut devices = it;
                if family_id.is_some() {
                    devices = UpDeviceInjection::get_instance().get_device_manager()
                        .get_device_list_by_family_id(family_id.unwrap());
                }
                let mut list: Vec<UpDeviceModel> = Vec::with_capacity(devices.len());
                
                for device in devices.iter() {
                    let extend_api: Arc<dyn ExtendApi> = device.get_extend_api();
                    let mut model: UpDeviceModel = device.into();
                    if with_engine_attributes {
                        let attributes: Vec<Arc<LEAttribute>> = extend_api.query_attributes().await;
                        let engine_attribute_list = attributes
                            .iter()
                            .map(|it: &Arc<LEAttribute>| it.into())
                            .collect();
                        model.set_engine_attribute_list(engine_attribute_list);
                        
                        let engine_caution_list = extend_api.query_cautions().await;
                        model.set_engine_caution_list(engine_caution_list);
                    }
                    list.push(model);
                }
                
                match to_json(&list) {
                    Ok(json) => {
                        tsfn.call(
                            vec!["0".to_owned(), json],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Err(e) => {
                        tsfn.call(
                            vec!["100".to_owned(), e.to_string()],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                }
            }
            Err(e) => {
                info!("cmd_update_device_list rust method call failed: {:?}", e);
                tsfn.call(
                    vec!["101".to_owned(), e.to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}

pub fn cmd_execute_command(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;

    // let pair_map: HashMap<String, String> = args
    //     .get("pair_map")?
    //     .ok_or(Error::new(Status::InvalidArg, "need pair_map"))?;
    let pair_map: String = args
        .get("pair_map")?
        .ok_or(Error::new(Status::InvalidArg, "need pair_map"))?;
    let pair_map: HashMap<String, Value> = serde_json::from_str(&pair_map)
        .map_err(|e| Error::new(Status::ObjectExpected, e.to_string()))?;
    debug!("execute_command before: {:?}", pair_map);
    let group_name: Option<String> = args.get("group_name")?;
    let tsfn = function.build_threadsafe_function().build()?;
    let attributes = pair_map
        .iter()
        .map(|(k, v)| UpDeviceAttribute::new(k.to_owned(), get_value(v)))
        .collect();
    let cmd = UpDeviceCommand::new(group_name, attributes);
    debug!("execute_command after: {:?}", cmd);
    match get_device_manager().get_device(device_id.as_str()) {
        None => to_js_object_str(env, "device not found"),
        Some(device) => {
            get_task_manager().spawn(async move {
                let result = device.execute_command(cmd).await;
                match result {
                    Ok(it) => match to_json(&it) {
                        Ok(json) => {
                            tsfn.call(
                                vec![platform_code::SUCCESS.to_string(), json],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                        Err(e) => {
                            tsfn.call(
                                vec![platform_code::SERDE_JSON_ERR.to_string(), e.to_string()],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                    },
                    Err(e) => {
                        tsfn.call(
                            vec![platform_code::DEVICE_ERR.to_string(), e.to_string()],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                }
            });
            new_js_object(env)
        }
    }
}

fn get_value(value: &Value) -> String {
    match value {
        Value::String(v) => v.to_owned(),
        _ => String::default(),
    }
}

////////////////////////////// ExtendApi Start //////////////////////////////

pub fn cmd_get_initial_attribute_list(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;

    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api = it.get_extend_api();

            let rt = runtime::Runtime::new()?;
            let attrs = rt.block_on(async move {
                let attr_list: Vec<Arc<LEAttribute>> =
                    extend_api.query_initial_attributes().await;
                attr_list
            });
            let json = to_vec_arc_json(&attrs)?;
            to_js_object_str(env, &json)
        }
    }
}

pub fn cmd_get_attribute_list(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;

    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api = it.get_extend_api();

            let rt = runtime::Runtime::new()?;
            let attrs = rt.block_on(async move {
                let attr_list: Vec<Arc<LEAttribute>> = extend_api.query_attributes().await;
                attr_list
            });

            let json = to_vec_arc_json(&attrs)?;
            to_js_object_str(env, &json)
        }
    }
}

pub fn cmd_get_attribute_by_name(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let name: String = args
        .get("name")?
        .ok_or(Error::new(Status::InvalidArg, "need name"))?;

    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api = it.get_extend_api();

            let rt = runtime::Runtime::new()?;
            let option_attr = rt.block_on(async move {
                let option_attr: Option<Arc<LEAttribute>> =
                    extend_api.query_attribute_by_name(name.as_str()).await;
                option_attr
            });
            if let Some(attr) = option_attr {
                let json = to_arc_json(&attr)?;
                to_js_object_str(env, &json)
            } else {
                new_js_object(env)
            }
        }
    }
}

pub fn cmd_get_caution_list(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;

    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api = it.get_extend_api();

            let rt = runtime::Runtime::new()?;
            let cautions = rt.block_on(async move {
                let cautions: Vec<LECaution> = extend_api.query_cautions().await;
                cautions
            });
            let json = to_json(&cautions)?;
            to_js_object_str(env, &json)
        }
    }
}

pub fn cmd_get_modifier_config(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;

    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api = it.get_extend_api();

            let rt = runtime::Runtime::new()?;
            let modifiers = rt.block_on(async move {
                let modifiers: Vec<Modifier> = extend_api.query_modifier_configs().await;
                modifiers
            });
            let json = to_json(&modifiers)?;
            to_js_object_str(env, &json)
        }
    }
}

pub fn cmd_get_business_function_list(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;

    match get_device_manager().get_device(device_id.as_str()) {
        None => new_js_object(env),
        Some(it) => {
            let extend_api = it.get_extend_api();

            let rt = runtime::Runtime::new()?;
            let business_attr_list = rt.block_on(async move {
                let business_attr_list: Vec<BusinessAttr> =
                    extend_api.query_business_functions().await;
                business_attr_list
            });
            let json = to_json(&business_attr_list)?;
            to_js_object_str(env, &json)
        }
    }
}

pub fn cmd_refresh_attributes(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match get_device_manager().get_device(device_id.as_str()) {
            None => {
                tsfn.call(
                    vec!["101".to_string(), "Don't find UpDevice".to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(it) => {
                it.refresh_attributes().await;
                tsfn.call(
                    vec!["0".to_string(), "Success".to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}

pub fn cmd_operate(env: Env, args: JsObject, function: Function<Vec<String>>) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let commands: String = args
        .get("commands")?
        .ok_or(Error::new(Status::InvalidArg, "need commands"))?;
    let command_list = serde_json::from_str::<Vec<Command>>(commands.as_str())
        .map_err(|e| Error::new(Status::ObjectExpected, e.to_string()))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match get_device_manager().get_device(device_id.as_str()) {
            None => {
                tsfn.call(
                    vec!["101".to_string(), "Don't find UpDevice".to_string()],
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(it) => match it.operate(command_list).await {
                Ok(fb) => {
                    tsfn.call(
                        vec![
                            "0".to_string(),
                            fb.device_id,
                            fb.req_sn.to_string(),
                            fb.error_info.to_string(),
                        ],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(e) => {
                    tsfn.call(
                        vec!["100".to_string(), e.to_string()],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    new_js_object(env)
}

pub fn cmd_operate_command_queue(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let commands: String = args
        .get("commands")?
        .ok_or(Error::new(Status::InvalidArg, "need commands"))?;
    let command_list = serde_json::from_str::<Vec<Command>>(commands.as_str())
        .map_err(|e| Error::new(Status::ObjectExpected, e.to_string()))?;
    let tsfn = function.build_threadsafe_function().build()?;
    match get_device_manager().get_device(device_id.as_str()) {
        None => {
            tsfn.call(
                vec!["101".to_string(), "Don't find UpDevice".to_string()],
                ThreadsafeFunctionCallMode::Blocking,
            );
        }
        Some(it) => {
            it.operate_command_queue(
                command_list,
                FnClone::new(
                    move |result: crate::api::error::Result<DeviceFeedback>| {
                        info!("[operate_command_queue] operate_command_queue result: {:?}", result);
                        match result {
                            Ok(fb) => {
                                tsfn.call(
                                    vec![
                                        "0".to_string(),
                                        fb.device_id,
                                        fb.req_sn.to_string(),
                                        fb.error_info.to_string(),
                                    ],
                                    ThreadsafeFunctionCallMode::Blocking,
                                );
                            }
                            Err(e) => {
                                tsfn.call(
                                    vec!["100".to_string(), e.to_string()],
                                    ThreadsafeFunctionCallMode::Blocking,
                                );
                            }
                        }
                    },
                ),
            );
        }
    }
    new_js_object(env)
}

pub fn cmd_clean_operate_serial_queue(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    match get_device_manager().get_device(device_id.as_str()) {
        None => Err(Error::new(Status::InvalidArg, "Don't find UpDevice")),
        Some(it) => {
            it.clear_command_queue();
            new_js_object(env)
        }
    }
}

pub fn cmd_priority_prepare_device(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    get_device_manager().priority_prepare_device(&device_id);
    new_js_object(env)
}

////////////////////////////// ExtendApi End //////////////////////////////

////////////////////////////// device group Start ////////////////////////////
pub fn cmd_create_device_group(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let trace_id = UhsdManager::get_instance()
            .get_bind()
            .create_trace_id()
            .unwrap_or_default();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    debug!(
        "{} cmd_create_device_group device_id:{:?}, trace_id:{}, req_sn:{}",
        DEVICE_GROUP_LOG_PREFIX, 
        device_id,
        trace_id,
        req_sn,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    get_task_manager().spawn(async move {
        let ret = UhsdManager::get_instance()
            .get_device_group()
            .create_device_group(GroupDeviceReqInfo{
                device_id,
                trace_id,
                req_sn,
                timeout:UHSD_DEFAULT_GROUP_DEVICE_TIMEOUT as u32,
            })
            .await;
        debug!("{}:cmd_create_device_group result: {:?}",DEVICE_GROUP_LOG_PREFIX, ret);
        match ret {
            Ok(values) => {
                let device_id = values.iter().find(|u| u.name == "device_id");
                if let Some(device_id) = device_id
                {
                    let device_info = UpDeviceInfo::new (
                        "".to_string(),
                        device_id.value.to_string(),
                        None,
                        None,
                        "".to_string(),
                        "".to_string(),
                        None,
                        "".to_string(),
                        UpDeviceBasic::default(),
                        UpDevicePermission::default(),
                        UpDeviceProduct::default(),
                        UpDeviceRelation::default(),
                    );
                    match to_json(&device_info) {
                        Ok(json) => {
                            tsfn.call(
                                vec![platform_code::SUCCESS.to_string(), json],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                        Err(_) => {
                            tsfn.call(
                                vec![
                                    platform_code::DEVICE_ERR.to_string(),
                                    "json error！".to_string(),
                                ],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                    }
                } else {
                    tsfn.call(
                        vec![
                            platform_code::DEVICE_ERR.to_string(),
                            "get device_id fail！".to_string(),
                        ],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
            Err(err) => {
                tsfn.call(
                    match_toolkit_error(err),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}
pub fn cmd_find_groupable_devices(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    debug!(
        "{} cmd_find_groupable_devices device_id:{:?}, req_sn:{}, trace_id:{}",
        DEVICE_GROUP_LOG_PREFIX,
        device_id,
        req_sn,
        trace_id,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    get_task_manager().spawn(async move {
        let ret = UhsdManager::get_instance()
            .get_device_group()
            .find_groupable_devices(GroupDeviceBaseReqInfo{
                base_device_id:device_id,
                trace_id,
                req_sn,
            })
            .await;
        debug!("{}:cmd_find_groupable_devices result: {:?}",DEVICE_GROUP_LOG_PREFIX, ret);
        match ret {
            Ok(values) => {
                let device_ids = values.iter().find(|u| u.name == "device_ids");
                if let Some(device_ids) = device_ids
                {
                    tsfn.call(
                        vec![platform_code::SUCCESS.to_string(), device_ids.value.to_string()],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                } else {
                    tsfn.call(
                        vec![
                            platform_code::DEVICE_ERR.to_string(),
                            "get device_ids fail！".to_string(),
                        ],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
            Err(err) => {
                tsfn.call(
                    match_toolkit_error(err),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}
pub fn cmd_is_group(
    env: Env,
    args: JsObject,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    debug!(
        "{} cmd_is_group device_id:{:?}",
        DEVICE_GROUP_LOG_PREFIX,
        device_id,
    );
    let result = UhsdManager::get_instance().get_device_group().is_group(device_id);
    let bool_result = result.unwrap_or(false);
    to_js_object_bool(env, bool_result)
}
pub fn cmd_delete_device_group(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    debug!(
        "{} cmd_delete_device_group device_id:{:?}, trace_id:{}, req_sn:{}",
        DEVICE_GROUP_LOG_PREFIX,
        device_id,
        trace_id,
        req_sn,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    get_task_manager().spawn(async move {
        let ret = UhsdManager::get_instance()
            .get_device_group()
            .delete_device_group(GroupDeviceReqInfo{
                device_id,
                trace_id,
                req_sn,
                timeout:UHSD_DEFAULT_GROUP_DEVICE_TIMEOUT as u32,
            })
            .await;
        debug!("{}:cmd_delete_device_group result: {:?}",DEVICE_GROUP_LOG_PREFIX, ret);
        match ret {
            Ok(values) => {
                let device_id = values.iter().find(|u| u.name == "device_id");
                if let Some(device_id) = device_id
                {
                    let result = CreateDeviceGroupResult {
                        device_id: device_id.value.to_string(),
                    };
                    match to_json(&result) {
                        Ok(json) => {
                            tsfn.call(
                                vec![platform_code::SUCCESS.to_string(), json],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                        Err(_) => {
                            tsfn.call(
                                vec![
                                    platform_code::DEVICE_ERR.to_string(),
                                    "json error！".to_string(),
                                ],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                    }
                } else {
                    tsfn.call(
                        vec![
                            platform_code::DEVICE_ERR.to_string(),
                            "get device_id fail！".to_string(),
                        ],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
            Err(err) => {
                tsfn.call(
                    match_toolkit_error(err),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}
pub fn cmd_add_devices_to_group(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let devices: JsObject = args
        .get("devices")?
        .ok_or(Error::new(Status::InvalidArg, "need devices"))?;
    let devices_keys = devices.get_property_names()?;
    let length = devices_keys.get_array_length()?;
    let mut device_ids: Vec<String> = vec![];
    for i in 0..length {
        let key = devices_keys.get_element::<JsString>(i)?;
        let utf8_value = JsString::into_utf8(key)?;
        let rust_str = utf8_value.as_str()?;
        let device_id = devices
            .get(rust_str)?
            .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
        device_ids.push(device_id);
    }
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    debug!(
        "{} cmd_add_devices_to_group device_id:{:?}, device_ids:{:?}, req_sn:{},trace_id:{}",
        DEVICE_GROUP_LOG_PREFIX,
        device_id,
        device_ids,
        req_sn,
        trace_id,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    get_task_manager().spawn(async move {
        let ret = UhsdManager::get_instance()
            .get_device_group()
            .add_devices_to_group(GroupDeviceAddDelReqInfo{
                group_device_id:device_id,
                device_ids,
                trace_id,
                num: length,
                req_sn,
                timeout:DEVICE_GROUP_TIMEOUT_ADD_DEL_DEVICES as u32,
            })
            .await;
        debug!("{}:cmd_add_devices_to_group result: {:?}",DEVICE_GROUP_LOG_PREFIX, ret);
        match ret {
            Ok(values) => {
                let add_del_result = values.iter().find(|u| u.name == "add_del_result");
                if let Some(add_del_result) = add_del_result
                {
                    tsfn.call(
                        vec![platform_code::SUCCESS.to_string(), add_del_result.value.to_string()],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }else{
                    tsfn.call(
                        vec![
                            platform_code::DEVICE_ERR.to_string(),
                            "get add_del_result fail！".to_string(),
                        ],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
            Err(err) => {
                tsfn.call(
                    match_toolkit_error(err),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}
pub fn cmd_remove_devices_from_group(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let devices: JsObject = args
        .get("devices")?
        .ok_or(Error::new(Status::InvalidArg, "need devices"))?;
    let devices_keys = devices.get_property_names()?;
    let length = devices_keys.get_array_length()?;
    let mut device_ids: Vec<String> = vec![];
    for i in 0..length {
        let key = devices_keys.get_element::<JsString>(i)?;
        let utf8_value = JsString::into_utf8(key)?;
        let rust_str = utf8_value.as_str()?;
        let device_id = devices
            .get(rust_str)?
            .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
        device_ids.push(device_id);
    }
    let req_sn = UhsdManager::get_instance().get_common().get_request_sn() as u32;
    let trace_id = UhsdManager::get_instance()
        .get_bind()
        .create_trace_id()
        .unwrap_or_default();
    debug!(
        "{} cmd_remove_devices_from_group device_id:{:?}, device_ids:{:?}, req_sn:{}, trace_id:{}",
        DEVICE_GROUP_LOG_PREFIX,
        device_id,
        device_ids,
        req_sn,
        trace_id,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    get_task_manager().spawn(async move {
        let ret = UhsdManager::get_instance()
            .get_device_group()
            .remove_devices_from_group(GroupDeviceAddDelReqInfo{
                group_device_id:device_id,
                device_ids,
                trace_id,
                num: length,
                req_sn,
                timeout:DEVICE_GROUP_TIMEOUT_ADD_DEL_DEVICES as u32,
            })
            .await;
        debug!("{}:cmd_remove_devices_from_group result: {:?}",DEVICE_GROUP_LOG_PREFIX, ret);
        match ret {
            Ok(values) => {
                let add_del_result = values.iter().find(|u| u.name == "add_del_result");
                if let Some(add_del_result) = add_del_result
                {
                    tsfn.call(
                        vec![platform_code::SUCCESS.to_string(), add_del_result.value.to_string()],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }else{
                    tsfn.call(
                        vec![
                            platform_code::DEVICE_ERR.to_string(),
                            "get add_del_result fail！".to_string(),
                        ],
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
            Err(err) => {
                tsfn.call(
                    match_toolkit_error(err),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    new_js_object(env)
}
pub fn cmd_get_group_member_list(
    env: Env,
    args: JsObject,
    function: Function<Vec<String>>,
) -> napi_ohos::Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    debug!(
        "{} cmd_get_group_member_list device_id:{:?}",
        DEVICE_GROUP_LOG_PREFIX, 
        device_id,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    let usdk_member_list = UhsdManager::get_instance().get_device_group().get_group_member_list(device_id.clone());
    if usdk_member_list.is_err() {
        tsfn.call(
            vec![
                platform_code::DEVICE_ERR.to_string(),
                "get usdk member list fail！".to_string(),
            ],
            ThreadsafeFunctionCallMode::Blocking,
        );
        return new_js_object(env);
    }
    let usdk_members = usdk_member_list.unwrap_or_default();
    let group_device = UpDeviceInjection::get_instance().get_device_manager().get_device_info(&device_id);
    match group_device {
        Some(group_device) => {
            let family_id = group_device.get_relation().family_id();
            debug!("{} cmd_get_group_member_list family_id: {}",DEVICE_GROUP_LOG_PREFIX, family_id);
            get_runtime().spawn(async move {
                match UserDomainManager::get_instance()
                    .get_user_domain()
                    .get_user()
                    .get_family_by_id(family_id.as_str())
                {
                    None => {
                        tsfn.call(
                            vec![
                                platform_code::DEVICE_ERR.to_string(),
                                "get family fail！".to_string(),
                            ],
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Some(family) => match family.get_group_device_list().await {
                        Ok(devices) => {
                            debug!("{} cmd_get_group_member_list device_list_len: {}",DEVICE_GROUP_LOG_PREFIX, devices.len());
                            let filter_devices = filter_devices(&devices,usdk_members);
                            let device_info_list: Vec<UpDeviceInfo> = filter_devices
                                .into_iter()
                                .map(|device| device.device_info.into())
                                .collect();
                            let mut device_models = Vec::new();
                            for device_info in device_info_list {
                                let attrs_result = UhsdManager::get_instance().get_device().get_device_attribute_list(&device_info.device_id());
                                let attrs = match attrs_result {
                                    Ok(attrs) => attrs,
                                    Err(e) => {
                                        debug!("{} Failed to get device attributes for {}: {:?}", DEVICE_GROUP_LOG_PREFIX, device_info.device_id(), e);
                                        Vec::new()
                                    }
                                };
                                let mut updevice_attrs = Vec::new();
                                for attr in attrs {
                                    let updevice_attr = UpDeviceAttribute::new(attr.name().to_string(), attr.value().to_string());
                                    updevice_attrs.push(updevice_attr);
                                }
                                let usdk_info_result = UhsdManager::get_instance().get_device().get_device_info(&device_info.device_id());
                                let mut connect_state = UpDeviceConnectState::default();
                                let mut online_state = UpDeviceOnlineState::default();
                                let mut sleep_state = UpDeviceSleepState::default();
                                let mut state_code = 0;
                                let mut offline_cause = UpDeviceOfflineCause::default();
                                let mut offline_days = 0;
                                if let Ok(usdk_info) = usdk_info_result {
                                    connect_state = UpDeviceConnectState::from(&usdk_info.connect_state);
                                    online_state = UpDeviceOnlineState::from(&usdk_info.online_state);
                                    sleep_state = UpDeviceSleepState::from(&usdk_info.sleep_state);
                                    state_code = usdk_info.state_code;
                                    offline_cause = UpDeviceOfflineCause::from(&usdk_info.offline_cause);
                                    offline_days = usdk_info.offline_days;
                                }
                                let model = UpDeviceModel {
                                    protocol: device_info.protocol(),
                                    device_id: device_info.device_id(),
                                    device_info,
                                    attribute_list: updevice_attrs,
                                    caution_list: vec![],
                                    engine_attribute_list: vec![],
                                    engine_caution_list: vec![],
                                    config_state: Default::default(),
                                    connect_state,
                                    ble_state: Default::default(),
                                    online_state,
                                    sleep_state,
                                    state_code,
                                    offline_cause,
                                    offline_days,
                                };
                                device_models.push(model);
                            }
                            match to_json(&device_models) {
                                Ok(json) => {
                                    tsfn.call(
                                        vec![platform_code::SUCCESS.to_string(), json],
                                        ThreadsafeFunctionCallMode::Blocking,
                                    );
                                }
                                Err(_) => {
                                    tsfn.call(
                                        vec![
                                            platform_code::DEVICE_ERR.to_string(),
                                            "json error！".to_string(),
                                        ],
                                        ThreadsafeFunctionCallMode::Blocking,
                                    );
                                }
                            }
                        }
                        Err(_) => {
                            tsfn.call(
                                vec![
                                    platform_code::DEVICE_ERR.to_string(),
                                    "get group devices fail！".to_string(),
                                ],
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                        }
                    },
                }
            });
        }
        None => {
            tsfn.call(
                vec![
                    platform_code::DEVICE_ERR.to_string(),
                    "get group device fail！".to_string(),
                ],
                ThreadsafeFunctionCallMode::Blocking,
            );
        }
    }
    new_js_object(env)
}
////////////////////////////// device group End //////////////////////////////

fn to_json<T>(value: &T) -> Result<String>
where
    T: ?Sized + Serialize,
{
    let json = serde_json::to_string(value)
        .map_err(|e| Error::new(Status::ObjectExpected, e.to_string()))?;
    Ok(json)
}

fn to_arc_json<T>(arc_value: &Arc<T>) -> Result<String>
where
    T: ?Sized + Serialize,
{
    let value = arc_value.as_ref();
    to_json(value)
}

fn to_vec_arc_json<T>(values: &Vec<Arc<T>>) -> Result<String>
where
    T: ?Sized + Serialize,
{
    let value_list: Vec<&T> = values.iter().map(|value| value.as_ref()).collect();
    to_json(&value_list)
}

fn to_js_object_str(env: Env, data: &str) -> Result<JsObject> {
    let mut result = env.create_object()?;
    result.set(RESULT_STRING_VALUE, data)?;
    Ok(result)
}

fn to_js_object_bool(env: Env, success: bool) -> Result<JsObject> {
    let mut result = env.create_object()?;
    result.set(RESULT_BOOLEAN_VALUE, success)?;
    Ok(result)
}

fn new_js_object(env: Env) -> Result<JsObject> {
    env.create_object()
}

fn get_device_manager() -> &'static UpDeviceManager {
    UpDeviceInjection::get_instance().get_device_manager()
}

fn match_toolkit_error(error: ToolkitError) -> Vec<String> {
    match error {
        ToolkitError::CError(c_error) => match c_error {
            CError::UhsdError(code, desc, _) => vec![code.to_string(), desc],
            _ => vec!["108".to_string(), c_error.to_string()]
        },
        _ => vec!["108".to_string(), error.to_string()]
    }
}

fn filter_devices(devices: &Vec<Device>,ids: Vec<String>) -> Vec<Device> {
    let mut arr = Vec::new();
    for device in devices {
        if ids.contains(&device.device_info.device_id) {
            arr.push(device.clone());
        }
    }
    arr
}

fn append_engine_attributes(model: &mut UpDeviceModel, extend_api: Arc<dyn ExtendApi>) {
    let attribute_list = runtime::Runtime::new().map_or(vec![], |rt| {
        rt.block_on(async move {
            let attributes: Vec<Arc<LEAttribute>> = extend_api.query_attributes().await;
            attributes
        })
    });
    let engine_attribute_list = attribute_list
        .iter()
        .map(|it: &Arc<LEAttribute>| it.into())
        .collect();
    model.set_engine_attribute_list(engine_attribute_list);
}

fn append_engine_cautions(model: &mut UpDeviceModel, extend_api: Arc<dyn ExtendApi>) {
    let engine_caution_list = runtime::Runtime::new().map_or(vec![], |rt| {
        rt.block_on(async move {
            let cautions: Vec<LECaution> = extend_api.query_cautions().await;
            cautions
        })
    });
    model.set_engine_caution_list(engine_caution_list);
}
