use crate::api::device_injection::UpDeviceInjection;
use crate::features::flat::updevice_generated::com::haier::uhome::uplus::rust::updevice::fbs::{
    NoneWrapper, None<PERSON>rapper<PERSON>rgs, UpDevice<PERSON>ontainer, UpDeviceFlat, UpDeviceFlatArgs,
};
use flatbuffers::FlatBufferBuilder;
use log::{debug, error, info, warn};
use std::collections::HashMap;
use task_manager::platform::function::PlatformConsumer;

static EMPTY: String = String::new();
const ACTION: &str = "action";
const DEFAULT_SIZE: usize = 1024;
const SUCCESS_CODE: &str = "000000";
const PARAMS_ERROR: &str = "900003";

macro_rules! require_params {
    ($params:expr, $($param:expr),+) => {
        for &param in &[$($param),+] {
            if !$params.contains_key(param) {
                 warn!("updevice: required parameter '{}' is missing", param);
                return invalid_arg_result(&format!("{} is required", param));
            }
        }
    };
}

pub fn lib_updevice_cross_platform(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("updevice: executing action: {}", action);
    match action {
        "init" => init(params),
        _ => {
            warn!("updevice: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub async fn lib_updevice_cross_platform_async(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("updevice: executing action: {}", action);
    match action {
        _ => {
            warn!("updevice: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub fn lib_updevice_cross_platform_consumer_data(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("updevice: executing action: {}", action);
    match action {
        _ => {
            warn!("updevice: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

fn init(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "app_id",
        "app_key",
        "app_version",
        "app_debug_mode",
        "client_id",
        "environment"
    );
    #[cfg(any(feature = "android", feature = "ohos", feature = "ios"))]
    {
        use rust_usdk::toolkit_ffi::fota::lib_fota::LibFotaImpl;
        use rust_usdk::toolkit_ffi::p2p_resource::lib_p2p_resource::LibP2PResourceImpl;
        use rust_usdk::toolkit_ffi::uhsd::LibUHomeImpl;
        use rust_usdk::toolkit_ffi::uhsd_bind::bind_impl::LibBindImpl;
        use rust_usdk::toolkit_ffi::uhsd_bind::by_qr_code_auth::lib_qrcode_auth::LibQRCodeAuthImpl;
        use rust_usdk::toolkit_ffi::uhsd_device_group::device_group_impl::LibDeviceGroupImpl;
        use rust_usdk::toolkit_ffi::uhsd_injection::UhsdInjection;
        use rust_usdk::usdk_toolkit::toolkit::{AppInfo, Area};
        UhsdInjection::get_instance().set_lib_uhome(Box::new(LibUHomeImpl::new()));
        UhsdInjection::get_instance().set_lib_bind(Box::new(LibBindImpl::new()));
        UhsdInjection::get_instance().set_lib_fota(Box::new(LibFotaImpl::new()));
        UhsdInjection::get_instance().set_lib_qrcode_auth(Box::new(LibQRCodeAuthImpl::new()));
        UhsdInjection::get_instance().set_lib_device_group(Box::new(LibDeviceGroupImpl::new()));
        UhsdInjection::get_instance().set_lib_resource(Box::new(LibP2PResourceImpl::new()));
        let app_id = params.get("app_id").unwrap().to_string();
        let app_key = params.get("app_key").unwrap().to_string();
        let app_version = params.get("app_version").unwrap().to_string();
        let app_debug_mode = params.get("app_debug_mode").unwrap().parse::<bool>().unwrap();
        let client_id = params.get("client_id").unwrap().to_string();
        let environment = params.get("environment").unwrap().parse::<i32>().unwrap();
        UpDeviceInjection::get_instance().init_device_manager(
            Area::China,
            AppInfo::new(app_id, app_key, app_version, app_debug_mode),
            client_id,
            environment.into(),
        );
    }
    debug!("init success");
    void_result()
}

fn void_result() -> Vec<u8> {
    debug!("Operation successful");
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let code = builder.create_string(SUCCESS_CODE);
    let flat = UpDeviceFlat::create(
        &mut builder,
        &UpDeviceFlatArgs {
            container_type: UpDeviceContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(code),
            error: None,
        },
    );
    builder.finish(flat, None);
    builder.finished_data().to_vec()
}

fn invalid_arg_result(error_message: &str) -> Vec<u8> {
    failure_result(error_message, PARAMS_ERROR)
}

fn failure_result(error_message: &str, error_code: &str) -> Vec<u8> {
    error!(
        "updevice ffi errormessage:{},error_code:{}",
        error_message, error_code
    );
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let error_message = builder.create_string(error_message);
    let error_code = builder.create_string(error_code);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let updevice_flat = UpDeviceFlat::create(
        &mut builder,
        &UpDeviceFlatArgs {
            container_type: UpDeviceContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(error_code),
            error: Some(error_message),
        },
    );
    builder.finish(updevice_flat, None);
    builder.finished_data().to_vec()
}
