// automatically generated by the FlatBuffers compiler, do not modify


// @generated

use core::mem;
use core::cmp::Ordering;

extern crate flatbuffers;
use self::flatbuffers::{EndianScalar, Follow};

#[allow(unused_imports, dead_code)]
pub mod com {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod haier {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod uhome {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod uplus {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod rust {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod updevice {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod fbs {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};

#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MIN_UP_DEVICE_CONTAINER: u8 = 0;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MAX_UP_DEVICE_CONTAINER: u8 = 4;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
#[allow(non_camel_case_types)]
pub const ENUM_VALUES_UP_DEVICE_CONTAINER: [UpDeviceContainer; 5] = [
  UpDeviceContainer::NONE,
  UpDeviceContainer::Int32Wrapper,
  UpDeviceContainer::StrWrapper,
  UpDeviceContainer::BoolWrapper,
  UpDeviceContainer::NoneWrapper,
];

#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
#[repr(transparent)]
pub struct UpDeviceContainer(pub u8);
#[allow(non_upper_case_globals)]
impl UpDeviceContainer {
  pub const NONE: Self = Self(0);
  pub const Int32Wrapper: Self = Self(1);
  pub const StrWrapper: Self = Self(2);
  pub const BoolWrapper: Self = Self(3);
  pub const NoneWrapper: Self = Self(4);

  pub const ENUM_MIN: u8 = 0;
  pub const ENUM_MAX: u8 = 4;
  pub const ENUM_VALUES: &'static [Self] = &[
    Self::NONE,
    Self::Int32Wrapper,
    Self::StrWrapper,
    Self::BoolWrapper,
    Self::NoneWrapper,
  ];
  /// Returns the variant's name or "" if unknown.
  pub fn variant_name(self) -> Option<&'static str> {
    match self {
      Self::NONE => Some("NONE"),
      Self::Int32Wrapper => Some("Int32Wrapper"),
      Self::StrWrapper => Some("StrWrapper"),
      Self::BoolWrapper => Some("BoolWrapper"),
      Self::NoneWrapper => Some("NoneWrapper"),
      _ => None,
    }
  }
}
impl core::fmt::Debug for UpDeviceContainer {
  fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
    if let Some(name) = self.variant_name() {
      f.write_str(name)
    } else {
      f.write_fmt(format_args!("<UNKNOWN {:?}>", self.0))
    }
  }
}
impl<'a> flatbuffers::Follow<'a> for UpDeviceContainer {
  type Inner = Self;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    let b = flatbuffers::read_scalar_at::<u8>(buf, loc);
    Self(b)
  }
}

impl flatbuffers::Push for UpDeviceContainer {
    type Output = UpDeviceContainer;
    #[inline]
    unsafe fn push(&self, dst: &mut [u8], _written_len: usize) {
        flatbuffers::emplace_scalar::<u8>(dst, self.0);
    }
}

impl flatbuffers::EndianScalar for UpDeviceContainer {
  type Scalar = u8;
  #[inline]
  fn to_little_endian(self) -> u8 {
    self.0.to_le()
  }
  #[inline]
  #[allow(clippy::wrong_self_convention)]
  fn from_little_endian(v: u8) -> Self {
    let b = u8::from_le(v);
    Self(b)
  }
}

impl<'a> flatbuffers::Verifiable for UpDeviceContainer {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    u8::run_verifier(v, pos)
  }
}

impl flatbuffers::SimpleToVerifyInSlice for UpDeviceContainer {}
pub struct UpDeviceContainerUnionTableOffset {}

pub enum Int32WrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct Int32Wrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for Int32Wrapper<'a> {
  type Inner = Int32Wrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> Int32Wrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    Int32Wrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args Int32WrapperArgs
  ) -> flatbuffers::WIPOffset<Int32Wrapper<'bldr>> {
    let mut builder = Int32WrapperBuilder::new(_fbb);
    builder.add_value(args.value);
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(Int32Wrapper::VT_VALUE, Some(0)).unwrap()}
  }
}

impl flatbuffers::Verifiable for Int32Wrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<i32>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct Int32WrapperArgs {
    pub value: i32,
}
impl<'a> Default for Int32WrapperArgs {
  #[inline]
  fn default() -> Self {
    Int32WrapperArgs {
      value: 0,
    }
  }
}

pub struct Int32WrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> Int32WrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: i32) {
    self.fbb_.push_slot::<i32>(Int32Wrapper::VT_VALUE, value, 0);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> Int32WrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    Int32WrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<Int32Wrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for Int32Wrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("Int32Wrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum BoolWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct BoolWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for BoolWrapper<'a> {
  type Inner = BoolWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> BoolWrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    BoolWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args BoolWrapperArgs
  ) -> flatbuffers::WIPOffset<BoolWrapper<'bldr>> {
    let mut builder = BoolWrapperBuilder::new(_fbb);
    builder.add_value(args.value);
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(BoolWrapper::VT_VALUE, Some(false)).unwrap()}
  }
}

impl flatbuffers::Verifiable for BoolWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<bool>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct BoolWrapperArgs {
    pub value: bool,
}
impl<'a> Default for BoolWrapperArgs {
  #[inline]
  fn default() -> Self {
    BoolWrapperArgs {
      value: false,
    }
  }
}

pub struct BoolWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> BoolWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: bool) {
    self.fbb_.push_slot::<bool>(BoolWrapper::VT_VALUE, value, false);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> BoolWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    BoolWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<BoolWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for BoolWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("BoolWrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum StrWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct StrWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for StrWrapper<'a> {
  type Inner = StrWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> StrWrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    StrWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args StrWrapperArgs<'args>
  ) -> flatbuffers::WIPOffset<StrWrapper<'bldr>> {
    let mut builder = StrWrapperBuilder::new(_fbb);
    if let Some(x) = args.value { builder.add_value(x); }
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(StrWrapper::VT_VALUE, None)}
  }
}

impl flatbuffers::Verifiable for StrWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct StrWrapperArgs<'a> {
    pub value: Option<flatbuffers::WIPOffset<&'a str>>,
}
impl<'a> Default for StrWrapperArgs<'a> {
  #[inline]
  fn default() -> Self {
    StrWrapperArgs {
      value: None,
    }
  }
}

pub struct StrWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> StrWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(StrWrapper::VT_VALUE, value);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> StrWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    StrWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<StrWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for StrWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("StrWrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum NoneWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct NoneWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for NoneWrapper<'a> {
  type Inner = NoneWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> NoneWrapper<'a> {

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    NoneWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    _args: &'args NoneWrapperArgs
  ) -> flatbuffers::WIPOffset<NoneWrapper<'bldr>> {
    let mut builder = NoneWrapperBuilder::new(_fbb);
    builder.finish()
  }

}

impl flatbuffers::Verifiable for NoneWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .finish();
    Ok(())
  }
}
pub struct NoneWrapperArgs {
}
impl<'a> Default for NoneWrapperArgs {
  #[inline]
  fn default() -> Self {
    NoneWrapperArgs {
    }
  }
}

pub struct NoneWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> NoneWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> NoneWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    NoneWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<NoneWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for NoneWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("NoneWrapper");
      ds.finish()
  }
}
pub enum UpDeviceFlatOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct UpDeviceFlat<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for UpDeviceFlat<'a> {
  type Inner = UpDeviceFlat<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> UpDeviceFlat<'a> {
  pub const VT_CONTAINER_TYPE: flatbuffers::VOffsetT = 4;
  pub const VT_CONTAINER: flatbuffers::VOffsetT = 6;
  pub const VT_CODE: flatbuffers::VOffsetT = 8;
  pub const VT_ERROR: flatbuffers::VOffsetT = 10;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    UpDeviceFlat { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args UpDeviceFlatArgs<'args>
  ) -> flatbuffers::WIPOffset<UpDeviceFlat<'bldr>> {
    let mut builder = UpDeviceFlatBuilder::new(_fbb);
    if let Some(x) = args.error { builder.add_error(x); }
    if let Some(x) = args.code { builder.add_code(x); }
    if let Some(x) = args.container { builder.add_container(x); }
    builder.add_container_type(args.container_type);
    builder.finish()
  }


  #[inline]
  pub fn container_type(&self) -> UpDeviceContainer {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<UpDeviceContainer>(UpDeviceFlat::VT_CONTAINER_TYPE, Some(UpDeviceContainer::NONE)).unwrap()}
  }
  #[inline]
  pub fn container(&self) -> Option<flatbuffers::Table<'a>> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<flatbuffers::Table<'a>>>(UpDeviceFlat::VT_CONTAINER, None)}
  }
  #[inline]
  pub fn code(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(UpDeviceFlat::VT_CODE, None)}
  }
  #[inline]
  pub fn error(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(UpDeviceFlat::VT_ERROR, None)}
  }
  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_int_32_wrapper(&self) -> Option<Int32Wrapper<'a>> {
    if self.container_type() == UpDeviceContainer::Int32Wrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { Int32Wrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_str_wrapper(&self) -> Option<StrWrapper<'a>> {
    if self.container_type() == UpDeviceContainer::StrWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { StrWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_bool_wrapper(&self) -> Option<BoolWrapper<'a>> {
    if self.container_type() == UpDeviceContainer::BoolWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { BoolWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_none_wrapper(&self) -> Option<NoneWrapper<'a>> {
    if self.container_type() == UpDeviceContainer::NoneWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { NoneWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

}

impl flatbuffers::Verifiable for UpDeviceFlat<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_union::<UpDeviceContainer, _>("container_type", Self::VT_CONTAINER_TYPE, "container", Self::VT_CONTAINER, false, |key, v, pos| {
        match key {
          UpDeviceContainer::Int32Wrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<Int32Wrapper>>("UpDeviceContainer::Int32Wrapper", pos),
          UpDeviceContainer::StrWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<StrWrapper>>("UpDeviceContainer::StrWrapper", pos),
          UpDeviceContainer::BoolWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<BoolWrapper>>("UpDeviceContainer::BoolWrapper", pos),
          UpDeviceContainer::NoneWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<NoneWrapper>>("UpDeviceContainer::NoneWrapper", pos),
          _ => Ok(()),
        }
     })?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("code", Self::VT_CODE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("error", Self::VT_ERROR, false)?
     .finish();
    Ok(())
  }
}
pub struct UpDeviceFlatArgs<'a> {
    pub container_type: UpDeviceContainer,
    pub container: Option<flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>>,
    pub code: Option<flatbuffers::WIPOffset<&'a str>>,
    pub error: Option<flatbuffers::WIPOffset<&'a str>>,
}
impl<'a> Default for UpDeviceFlatArgs<'a> {
  #[inline]
  fn default() -> Self {
    UpDeviceFlatArgs {
      container_type: UpDeviceContainer::NONE,
      container: None,
      code: None,
      error: None,
    }
  }
}

pub struct UpDeviceFlatBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> UpDeviceFlatBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_container_type(&mut self, container_type: UpDeviceContainer) {
    self.fbb_.push_slot::<UpDeviceContainer>(UpDeviceFlat::VT_CONTAINER_TYPE, container_type, UpDeviceContainer::NONE);
  }
  #[inline]
  pub fn add_container(&mut self, container: flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(UpDeviceFlat::VT_CONTAINER, container);
  }
  #[inline]
  pub fn add_code(&mut self, code: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(UpDeviceFlat::VT_CODE, code);
  }
  #[inline]
  pub fn add_error(&mut self, error: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(UpDeviceFlat::VT_ERROR, error);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> UpDeviceFlatBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    UpDeviceFlatBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<UpDeviceFlat<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for UpDeviceFlat<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("UpDeviceFlat");
      ds.field("container_type", &self.container_type());
      match self.container_type() {
        UpDeviceContainer::Int32Wrapper => {
          if let Some(x) = self.container_as_int_32_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        UpDeviceContainer::StrWrapper => {
          if let Some(x) = self.container_as_str_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        UpDeviceContainer::BoolWrapper => {
          if let Some(x) = self.container_as_bool_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        UpDeviceContainer::NoneWrapper => {
          if let Some(x) = self.container_as_none_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        _ => {
          let x: Option<()> = None;
          ds.field("container", &x)
        },
      };
      ds.field("code", &self.code());
      ds.field("error", &self.error());
      ds.finish()
  }
}
#[inline]
/// Verifies that a buffer of bytes contains a `UpDeviceFlat`
/// and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_up_device_flat_unchecked`.
pub fn root_as_up_device_flat(buf: &[u8]) -> Result<UpDeviceFlat, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::root::<UpDeviceFlat>(buf)
}
#[inline]
/// Verifies that a buffer of bytes contains a size prefixed
/// `UpDeviceFlat` and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `size_prefixed_root_as_up_device_flat_unchecked`.
pub fn size_prefixed_root_as_up_device_flat(buf: &[u8]) -> Result<UpDeviceFlat, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::size_prefixed_root::<UpDeviceFlat>(buf)
}
#[inline]
/// Verifies, with the given options, that a buffer of bytes
/// contains a `UpDeviceFlat` and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_up_device_flat_unchecked`.
pub fn root_as_up_device_flat_with_opts<'b, 'o>(
  opts: &'o flatbuffers::VerifierOptions,
  buf: &'b [u8],
) -> Result<UpDeviceFlat<'b>, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::root_with_opts::<UpDeviceFlat<'b>>(opts, buf)
}
#[inline]
/// Verifies, with the given verifier options, that a buffer of
/// bytes contains a size prefixed `UpDeviceFlat` and returns
/// it. Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_up_device_flat_unchecked`.
pub fn size_prefixed_root_as_up_device_flat_with_opts<'b, 'o>(
  opts: &'o flatbuffers::VerifierOptions,
  buf: &'b [u8],
) -> Result<UpDeviceFlat<'b>, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::size_prefixed_root_with_opts::<UpDeviceFlat<'b>>(opts, buf)
}
#[inline]
/// Assumes, without verification, that a buffer of bytes contains a UpDeviceFlat and returns it.
/// # Safety
/// Callers must trust the given bytes do indeed contain a valid `UpDeviceFlat`.
pub unsafe fn root_as_up_device_flat_unchecked(buf: &[u8]) -> UpDeviceFlat {
  flatbuffers::root_unchecked::<UpDeviceFlat>(buf)
}
#[inline]
/// Assumes, without verification, that a buffer of bytes contains a size prefixed UpDeviceFlat and returns it.
/// # Safety
/// Callers must trust the given bytes do indeed contain a valid size prefixed `UpDeviceFlat`.
pub unsafe fn size_prefixed_root_as_up_device_flat_unchecked(buf: &[u8]) -> UpDeviceFlat {
  flatbuffers::size_prefixed_root_unchecked::<UpDeviceFlat>(buf)
}
#[inline]
pub fn finish_up_device_flat_buffer<'a, 'b, A: flatbuffers::Allocator + 'a>(
    fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
    root: flatbuffers::WIPOffset<UpDeviceFlat<'a>>) {
  fbb.finish(root, None);
}

#[inline]
pub fn finish_size_prefixed_up_device_flat_buffer<'a, 'b, A: flatbuffers::Allocator + 'a>(fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>, root: flatbuffers::WIPOffset<UpDeviceFlat<'a>>) {
  fbb.finish_size_prefixed(root, None);
}
}  // pub mod fbs
}  // pub mod updevice
}  // pub mod rust
}  // pub mod uplus
}  // pub mod uhome
}  // pub mod haier
}  // pub mod com

