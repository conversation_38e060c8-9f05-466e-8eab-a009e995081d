use std::sync::Arc;

use logic_engine::device::device_attribute::UpDeviceAttribute;
use logic_engine::device::device_caution::UpDeviceCaution;
use logic_engine::engine::attribute::{LEAttribute, ValueRange};
use logic_engine::engine::caution::LECaution;
use serde::{Deserialize, Serialize};

use crate::models::device_config_state::UpDeviceConfigState;
use crate::models::device_connect_state::UpDeviceConnectState;
use crate::models::device_info::UpDeviceInfo;
use crate::models::device_offline_cause::UpDeviceOfflineCause;
use crate::models::device_online_state::UpDeviceOnlineState;
use crate::models::device_sleep_state::UpDeviceSleepState;

pub const RESULT_STRING_VALUE: &str = "stringValue";
pub const RESULT_BOOLEAN_VALUE: &str = "booleanValue";
pub const RESULT_NUMBER_VALUE: &str = "numberValue";

#[derive(Default, <PERSON><PERSON>, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct UpDeviceModel {
    pub protocol: String,
    pub device_id: String,
    pub device_info: UpDeviceInfo,
    pub attribute_list: Vec<UpDeviceAttribute>,
    pub caution_list: Vec<UpDeviceCaution>,
    pub engine_attribute_list: Vec<UpDeviceAttributeModel>,
    pub engine_caution_list: Vec<LECaution>,
    pub config_state: UpDeviceConfigState,
    pub connect_state: UpDeviceConnectState,
    pub ble_state: UpDeviceConnectState,
    pub online_state: UpDeviceOnlineState,
    pub sleep_state: UpDeviceSleepState,
    #[serde(rename = "faultStateCode")]
    pub state_code: i32,
    pub offline_cause: UpDeviceOfflineCause,
    pub offline_days: u32,
}

impl UpDeviceModel {
    pub fn set_engine_attribute_list(&mut self, attribute_list: Vec<UpDeviceAttributeModel>) {
        self.engine_attribute_list = attribute_list;
    }

    pub fn set_engine_caution_list(&mut self, caution_list: Vec<LECaution>) {
        self.engine_caution_list = caution_list;
    }
}

#[derive(Default, Clone, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct UpDeviceAttributeModel {
    pub name: String,
    pub code: Vec<String>,
    pub operation_type: String,
    pub readable: bool,
    pub writable: bool,
    pub invisible: bool,
    pub default_value: Option<String>,
    pub desc: Option<String>,
    pub value: Option<String>,
    pub value_range: ValueRange,
}

impl From<&Arc<LEAttribute>> for UpDeviceAttributeModel {
    fn from(attr: &Arc<LEAttribute>) -> Self {
        UpDeviceAttributeModel {
            name: attr.name().to_string(),
            code: attr.codes().to_owned(),
            operation_type: attr.operation_type().to_string(),
            readable: attr.readable(),
            writable: attr.writable(),
            invisible: attr.invisible(),
            default_value: attr.default_value().to_owned(),
            desc: attr.desc().to_owned(),
            value: attr.value().to_owned(),
            value_range: attr.value_range().into()
        }
    }
}