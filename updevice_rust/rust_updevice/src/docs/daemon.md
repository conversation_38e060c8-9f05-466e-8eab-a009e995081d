# Daemon模块

Daemon模块位于`src/daemon`目录下，负责设备的生命周期管理，包括设备的准备、释放、状态维护等核心功能。

## 模块结构

```
src/daemon/
├── device_daemon.rs         # 设备守护进程
├── device_prepare.rs        # 设备准备器
├── device_attach.rs         # 设备附加接口
├── extend_api_prepare.rs    # 扩展API准备器
├── preparing_state.rs       # 准备状态枚举
├── channel.rs              # 消息通道
└── mod.rs
```

## 核心组件

### 1. UpDeviceDaemon (设备守护进程)

`UpDeviceDaemon`是设备生命周期管理的核心组件，负责设备的存储、准备队列管理和释放处理。

#### 数据结构

```rust
pub struct UpDeviceDaemon {
    /// 用于存储当前设备映射关系
    device_map: Arc<DashMap<String, Arc<dyn UpDevice>>>,
    /// 运行状态
    is_prepare_running: AtomicBool,
    is_release_running: AtomicBool,
    /// 准备队列
    tx_prepare: Option<Sender<DeviceMessage<String>>>,
    prepare_cache: Arc<RwLock<VecDeque<DeviceMessage<String>>>>,
    /// 释放队列
    tx_release: Option<Sender<DeviceMessage<String>>>,
    release_map: Arc<DashMap<String, Arc<dyn UpDevice>>>,
}
```

#### 设备管理

```rust
impl UpDeviceDaemon {
    /// 获取设备
    pub fn get_device(&self, device_id: &str) -> Option<Arc<dyn UpDevice>> {
        if let Some(device) = self.device_map.get(device_id) {
            Some(device.value().clone())
        } else {
            None
        }
    }

    /// 添加设备
    pub fn put_device(&self, device: Arc<dyn UpDevice>) {
        let device_id = device.device_id();
        self.device_map.insert(device_id, device);
    }

    /// 移除设备
    pub fn remove_device(&self, device_id: &str) {
        let option = self.device_map.remove(device_id);
        if let Some((device_id, device)) = option {
            self.release_map.insert(device_id.clone(), device);
            self.add_to_release(device_id);
        }
    }
}
```

#### 设备准备队列

```mermaid
graph LR
    A[设备创建] --> B[添加到准备队列]
    B --> C[准备通道]
    C --> D[异步准备任务]
    D --> E[设备准备]
    E --> F{准备成功?}
    F -->|是| G[状态更新]
    F -->|否| H[重新入队]
    H --> C
    G --> I[准备完成]
```

```rust
impl UpDeviceDaemon {
    /// 发送设备准备消息
    pub fn add_to_prepare(&self, device_id: &str) {
        let device_id = device_id.to_string();
        match &self.tx_prepare {
            Some(tx_prepare) => {
                let sender = tx_prepare.clone();
                send_prepare_message(device_id, sender)
            }
            None => {
                self.prepare_cache
                    .write()
                    .push_back(DeviceMessage::Normal(device_id));
            }
        }
    }

    /// 发送优先的设备准备消息
    pub fn add_to_prepare_front(&self, device_id: &str) {
        let device_id = device_id.to_string();
        match &self.tx_prepare {
            Some(tx_prepare) => {
                let sender = tx_prepare.clone();
                send_permit_message(device_id.clone(), sender);
                info!("add to prepare queue front success: {}", device_id);
            }
            None => self
                .prepare_cache
                .write()
                .push_front(DeviceMessage::Normal(device_id)),
        }
    }
}
```

#### 准备任务处理

```rust
async fn handle_prepare_task(
    mut rx: Receiver<DeviceMessage<String>>,
    tx: Sender<DeviceMessage<String>>,
) {
    while let Some(message) = rx.recv().await {
        match message {
            DeviceMessage::Normal(device_id) => {
                if let Some(device) = UpDeviceInjection::get_instance()
                    .get_device_manager()
                    .device_daemon
                    .get_device(&device_id)
                {
                    let result = device.prepare().await;
                    info!("device[{}] prepare result{:?}.", device_id, result);
                    
                    if let Err(e) = result {
                        error!("prepare device failed: {}", e);
                        tokio::time::sleep(Duration::from_millis(100)).await;
                        // 失败重新发送加入消息队列
                        send_prepare_message(device_id.clone(), tx.clone());
                    } else {
                        // 准备成功，更新设备信息
                        let device_info = UpDeviceInjection::get_instance()
                            .get_device_toolkit()
                            .get_device_info(&device_id);
                        if let Ok(device_info) = device_info {
                            device.process_device_info_change(device_info);
                        }
                    }
                }
            }
            DeviceMessage::Permit(device_id) => {
                // 优先处理的设备准备
                // 类似Normal处理，但优先级更高
            }
            DeviceMessage::Stop => {
                info!("prepare task stopped");
                break;
            }
        }
    }
}
```

### 2. DevicePrepare (设备准备器)

`DevicePrepare`管理单个设备的准备过程，包括设备附加和扩展API准备。

#### 数据结构

```rust
pub struct DevicePrepare {
    device_id: String,
    device_attach: Box<dyn DeviceAttach>,
    extend_api_prepare: Box<dyn ExtendApiPrepare>,
    state: RwLock<PreparingState>,
}
```

#### 准备流程

```mermaid
stateDiagram-v2
    [*] --> Released
    Released --> Preparing: prepare()
    Preparing --> Prepared: 成功
    Preparing --> Released: 失败
    Prepared --> Releasing: release()
    Releasing --> Released: 完成
    Prepared --> Reloading: reset()
    Reloading --> Released: 完成
```

```rust
impl DevicePrepare {
    pub async fn prepare(&self) -> Result<()> {
        if !self.check_state(PreparingState::Released) {
            warn!("device {} already prepared", self.device_id);
            return Ok(());
        }

        self.change_state(PreparingState::Preparing);
        info!("device[{}] prepare start", self.device_id);
        
        // 1. 设备附加
        let result = self.device_attach.attach();
        info!("device[{}] attach result:{:?}", self.device_id, result);
        if result.is_err() {
            self.change_state(PreparingState::Released);
            return result;
        }

        // 2. 扩展API准备
        let result = self.extend_api_prepare.prepare().await;
        if result.is_err() {
            self.change_state(PreparingState::Released);
        } else {
            self.change_state(PreparingState::Prepared);
        }
        result
    }

    pub async fn release(&self) -> Result<()> {
        self.extend_api_prepare.release()?;
        self.device_attach.detach()?;
        let mut config_state = self.state.write();
        *config_state = PreparingState::Released;
        Ok(())
    }

    pub async fn reset(&self) {
        self.change_state(PreparingState::Released);
        self.extend_api_prepare.reset().await;
    }
}
```

### 3. DeviceAttach (设备附加接口)

`DeviceAttach`定义了设备附加的抽象接口：

```rust
pub trait DeviceAttach: Send + Sync {
    fn attach(&self) -> Result<()>;
    fn detach(&self) -> Result<()>;
    fn is_attached(&self) -> bool;
}
```

#### 实现类型

1. **DefaultDeviceAttach**: 默认实现，直接返回成功
2. **ToolkitDeviceAttach**: 通过工具包进行设备附加

```rust
pub struct ToolkitDeviceAttach {
    device_id: String,
    is_attached: AtomicBool,
}

impl DeviceAttach for ToolkitDeviceAttach {
    fn attach(&self) -> Result<()> {
        let result = UpDeviceInjection::get_instance()
            .get_device_toolkit()
            .attach_device(&self.device_id);
        
        match result {
            Ok(_) => {
                self.is_attached.store(true, Ordering::SeqCst);
                Ok(())
            }
            Err(e) => Err(DeviceError::from(e)),
        }
    }

    fn detach(&self) -> Result<()> {
        let result = UpDeviceInjection::get_instance()
            .get_device_toolkit()
            .detach_device(&self.device_id);
        
        self.is_attached.store(false, Ordering::SeqCst);
        result.map_err(DeviceError::from)
    }

    fn is_attached(&self) -> bool {
        self.is_attached.load(Ordering::SeqCst)
    }
}
```

### 4. ExtendApiPrepare (扩展API准备器)

`ExtendApiPrepare`负责设备扩展功能的准备，如LogicEngine初始化：

```rust
#[async_trait]
pub trait ExtendApiPrepare: Send + Sync {
    async fn prepare(&self) -> Result<()>;
    fn release(&self) -> Result<()>;
    async fn reset(&self);
    fn get_logic_engine(&self) -> Arc<tokio::sync::RwLock<Option<LogicEngine>>>;
    fn get_config_state(&self) -> UpDeviceConfigState;
}
```

#### 实现类型

1. **DefaultDevicePrepare**: 默认实现，无扩展功能
2. **EngineDevicePrepare**: 引擎设备准备，初始化LogicEngine

```rust
pub struct EngineDevicePrepare {
    device_id: String,
    logic_engine: Arc<tokio::sync::RwLock<Option<LogicEngine>>>,
}

impl ExtendApiPrepare for EngineDevicePrepare {
    async fn prepare(&self) -> Result<()> {
        // 1. 获取设备配置
        let config_data_source = UpDeviceInjection::get_instance().get_config_data_source();
        let device_config = config_data_source.get_device_config(&self.device_id).await?;
        
        // 2. 创建LogicEngine
        let logic_engine = LogicEngine::new(device_config)?;
        
        // 3. 初始化LogicEngine
        logic_engine.init().await?;
        
        // 4. 存储LogicEngine实例
        let mut guard = self.logic_engine.write().await;
        *guard = Some(logic_engine);
        
        Ok(())
    }

    fn release(&self) -> Result<()> {
        // 释放LogicEngine资源
        get_task_manager().spawn(async {
            let mut guard = self.logic_engine.write().await;
            if let Some(logic_engine) = guard.take() {
                logic_engine.release().await;
            }
        });
        Ok(())
    }

    async fn reset(&self) {
        let mut guard = self.logic_engine.write().await;
        if let Some(logic_engine) = guard.as_ref() {
            logic_engine.reset().await;
        }
    }
}
```

### 5. PreparingState (准备状态)

```rust
#[derive(Debug, Clone, PartialEq)]
pub enum PreparingState {
    /// 已释放
    Released,
    /// 准备中
    Preparing,
    /// 已准备
    Prepared,
    /// 释放中
    Releasing,
    /// 重新加载中
    Reloading,
}
```

### 6. 消息通道

设备守护进程使用消息通道进行异步通信：

```rust
#[derive(Debug)]
pub enum DeviceMessage<T> {
    /// 普通消息
    Normal(T),
    /// 优先消息
    Permit(T),
    /// 停止消息
    Stop,
}
```

## 工作流程

### 设备准备完整流程

```mermaid
sequenceDiagram
    participant DM as DeviceManager
    participant DD as DeviceDaemon
    participant DP as DevicePrepare
    participant DA as DeviceAttach
    participant EAP as ExtendApiPrepare
    participant LE as LogicEngine

    DM->>DD: put_device(device)
    DM->>DD: add_to_prepare(device_id)
    DD->>DD: 加入准备队列
    
    Note over DD: 异步准备任务
    DD->>DP: prepare()
    DP->>DP: 检查状态
    DP->>DP: 设置Preparing状态
    DP->>DA: attach()
    DA-->>DP: 附加结果
    
    alt 附加成功
        DP->>EAP: prepare()
        EAP->>LE: 初始化LogicEngine
        LE-->>EAP: 初始化结果
        EAP-->>DP: 准备结果
        
        alt 准备成功
            DP->>DP: 设置Prepared状态
            DP-->>DD: 准备成功
        else 准备失败
            DP->>DP: 设置Released状态
            DP-->>DD: 准备失败
            DD->>DD: 重新入队
        end
    else 附加失败
        DP->>DP: 设置Released状态
        DP-->>DD: 准备失败
        DD->>DD: 重新入队
    end
```

## 性能优化

### 1. 并发控制

```rust
// 使用DashMap提供并发安全的设备存储
device_map: Arc<DashMap<String, Arc<dyn UpDevice>>>,

// 使用AtomicBool控制运行状态
is_prepare_running: AtomicBool,
is_release_running: AtomicBool,
```

### 2. 异步处理

```rust
// 异步准备任务，避免阻塞主线程
async fn handle_prepare_task(
    mut rx: Receiver<DeviceMessage<String>>,
    tx: Sender<DeviceMessage<String>>,
) {
    // 异步处理准备消息
}

// 异步释放任务
async fn handle_release_task(
    mut rx: Receiver<DeviceMessage<String>>,
) {
    // 异步处理释放消息
}
```

### 3. 队列管理

```rust
// 支持优先级队列
pub fn add_to_prepare_front(&self, device_id: &str) {
    // 优先级设备插入队列前端
    self.prepare_cache.write().push_front(DeviceMessage::Normal(device_id));
}

// 缓存队列，在通道未初始化时暂存消息
prepare_cache: Arc<RwLock<VecDeque<DeviceMessage<String>>>>,
```

## 错误处理

### 1. 准备失败重试

```rust
if let Err(e) = result {
    error!("prepare device failed: {}", e);
    tokio::time::sleep(Duration::from_millis(100)).await;
    // 失败重新发送加入消息队列
    send_prepare_message(device_id.clone(), tx.clone());
}
```

### 2. 状态一致性

```rust
fn check_state(&self, target: PreparingState) -> bool {
    let state = self.state.read();
    match state.deref() {
        PreparingState::Reloading => false,
        PreparingState::Releasing => false,
        PreparingState::Preparing => false,
        _ => *state == target,
    }
}
```

## 最佳实践

### 1. 设备生命周期管理

```rust
// 创建设备后立即添加到守护进程
let device = device_factory.create_device(device_id, &device_info)?;
device_daemon.put_device(Arc::from(device));
device_daemon.add_to_prepare(device_id);

// 设备不再需要时及时释放
device_daemon.remove_device(device_id);
```

### 2. 优先级处理

```rust
// 当前家庭设备优先准备
if is_current_family_device {
    device_daemon.add_to_prepare_front(device_id);
} else {
    device_daemon.add_to_prepare(device_id);
}
```

### 3. 资源清理

```rust
// 应用退出时清理所有设备
impl Drop for UpDeviceDaemon {
    fn drop(&mut self) {
        // 停止准备和释放任务
        self.stop_prepare();
        self.stop_release();
        
        // 释放所有设备
        for device in self.device_map.iter() {
            let _ = device.release();
        }
    }
}
```
