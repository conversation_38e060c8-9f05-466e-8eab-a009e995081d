# Device模块

Device模块位于`src/device`目录下，是UpDevice库的核心设备抽象层，定义了设备的基本接口和各种具体设备实现。

## 模块结构

```
src/device/
├── up_device.rs              # 设备抽象接口
├── device_core.rs            # 设备核心实现
├── engine_device.rs          # 引擎设备
├── aggregate_device.rs       # 聚合设备
├── washing_device.rs         # 洗衣机设备
├── common_device.rs          # 通用设备
├── not_net_device.rs         # 非网络设备
├── extend_api.rs             # 扩展API接口
├── engine_device_extend.rs   # 引擎设备扩展
├── empty_device_extend.rs    # 空设备扩展
├── engine_type_id_white_list.rs # 引擎类型白名单
├── compat/                   # 兼容性设备
│   ├── compat_device.rs
│   ├── compat_device_api.rs
│   └── voice_box/           # 语音盒设备
└── mod.rs
```

## 设备架构

```mermaid
classDiagram
    class UpDevice {
        <<trait>>
        +get_device_core() Arc~UpDeviceCore~
        +get_extend_api() Arc~ExtendApi~
        +device_id() String
        +family_id() String
        +get_info() UpDeviceInfo
        +execute_command() Future~DeviceFeedback~
        +prepare() Future~Result~
        +release() Future~Result~
    }
    
    class UpDeviceCore {
        <<trait>>
        +protocol() String
        +device_id() String
        +get_device_info() UpDeviceInfo
        +get_attribute_list() Vec~UpDeviceAttribute~
        +execute_command() Future~DeviceFeedback~
        +subscribe() String
        +unsubscribe()
    }
    
    class DeviceCore {
        -unique_id: String
        -device_info: RwLock~UpDeviceInfo~
        -device_prepare: DevicePrepare
        -attributes: RwLock~HashMap~
        -connect_state: RwLock~UpDeviceConnectState~
        -online_state: RwLock~UpDeviceOnlineState~
    }
    
    class UpEngineDevice {
        -device_core: Arc~DeviceCore~
        -device_extend: Arc~EngineDeviceExtend~
        -command_queue: CommandQueue
        -last_operation: RwLock~Instant~
    }
    
    class UpAggregateDevice {
        -device_core: Arc~DeviceCore~
        -extend_api: Arc~EmptyDeviceExtend~
    }
    
    class UpWashingDevice {
        -device_core: Arc~DeviceCore~
        -extend_api: Arc~EmptyDeviceExtend~
    }
    
    class UpCommonDevice {
        -device_core: Arc~DeviceCore~
    }
    
    UpDevice <|.. UpEngineDevice
    UpDevice <|.. UpAggregateDevice
    UpDevice <|.. UpWashingDevice
    UpDevice <|.. UpCommonDevice
    UpDeviceCore <|.. DeviceCore
    UpEngineDevice --> DeviceCore
    UpAggregateDevice --> DeviceCore
    UpWashingDevice --> DeviceCore
    UpCommonDevice --> DeviceCore
```

## 核心接口

### 1. UpDevice Trait

`UpDevice`是所有设备的抽象接口，定义了设备的基本操作。

```rust
#[async_trait]
pub trait UpDevice: Send + Sync {
    // 核心组件访问
    fn get_device_core(&self) -> Arc<dyn UpDeviceCore>;
    fn get_extend_api(&self) -> Arc<dyn ExtendApi>;
    
    // 基本信息
    fn device_id(&self) -> String;
    fn family_id(&self) -> String;
    fn model(&self) -> String;
    fn type_id(&self) -> Option<String>;
    fn get_info(&self) -> UpDeviceInfo;
    
    // 状态管理
    fn get_config_state(&self) -> UpDeviceConfigState;
    fn get_connect_state(&self) -> UpDeviceConnectState;
    fn get_online_state(&self) -> UpDeviceOnlineState;
    
    // 属性和告警
    fn get_attribute_list(&self) -> Vec<UpDeviceAttribute>;
    fn get_caution_list(&self) -> Vec<UpDeviceCaution>;
    
    // 生命周期管理
    async fn prepare(&self) -> Result<()>;
    async fn release(&self) -> Result<()>;
    
    // 命令执行
    async fn execute_command(&self, command: UpDeviceCommand) -> Result<DeviceFeedback>;
    async fn operate(&self, commands: Vec<Command>) -> Result<DeviceFeedback>;
    
    // 事件订阅
    fn subscribe(&self, listener: UpDeviceListener) -> String;
    fn unsubscribe(&self, subscription_id: &str);
}
```

### 2. UpDeviceCore Trait

`UpDeviceCore`定义了设备核心功能的接口。

```rust
#[async_trait]
pub trait UpDeviceCore: Send + Sync {
    // 基本信息
    fn protocol(&self) -> String;
    fn device_id(&self) -> String;
    fn unique_id(&self) -> String;
    fn family_id(&self) -> String;
    
    // 状态处理
    fn process_attribute_change(&self, attributes: Vec<UpDeviceAttribute>);
    fn process_connect_state_change(&self, connect_state: UpDeviceConnectState);
    fn process_online_state_change(&self, online_state: UpDeviceOnlineState);
    fn process_device_info_change(&self, info: DeviceInfo);
    
    // 生命周期
    async fn prepare(&self) -> Result<()>;
    async fn release(&self) -> Result<()>;
    async fn reset(&self);
    
    // 命令执行
    async fn execute_command(&self, command: UpDeviceCommand) -> Result<DeviceFeedback>;
    
    // 状态查询
    fn get_state(&self) -> PreparingState;
    fn is_attached(&self) -> bool;
}
```

## 设备类型

### 1. EngineDevice (引擎设备)

引擎设备是支持逻辑引擎的智能设备，具有复杂的属性计算和命令处理能力。

#### 特性
- 集成LogicEngine进行属性计算
- 支持命令队列和批量操作
- 具有扩展API能力
- 支持设备重置和状态恢复

#### 核心实现
```rust
impl UpEngineDevice {
    pub fn new(unique_id: &str, device_info: UpDeviceInfo) -> Self {
        let device_prepare = DevicePrepare::new(
            device_info.device_id(),
            Box::new(ToolkitDeviceAttach::new(device_info.device_id())),
            Box::new(EngineDevicePrepare::new(device_info.device_id())),
        );
        // ...
    }
    
    async fn operate(&self, commands: Vec<Command>) -> Result<DeviceFeedback> {
        let logic_engine = self.device_core.get_logic_engine();
        let guard = logic_engine.read().await;
        match guard.as_ref() {
            Some(logic_engine) => {
                let command_vec = logic_engine.prepare_operate_commands(commands)?;
                for command in command_vec {
                    result = self.get_device_core().execute_command(command).await?;
                }
                result
            }
            None => Err(DeviceError::LogicEngineError(...))
        }
    }
}
```

#### 命令处理流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant ED as EngineDevice
    participant LE as LogicEngine
    participant DC as DeviceCore
    participant SDK as DeviceSDK

    App->>ED: operate(commands)
    ED->>LE: prepare_operate_commands()
    LE-->>ED: device_commands
    
    loop 遍历设备命令
        ED->>DC: execute_command()
        DC->>SDK: 发送命令
        SDK-->>DC: DeviceFeedback
        DC-->>ED: Result
    end
    
    ED-->>App: 最终结果
```

### 2. AggregateDevice (聚合设备)

聚合设备是包含多个子设备的复合设备。

#### 特性
- 管理多个子设备
- 聚合子设备状态
- 支持批量操作
- 简化的设备准备流程

```rust
impl UpAggregateDevice {
    pub fn new(unique_id: String, device_info: UpDeviceInfo) -> Self {
        let device_prepare = DevicePrepare::new(
            device_info.device_id(),
            Box::new(DefaultDeviceAttach::new()),
            Box::new(DefaultDevicePrepare::new()),
        );
        UpAggregateDevice {
            device_core: Arc::new(DeviceCore::new(unique_id, device_info, device_prepare)),
            extend_api: Arc::new(EmptyDeviceExtend {}),
        }
    }
}
```

### 3. WashingDevice (洗衣机设备)

洗衣机设备是特定类型的智能设备，具有特殊的配置状态判断。

#### 特性
- 特定的设备附加逻辑
- 自定义配置状态判断
- 支持原生设备检测

```rust
impl UpWashingDevice {
    fn get_config_state(&self) -> UpDeviceConfigState {
        if self.device_core.is_attached() {
            UpDeviceConfigState::NativeDevice
        } else {
            UpDeviceConfigState::Unknown
        }
    }
}
```

### 4. CommonDevice (通用设备)

通用设备是默认的设备实现，适用于大多数标准设备。

#### 特性
- 标准的设备功能
- 工具包设备附加
- 空扩展API

## DeviceCore实现

`DeviceCore`是所有设备的核心实现，管理设备的状态、属性和生命周期。

### 核心数据结构

```rust
pub struct DeviceCore {
    unique_id: String,
    device_info: RwLock<UpDeviceInfo>,
    device_prepare: DevicePrepare,
    attributes: RwLock<HashMap<String, UpDeviceAttribute>>,
    cautions: RwLock<HashMap<String, UpDeviceCaution>>,
    connect_state: RwLock<UpDeviceConnectState>,
    ble_state: RwLock<UpDeviceConnectState>,
    online_state: RwLock<UpDeviceOnlineState>,
    sleep_state: RwLock<UpDeviceSleepState>,
    state_code: RwLock<i32>,
    offline_cause: RwLock<UpDeviceOfflineCause>,
    offline_days: RwLock<u32>,
}
```

### 状态管理

```rust
impl DeviceCore {
    // 处理属性变化
    fn process_attribute_change(&self, attributes: Vec<UpDeviceAttribute>) {
        let mut attr_map = self.attributes.write();
        for attribute in attributes {
            let old_value = attr_map.get(&attribute.name).cloned();
            attr_map.insert(attribute.name.clone(), attribute.clone());
            
            // 发送属性变化事件
            self.publish_attribute_change_event(attribute, old_value);
        }
    }
    
    // 处理连接状态变化
    fn process_connect_state_change(&self, connect_state: UpDeviceConnectState) {
        let old_state = *self.connect_state.read();
        if old_state != connect_state {
            *self.connect_state.write() = connect_state;
            self.publish_state_change_event("connect_state", old_state, connect_state);
        }
    }
}
```

### 命令执行

```rust
impl DeviceCore {
    async fn execute_command(&self, command: UpDeviceCommand) -> Result<DeviceFeedback> {
        match self.device_prepare.get_state() {
            PreparingState::Prepared => {
                let device_feedback = UpDeviceInjection::get_instance()
                    .get_device_toolkit()
                    .execute_command(
                        &self.device_id(),
                        command,
                        TIME_OUT,
                        DeviceCtrlRoute::UHSD_USR_DEVICE_CTRL_ROUTE_DEFAULT,
                    )
                    .await?;

                match device_feedback.error_info.code {
                    0 => Ok(device_feedback),
                    _ => Err(DeviceError::ExecuteCommandError(...)),
                }
            }
            _ => Err(DeviceError::DeviceNotPrepared(self.device_id())),
        }
    }
}
```

## 设备生命周期

### 设备准备流程

```mermaid
stateDiagram-v2
    [*] --> Released
    Released --> Preparing: prepare()
    Preparing --> Prepared: 成功
    Preparing --> Released: 失败
    Prepared --> Releasing: release()
    Releasing --> Released: 完成
    Prepared --> Reloading: reset()
    Reloading --> Released: 完成
```

### 准备步骤

1. **设备附加**: 通过DeviceAttach建立设备连接
2. **扩展API准备**: 初始化LogicEngine等扩展功能
3. **状态更新**: 更新设备准备状态
4. **事件通知**: 发送设备准备完成事件

## 扩展API

### ExtendApi接口

```rust
pub trait ExtendApi: Any + Send + Sync {
    // 查询初始属性列表
    fn query_initial_attributes(&self) -> Vec<Arc<LEAttribute>>;
    
    // 查询初始告警列表
    fn query_initial_cautions(&self) -> Vec<LECaution>;
    
    // 查询属性列表
    fn query_attributes(&self) -> Vec<Arc<LEAttribute>>;
    
    // 查询告警列表
    fn query_cautions(&self) -> Vec<LECaution>;
    
    // 查询命令列表
    fn query_commands(&self) -> Vec<UpDeviceCommand>;
}
```

### 实现类型

- **EngineDeviceExtend**: 引擎设备扩展，集成LogicEngine
- **EmptyDeviceExtend**: 空扩展，用于简单设备

## 最佳实践

### 1. 设备创建
```rust
// 通过工厂创建设备
let device = device_factory.create_device(device_id, &device_info)?;

// 添加到守护进程管理
device_daemon.put_device(Arc::from(device));
device_daemon.add_to_prepare(device_id);
```

### 2. 状态监控
```rust
// 订阅设备状态变化
let subscription_id = device.subscribe(Box::new(|event| {
    match event {
        UpDeviceEvent::DeviceStateChanged { device_id, state_type, new_value, .. } => {
            println!("设备 {} 状态 {} 变为: {}", device_id, state_type, new_value);
        }
        _ => {}
    }
}));
```

### 3. 命令执行
```rust
// 执行单个命令
let command = UpDeviceCommand::new("power", "on");
let feedback = device.execute_command(command).await?;

// 执行批量操作（引擎设备）
let commands = vec![
    Command::new("power", "on"),
    Command::new("temperature", "25"),
];
let feedback = device.operate(commands).await?;
```

### 4. 资源管理
```rust
// 及时释放设备资源
device.release().await?;

// 取消事件订阅
device.unsubscribe(&subscription_id);
```
