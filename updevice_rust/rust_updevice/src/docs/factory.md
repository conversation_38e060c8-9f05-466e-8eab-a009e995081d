# Factory模块

Factory模块位于`src/factory`目录下，实现了设备创建的工厂模式，负责根据设备信息创建对应的设备实例。

## 模块结构

```
src/factory/
├── device_factory.rs         # 设备工厂接口和实现
├── device_creator.rs         # 设备创建器
├── utils/
│   ├── type_ids.rs          # 设备类型ID工具
│   └── mod.rs
└── mod.rs
```

## 设计模式

Factory模块采用了多种设计模式：

1. **工厂方法模式**: `UpDeviceFactory` trait定义创建接口
2. **抽象工厂模式**: `DeviceFactory`实现具体的设备创建逻辑
3. **责任链模式**: `DeviceCreator`支持多工厂链式创建
4. **策略模式**: 根据设备类型选择不同的创建策略

## 核心组件

### 1. UpDeviceFactory Trait

定义了设备工厂的抽象接口：

```rust
#[mry::mry]
pub trait UpDeviceFactory: Send + Sync {
    fn create_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>>;
}
```

### 2. DeviceFactory 实现

`DeviceFactory`是默认的设备工厂实现，支持多种设备类型的创建：

```rust
pub struct DeviceFactory;

impl DeviceFactory {
    const NON_NET_DEVICE: &'static str = "nonNetDevice";
    
    pub fn new() -> Self {
        DeviceFactory {}
    }
}
```

#### 设备创建流程

```mermaid
flowchart TD
    A[开始创建设备] --> B[检查语音盒设备]
    B --> C{是语音盒?}
    C -->|是| D[创建VoiceBox设备]
    C -->|否| E[检查洗衣机设备]
    E --> F{是洗衣机?}
    F -->|是| G[创建WashingDevice]
    F -->|否| H[检查聚合设备]
    H --> I{是聚合设备?}
    I -->|是| J[创建AggregateDevice]
    I -->|否| K[检查非网络设备]
    K --> L{是非网络设备?}
    L -->|是| M[创建NotNetDevice]
    L -->|否| N[创建EngineDevice]
    D --> O[返回设备实例]
    G --> O
    J --> O
    M --> O
    N --> O
```

#### 具体创建方法

```rust
impl UpDeviceFactory for DeviceFactory {
    fn create_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        // 1. 尝试创建语音盒设备
        let mut device = self.create_voice_box(device_id, device_info);

        // 2. 尝试创建洗衣机设备
        if device.is_none() {
            device = self.create_washing_device(device_id, device_info);
        }

        // 3. 尝试创建聚合设备
        if device.is_none() {
            device = self.create_aggregate_device(device_id, device_info);
        }

        // 4. 尝试创建非网络设备
        if device.is_none() {
            device = self.create_not_net_device(device_id, device_info);
        }

        // 5. 默认创建引擎设备
        if device.is_none() {
            device = self.create_engine_device(device_id, device_info);
        }

        device
    }
}
```

### 3. 具体设备创建方法

#### 语音盒设备创建

```rust
fn create_voice_box(
    &self,
    device_id: &str,
    device_info: &UpDeviceInfo,
) -> Option<Box<dyn UpDevice>> {
    let mut device = Option::<Box<dyn UpDevice>>::None;

    if TypeIds::is_voice_box_dot(device_info.type_id()) {
        device = Some(Box::new(VoiceBoxDot::new(device_id, device_info.clone())));
    } else if TypeIds::is_voice_box_dot2(device_info.type_id())
        || TypeIds::is_voice_box_dot3(device_info.type_id())
    {
        device = Some(Box::new(VoiceBoxDot2::new(device_id, device_info.clone())));
    }

    device
}
```

#### 洗衣机设备创建

```rust
fn create_washing_device(
    &self,
    device_id: &str,
    device_info: &UpDeviceInfo,
) -> Option<Box<dyn UpDevice>> {
    let mut device = Option::<Box<dyn UpDevice>>::None;

    if device_info.type_id().is_some()
        && TypeIds::is_washing_machine(&device_info.type_id().unwrap())
    {
        device = Some(Box::new(UpWashingDevice::new(
            device_id.to_string(),
            device_info.clone(),
        )));
    }

    device
}
```

#### 聚合设备创建

```rust
fn create_aggregate_device(
    &self,
    device_id: &str,
    device_info: &UpDeviceInfo,
) -> Option<Box<dyn UpDevice>> {
    let mut device = Option::<Box<dyn UpDevice>>::None;
    if !device_info
        .get_basic()
        .device_aggregate_type().is_empty()
    {
        device = Some(Box::new(UpAggregateDevice::new(
            device_id.to_string(),
            device_info.clone(),
        )));
    }
    device
}
```

#### 非网络设备创建

```rust
fn create_not_net_device(
    &self,
    device_id: &str,
    device_info: &UpDeviceInfo,
) -> Option<Box<dyn UpDevice>> {
    let mut device = Option::<Box<dyn UpDevice>>::None;

    if device_info
        .get_basic()
        .device_net_type()
        .unwrap_or("".to_string())
        == Self::NON_NET_DEVICE
    {
        device = Some(Box::new(UpNotNetDevice::new(
            device_id.to_string(),
            device_info.clone(),
        )));
    }

    device
}
```

#### 引擎设备创建（默认）

```rust
fn create_engine_device(
    &self,
    device_id: &str,
    device_info: &UpDeviceInfo,
) -> Option<Box<dyn UpDevice>> {
    Some(Box::new(UpEngineDevice::new(
        device_id,
        device_info.clone(),
    )))
}
```

### 4. DeviceCreator (设备创建器)

`DeviceCreator`实现了责任链模式，支持多个工厂的链式创建：

```rust
pub struct UpDeviceCreator {
    device_factories: RwLock<Vec<Box<dyn UpDeviceFactory>>>,
}

impl UpDeviceCreator {
    pub fn new() -> Self {
        UpDeviceCreator {
            device_factories: RwLock::new(vec![]),
        }
    }

    // 追加设备工厂
    pub fn append_device_factory(&self, factory: Box<dyn UpDeviceFactory>) {
        let mut factories = self.device_factories.write();
        factories.push(factory);
    }

    // 前置设备工厂（优先级更高）
    pub fn prepend_device_factory(&self, factory: Box<dyn UpDeviceFactory>) {
        let mut factories = self.device_factories.write();
        factories.insert(0, factory);
    }
}
```

#### 创建器工作流程

```mermaid
sequenceDiagram
    participant DM as DeviceManager
    participant DC as DeviceCreator
    participant F1 as Factory1
    participant F2 as Factory2
    participant F3 as DefaultFactory

    DM->>DC: create_device(device_id, device_info)
    DC->>F1: create_device()
    F1-->>DC: None
    DC->>F2: create_device()
    F2-->>DC: Some(device)
    Note over DC: 找到合适的工厂，停止遍历
    DC-->>DM: device
    
    Note over DM: 如果所有工厂都返回None
    DC->>DC: 创建CommonDevice作为默认
    DC-->>DM: CommonDevice
```

#### 责任链实现

```rust
impl UpDeviceFactory for UpDeviceCreator {
    fn create_device(
        &self,
        unique_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        // 尝试通过注册的工厂创建
        let mut device = self.try_create_device(unique_id, device_info);
        
        // 如果所有工厂都无法创建，则创建通用设备
        if device.is_none() {
            device = Some(Box::new(UpCommonDevice::new(
                unique_id,
                device_info.clone(),
            )));
        }

        device
    }
}

fn try_create_device(
    &self,
    unique_id: &str,
    device_info: &UpDeviceInfo,
) -> Option<Box<dyn UpDevice>> {
    let device_factories = self.device_factories.read();
    let mut device = None;
    
    // 遍历所有工厂，直到找到能创建设备的工厂
    for factory in device_factories.iter() {
        device = factory.create_device(unique_id, device_info);
        if device.is_some() {
            break;
        }
    }
    device
}
```

### 5. TypeIds 工具类

`TypeIds`提供设备类型判断的工具方法：

```rust
pub struct TypeIds;

impl TypeIds {
    // 语音盒类型判断
    pub fn is_voice_box_dot(type_id: &Option<String>) -> bool {
        VoiceBoxTypeIds::is_voice_box_dot(type_id)
    }
    
    pub fn is_voice_box_dot2(type_id: &Option<String>) -> bool {
        VoiceBoxTypeIds::is_voice_box_dot2(type_id)
    }
    
    pub fn is_voice_box_dot3(type_id: &Option<String>) -> bool {
        VoiceBoxTypeIds::is_voice_box_dot3(type_id)
    }
    
    // 洗衣机类型判断
    pub fn is_washing_machine(type_id: &str) -> bool {
        WashingMachineTypeIds::is_washing_machine(type_id)
    }
}
```

## 扩展性设计

### 1. 添加新设备类型

要添加新的设备类型，需要：

1. **创建设备实现类**：
```rust
pub struct MyCustomDevice {
    device_core: Arc<DeviceCore>,
    extend_api: Arc<dyn ExtendApi>,
}

impl UpDevice for MyCustomDevice {
    // 实现UpDevice trait
}
```

2. **扩展DeviceFactory**：
```rust
impl DeviceFactory {
    fn create_custom_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        if self.is_custom_device(device_info) {
            Some(Box::new(MyCustomDevice::new(device_id, device_info.clone())))
        } else {
            None
        }
    }
}
```

3. **更新创建流程**：
```rust
impl UpDeviceFactory for DeviceFactory {
    fn create_device(&self, device_id: &str, device_info: &UpDeviceInfo) -> Option<Box<dyn UpDevice>> {
        let mut device = self.create_custom_device(device_id, device_info);
        
        if device.is_none() {
            device = self.create_voice_box(device_id, device_info);
        }
        // ... 其他创建逻辑
    }
}
```

### 2. 自定义工厂

可以创建自定义工厂并注册到创建器：

```rust
pub struct CustomDeviceFactory;

impl UpDeviceFactory for CustomDeviceFactory {
    fn create_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        // 自定义创建逻辑
        if self.can_handle(device_info) {
            Some(Box::new(CustomDevice::new(device_id, device_info.clone())))
        } else {
            None
        }
    }
}

// 注册自定义工厂
let device_manager = UpDeviceInjection::get_instance().get_device_manager();
device_manager.prepend_device_factory(Box::new(CustomDeviceFactory));
```

## 测试支持

Factory模块提供了测试支持：

### FakeDeviceFactory

```rust
pub struct FakeDeviceFactory {}

impl UpDeviceFactory for FakeDeviceFactory {
    fn create_device(
        &self,
        unique_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        Some(Box::new(FakeDevice::new(
            unique_id.to_string(),
            device_info.clone(),
        )))
    }
}
```

### Mock支持

使用`mry`库提供Mock支持：

```rust
#[mry::mry]
pub trait UpDeviceFactory: Send + Sync {
    fn create_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>>;
}
```

## 最佳实践

### 1. 工厂注册顺序

```rust
// 优先级高的工厂先注册
device_manager.prepend_device_factory(Box::new(HighPriorityFactory));
device_manager.append_device_factory(Box::new(LowPriorityFactory));
device_manager.append_device_factory(Box::new(DeviceFactory::new())); // 默认工厂
```

### 2. 设备类型判断

```rust
// 使用TypeIds工具类进行类型判断
if TypeIds::is_voice_box_dot(device_info.type_id()) {
    // 创建语音盒设备
} else if TypeIds::is_washing_machine(&device_info.type_id().unwrap_or_default()) {
    // 创建洗衣机设备
}
```

### 3. 错误处理

```rust
// 工厂方法应该返回Option，而不是抛出异常
fn create_device(&self, device_id: &str, device_info: &UpDeviceInfo) -> Option<Box<dyn UpDevice>> {
    match self.validate_device_info(device_info) {
        Ok(_) => Some(Box::new(MyDevice::new(device_id, device_info.clone()))),
        Err(_) => None, // 返回None让下一个工厂尝试
    }
}
```

### 4. 性能优化

```rust
// 缓存设备类型判断结果
lazy_static! {
    static ref TYPE_ID_CACHE: DashMap<String, DeviceType> = DashMap::new();
}

fn get_device_type(type_id: &str) -> DeviceType {
    TYPE_ID_CACHE.entry(type_id.to_string())
        .or_insert_with(|| determine_device_type(type_id))
        .clone()
}
```
