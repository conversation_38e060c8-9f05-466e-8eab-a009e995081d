# Features模块

Features模块位于`src/features`目录下，负责跨平台FFI接口的实现，支持Android、iOS和HarmonyOS三个平台的原生调用。

## 模块结构

```
src/features/
├── android.rs              # Android平台FFI接口
├── ios.rs                  # iOS平台FFI接口  
├── ohos.rs                 # HarmonyOS平台FFI接口
├── ohos_cmd.rs             # HarmonyOS命令行工具
├── constant.rs             # 常量定义
├── ffi_models.rs           # FFI数据模型
├── flat/                   # FlatBuffers相关
│   ├── cross_platform.rs   # 跨平台序列化
│   ├── updevice_generated.rs # 自动生成的FlatBuffers代码
│   └── mod.rs
└── mod.rs
```

## 平台特性

### 条件编译

```rust
#[cfg(feature = "android")]
mod android;

#[cfg(feature = "ios")]
mod ios;

#[cfg(feature = "ohos")]
mod ohos;

#[cfg(feature = "ohos")]
mod ohos_cmd;
```

### 构建配置

```toml
[features]
default = []
android = ["rust_usdk/android"]
ios = ["rust_usdk/ios"]
ohos = ["dep:napi-ohos", "dep:napi-derive-ohos", "rust_usdk/ohos"]
```

## Android平台

### JNI接口

Android平台使用JNI (Java Native Interface) 提供原生接口：

```rust
// Android FFI接口示例
#[cfg(feature = "android")]
use jni::JNIEnv;
#[cfg(feature = "android")]
use jni::objects::{JClass, JString};
#[cfg(feature = "android")]
use jni::sys::jstring;

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn Java_com_example_UpDevice_initDeviceManager(
    env: JNIEnv,
    _class: JClass,
    app_id: JString,
    app_secret: JString,
    client_id: JString,
) -> jstring {
    // 初始化设备管理器
    let app_id_str: String = env.get_string(app_id).unwrap().into();
    let app_secret_str: String = env.get_string(app_secret).unwrap().into();
    let client_id_str: String = env.get_string(client_id).unwrap().into();
    
    // 调用Rust核心逻辑
    let result = init_device_manager_internal(app_id_str, app_secret_str, client_id_str);
    
    // 返回结果
    env.new_string(result).unwrap().into_inner()
}

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn Java_com_example_UpDevice_getDeviceList(
    env: JNIEnv,
    _class: JClass,
    family_id: JString,
) -> jstring {
    let family_id_str: String = env.get_string(family_id).unwrap().into();
    
    // 获取设备列表
    let devices = get_device_list_internal(Some(family_id_str));
    
    // 序列化为JSON返回
    let json_result = serde_json::to_string(&devices).unwrap_or_default();
    env.new_string(json_result).unwrap().into_inner()
}
```

### 数据传递

```rust
// Android数据模型转换
#[cfg(feature = "android")]
fn convert_device_info_to_android(device_info: &UpDeviceInfo) -> AndroidDeviceInfo {
    AndroidDeviceInfo {
        device_id: device_info.device_id(),
        device_name: device_info.get_base_info().device_name(),
        model: device_info.model(),
        type_id: device_info.type_id().unwrap_or_default(),
        online: device_info.get_basic().online(),
        // ... 其他字段转换
    }
}
```

## iOS平台

### C接口

iOS平台使用C接口和Objective-C桥接：

```rust
#[cfg(feature = "ios")]
use std::ffi::{CStr, CString};
#[cfg(feature = "ios")]
use std::os::raw::c_char;

#[cfg(feature = "ios")]
#[no_mangle]
pub extern "C" fn up_device_init_manager(
    app_id: *const c_char,
    app_secret: *const c_char,
    client_id: *const c_char,
) -> *mut c_char {
    let app_id_str = unsafe { CStr::from_ptr(app_id).to_str().unwrap() };
    let app_secret_str = unsafe { CStr::from_ptr(app_secret).to_str().unwrap() };
    let client_id_str = unsafe { CStr::from_ptr(client_id).to_str().unwrap() };
    
    // 调用核心逻辑
    let result = init_device_manager_internal(
        app_id_str.to_string(),
        app_secret_str.to_string(),
        client_id_str.to_string(),
    );
    
    // 返回C字符串
    CString::new(result).unwrap().into_raw()
}

#[cfg(feature = "ios")]
#[no_mangle]
pub extern "C" fn up_device_get_list(family_id: *const c_char) -> *mut c_char {
    let family_id_str = if family_id.is_null() {
        None
    } else {
        Some(unsafe { CStr::from_ptr(family_id).to_str().unwrap().to_string() })
    };
    
    let devices = get_device_list_internal(family_id_str);
    let json_result = serde_json::to_string(&devices).unwrap_or_default();
    
    CString::new(json_result).unwrap().into_raw()
}

#[cfg(feature = "ios")]
#[no_mangle]
pub extern "C" fn up_device_free_string(ptr: *mut c_char) {
    if !ptr.is_null() {
        unsafe {
            CString::from_raw(ptr);
        }
    }
}
```

### 内存管理

```rust
// iOS平台需要手动管理内存
#[cfg(feature = "ios")]
pub struct IOSStringManager {
    allocated_strings: std::sync::Mutex<Vec<*mut c_char>>,
}

#[cfg(feature = "ios")]
impl IOSStringManager {
    pub fn allocate_string(&self, s: String) -> *mut c_char {
        let c_string = CString::new(s).unwrap();
        let ptr = c_string.into_raw();
        self.allocated_strings.lock().unwrap().push(ptr);
        ptr
    }
    
    pub fn free_all(&self) {
        let mut strings = self.allocated_strings.lock().unwrap();
        for ptr in strings.drain(..) {
            unsafe {
                CString::from_raw(ptr);
            }
        }
    }
}
```

## HarmonyOS平台

### NAPI接口

HarmonyOS平台使用Node-API (NAPI) 接口：

```rust
#[cfg(feature = "ohos")]
use napi_derive_ohos::napi;
#[cfg(feature = "ohos")]
use napi_ohos::{Env, Error, JsObject, Result, Status};

#[cfg(feature = "ohos")]
#[napi]
pub fn init_device_manager(
    app_id: String,
    app_secret: String,
    client_id: String,
) -> Result<String> {
    // 初始化设备管理器
    match init_device_manager_internal(app_id, app_secret, client_id) {
        Ok(result) => Ok(result),
        Err(e) => Err(Error::new(Status::GenericFailure, e.to_string())),
    }
}

#[cfg(feature = "ohos")]
#[napi]
pub fn get_device_list(family_id: Option<String>) -> Result<String> {
    let devices = get_device_list_internal(family_id);
    serde_json::to_string(&devices)
        .map_err(|e| Error::new(Status::GenericFailure, e.to_string()))
}

#[cfg(feature = "ohos")]
#[napi]
pub async fn execute_device_command(
    device_id: String,
    command: String,
    params: String,
) -> Result<String> {
    // 异步执行设备命令
    let result = execute_command_internal(device_id, command, params).await;
    match result {
        Ok(feedback) => serde_json::to_string(&feedback)
            .map_err(|e| Error::new(Status::GenericFailure, e.to_string())),
        Err(e) => Err(Error::new(Status::GenericFailure, e.to_string())),
    }
}
```

### 对象传递

```rust
#[cfg(feature = "ohos")]
#[napi(object)]
pub struct DeviceInfo {
    pub device_id: String,
    pub device_name: String,
    pub model: String,
    pub type_id: Option<String>,
    pub online: bool,
    pub family_id: String,
}

#[cfg(feature = "ohos")]
#[napi(object)]
pub struct CommandResult {
    pub success: bool,
    pub error_code: i32,
    pub error_message: String,
    pub data: Option<String>,
}
```

## FlatBuffers跨平台序列化

### Schema定义

```flatbuffers
// updevice.fbs
namespace UpDevice;

table DeviceInfo {
    device_id: string;
    device_name: string;
    model: string;
    type_id: string;
    online: bool;
    family_id: string;
    attributes: [DeviceAttribute];
}

table DeviceAttribute {
    name: string;
    value: string;
    data_type: string;
}

table DeviceList {
    devices: [DeviceInfo];
}

root_type DeviceList;
```

### 序列化实现

```rust
use flatbuffers::{FlatBufferBuilder, WIPOffset};
use crate::features::flat::updevice_generated::*;

pub fn serialize_device_list(devices: &[UpDeviceInfo]) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();
    
    // 构建设备列表
    let device_offsets: Vec<WIPOffset<DeviceInfo>> = devices
        .iter()
        .map(|device| serialize_device_info(&mut builder, device))
        .collect();
    
    let devices_vector = builder.create_vector(&device_offsets);
    
    // 创建根对象
    let device_list = DeviceList::create(&mut builder, &DeviceListArgs {
        devices: Some(devices_vector),
    });
    
    builder.finish(device_list, None);
    builder.finished_data().to_vec()
}

fn serialize_device_info(
    builder: &mut FlatBufferBuilder,
    device: &UpDeviceInfo,
) -> WIPOffset<DeviceInfo> {
    // 序列化字符串
    let device_id = builder.create_string(&device.device_id());
    let device_name = builder.create_string(&device.get_base_info().device_name());
    let model = builder.create_string(&device.model());
    let family_id = builder.create_string(&device.get_relation().family_id());
    
    // 序列化属性
    let attributes = serialize_attributes(builder, &device.get_attribute_list());
    
    DeviceInfo::create(builder, &DeviceInfoArgs {
        device_id: Some(device_id),
        device_name: Some(device_name),
        model: Some(model),
        family_id: Some(family_id),
        online: device.get_basic().online(),
        attributes: Some(attributes),
        ..Default::default()
    })
}
```

### 反序列化实现

```rust
pub fn deserialize_device_list(data: &[u8]) -> Result<Vec<UpDeviceInfo>, String> {
    let device_list = flatbuffers::root::<DeviceList>(data)
        .map_err(|e| format!("Failed to parse FlatBuffer: {}", e))?;
    
    let devices = device_list.devices()
        .ok_or("No devices found")?;
    
    let mut result = Vec::new();
    for i in 0..devices.len() {
        if let Some(device) = devices.get(i) {
            result.push(deserialize_device_info(device)?);
        }
    }
    
    Ok(result)
}

fn deserialize_device_info(device: DeviceInfo) -> Result<UpDeviceInfo, String> {
    let device_id = device.device_id()
        .ok_or("Missing device_id")?
        .to_string();
    
    let device_name = device.device_name()
        .unwrap_or("")
        .to_string();
    
    let model = device.model()
        .unwrap_or("")
        .to_string();
    
    // 构建UpDeviceInfo
    UpDeviceInfo::builder()
        .device_id(device_id)
        .device_name(device_name)
        .model(model)
        .online(device.online())
        .build()
        .map_err(|e| format!("Failed to build device info: {}", e))
}
```

## 跨平台接口统一

### 核心逻辑抽象

```rust
// 平台无关的核心逻辑
fn init_device_manager_internal(
    app_id: String,
    app_secret: String,
    client_id: String,
) -> Result<String, String> {
    let app_info = AppInfo::new(app_id, app_secret);
    let area = Area::China;
    let environment = DeviceEnvironment::DeviceEnvProduction;
    
    UpDeviceInjection::get_instance().init_device_manager(
        area,
        app_info,
        client_id,
        environment,
    );
    
    Ok("Device manager initialized successfully".to_string())
}

fn get_device_list_internal(family_id: Option<String>) -> Vec<DeviceInfo> {
    let device_manager = UpDeviceInjection::get_instance().get_device_manager();
    
    let devices = match family_id {
        Some(id) => device_manager.get_device_list_by_family_id(id),
        None => device_manager.get_device_list(None),
    };
    
    devices.iter()
        .map(|device| convert_to_ffi_device_info(device))
        .collect()
}

async fn execute_command_internal(
    device_id: String,
    command: String,
    params: String,
) -> Result<CommandResult, String> {
    let device_manager = UpDeviceInjection::get_instance().get_device_manager();
    
    let device = device_manager.get_device(&device_id)
        .ok_or("Device not found")?;
    
    let device_command = UpDeviceCommand::new(command, params);
    
    match device.execute_command(device_command).await {
        Ok(feedback) => Ok(CommandResult {
            success: feedback.error_info.code == 0,
            error_code: feedback.error_info.code,
            error_message: feedback.error_info.desc,
            data: Some(feedback.data),
        }),
        Err(e) => Err(e.to_string()),
    }
}
```

### 数据模型转换

```rust
// FFI数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_id: String,
    pub device_name: String,
    pub model: String,
    pub type_id: Option<String>,
    pub online: bool,
    pub family_id: String,
    pub connect_state: String,
    pub attributes: Vec<DeviceAttribute>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceAttribute {
    pub name: String,
    pub value: String,
    pub data_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandResult {
    pub success: bool,
    pub error_code: i32,
    pub error_message: String,
    pub data: Option<String>,
}

// 转换函数
fn convert_to_ffi_device_info(device: &Arc<dyn UpDevice>) -> DeviceInfo {
    let device_info = device.get_info();
    
    DeviceInfo {
        device_id: device.device_id(),
        device_name: device_info.get_base_info().device_name(),
        model: device.model(),
        type_id: device.type_id(),
        online: device_info.get_basic().online(),
        family_id: device.family_id(),
        connect_state: format!("{:?}", device.get_connect_state()),
        attributes: device.get_attribute_list()
            .into_iter()
            .map(|attr| DeviceAttribute {
                name: attr.name,
                value: attr.value,
                data_type: attr.data_type,
            })
            .collect(),
    }
}
```

## 错误处理

### 统一错误类型

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FFIError {
    pub code: i32,
    pub message: String,
    pub details: Option<String>,
}

impl From<DeviceError> for FFIError {
    fn from(error: DeviceError) -> Self {
        match error {
            DeviceError::DeviceNotFound(msg) => FFIError {
                code: 1001,
                message: "Device not found".to_string(),
                details: Some(msg),
            },
            DeviceError::NetworkError(msg) => FFIError {
                code: 2001,
                message: "Network error".to_string(),
                details: Some(msg),
            },
            // ... 其他错误类型
            _ => FFIError {
                code: 9999,
                message: "Unknown error".to_string(),
                details: Some(error.to_string()),
            },
        }
    }
}
```

## 构建脚本

### build.rs

```rust
fn main() {
    // 根据目标平台生成不同的绑定
    let target = std::env::var("TARGET").unwrap();
    
    if target.contains("android") {
        println!("cargo:rustc-cfg=feature=\"android\"");
        generate_android_bindings();
    } else if target.contains("ios") {
        println!("cargo:rustc-cfg=feature=\"ios\"");
        generate_ios_bindings();
    } else if target.contains("ohos") {
        println!("cargo:rustc-cfg=feature=\"ohos\"");
        generate_ohos_bindings();
    }
    
    // 生成FlatBuffers代码
    generate_flatbuffers_code();
}

fn generate_flatbuffers_code() {
    let out_dir = std::env::var("OUT_DIR").unwrap();
    let schema_path = "updevice.fbs";
    
    std::process::Command::new("flatc")
        .args(&["--rust", "-o", &out_dir, schema_path])
        .status()
        .expect("Failed to generate FlatBuffers code");
}
```

## 最佳实践

### 1. 内存安全

```rust
// 确保跨FFI边界的内存安全
#[cfg(feature = "ios")]
pub fn safe_string_to_c_char(s: String) -> *mut c_char {
    match CString::new(s) {
        Ok(c_string) => c_string.into_raw(),
        Err(_) => std::ptr::null_mut(),
    }
}

#[cfg(feature = "ios")]
pub unsafe fn safe_c_char_to_string(ptr: *const c_char) -> Option<String> {
    if ptr.is_null() {
        return None;
    }
    
    CStr::from_ptr(ptr).to_str().ok().map(|s| s.to_string())
}
```

### 2. 异步处理

```rust
// 跨平台异步接口
#[cfg(feature = "ohos")]
#[napi]
pub async fn async_operation(param: String) -> Result<String> {
    // HarmonyOS支持原生async/await
    let result = perform_async_operation(param).await;
    Ok(result)
}

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn async_operation_android(
    param: *const c_char,
    callback: extern "C" fn(*const c_char),
) {
    // Android使用回调处理异步
    let param_str = unsafe { CStr::from_ptr(param).to_str().unwrap().to_string() };
    
    tokio::spawn(async move {
        let result = perform_async_operation(param_str).await;
        let c_result = CString::new(result).unwrap();
        callback(c_result.as_ptr());
    });
}
```

### 3. 版本兼容

```rust
// API版本管理
pub const API_VERSION: &str = "1.0.0";

#[cfg(any(feature = "android", feature = "ios", feature = "ohos"))]
pub fn get_api_version() -> String {
    API_VERSION.to_string()
}

// 向后兼容的接口
#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn get_device_list_v1(family_id: *const c_char) -> *mut c_char {
    // 旧版本接口实现
    get_device_list_v2(family_id, std::ptr::null())
}

#[cfg(feature = "android")]
#[no_mangle]
pub extern "C" fn get_device_list_v2(
    family_id: *const c_char,
    filter: *const c_char,
) -> *mut c_char {
    // 新版本接口实现
    // ...
}
```
