# Utils模块

Utils模块位于`src/utils`目录下，提供了UpDevice库中使用的各种工具函数和辅助组件，为其他模块提供通用的功能支持。

## 模块结构

```
src/utils/
├── convert_vec.rs           # 向量转换工具
├── fn_clone.rs             # 函数克隆工具
└── mod.rs
```

## 核心工具

### 1. FnClone (函数克隆工具)

`FnClone`是一个重要的工具类，用于在需要`Clone` trait的场景中包装函数和闭包。

#### 设计背景

在Rust中，函数和闭包默认不实现`Clone` trait，但在事件系统、回调机制等场景中，我们经常需要克隆函数对象。`FnClone`解决了这个问题。

#### 实现原理

```rust
use std::sync::Arc;

/// 可克隆的函数包装器
pub struct FnClone<T> {
    inner: Arc<dyn Fn(T) + Send + Sync>,
}

impl<T> FnClone<T> {
    /// 创建新的FnClone实例
    pub fn new<F>(f: F) -> Self
    where
        F: Fn(T) + Send + Sync + 'static,
    {
        Self {
            inner: Arc::new(f),
        }
    }
    
    /// 调用包装的函数
    pub fn call(&self, arg: T) {
        (self.inner)(arg);
    }
}

impl<T> Clone for FnClone<T> {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone(),
        }
    }
}

impl<T> std::fmt::Debug for FnClone<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("FnClone")
            .field("inner", &"<function>")
            .finish()
    }
}
```

#### 使用场景

1. **事件监听器**：
```rust
use crate::utils::fn_clone::FnClone;
use crate::api::event::UpDeviceEvent;

// 创建可克隆的事件监听器
let listener = FnClone::new(|event: UpDeviceEvent| {
    match event {
        UpDeviceEvent::DeviceListChanged(devices) => {
            println!("设备列表更新: {} 个设备", devices.len());
        }
        _ => {}
    }
});

// 可以克隆监听器用于多个订阅
let listener_clone = listener.clone();
event_bus.subscribe("channel1", listener);
event_bus.subscribe("channel2", listener_clone);
```

2. **回调函数**：
```rust
// 异步操作的回调
let callback = FnClone::new(|result: Result<String, String>| {
    match result {
        Ok(data) => println!("操作成功: {}", data),
        Err(error) => println!("操作失败: {}", error),
    }
});

// 传递给异步函数
async_operation(callback).await;
```

3. **数据源事件订阅**：
```rust
use crate::data_source::event::DeviceDataSourceEvent;

let data_source_listener = FnClone::new(|event: DeviceDataSourceEvent| {
    match event {
        DeviceDataSourceEvent::DeviceListChanged(devices) => {
            // 处理设备列表变化
            update_device_cache(devices);
        }
    }
});

device_data_source.subscribe_device_list_change(data_source_listener);
```

#### 高级用法

1. **带状态的闭包**：
```rust
use std::sync::{Arc, Mutex};

let counter = Arc::new(Mutex::new(0));
let counter_clone = counter.clone();

let counting_listener = FnClone::new(move |_event| {
    let mut count = counter_clone.lock().unwrap();
    *count += 1;
    println!("事件计数: {}", *count);
});
```

2. **条件执行**：
```rust
let conditional_listener = FnClone::new(|event: UpDeviceEvent| {
    if should_handle_event(&event) {
        handle_event(event);
    }
});

fn should_handle_event(event: &UpDeviceEvent) -> bool {
    // 条件判断逻辑
    true
}
```

### 2. ConvertVec (向量转换工具)

`ConvertVec`提供了向量类型转换的便利方法。

#### 实现

```rust
/// 向量转换工具
pub trait ConvertVec<T> {
    /// 转换向量中的每个元素
    fn convert_vec<U, F>(self, f: F) -> Vec<U>
    where
        F: FnMut(T) -> U;
    
    /// 过滤并转换向量
    fn filter_convert_vec<U, F, P>(self, predicate: P, f: F) -> Vec<U>
    where
        F: FnMut(T) -> U,
        P: FnMut(&T) -> bool;
}

impl<T> ConvertVec<T> for Vec<T> {
    fn convert_vec<U, F>(self, f: F) -> Vec<U>
    where
        F: FnMut(T) -> U,
    {
        self.into_iter().map(f).collect()
    }
    
    fn filter_convert_vec<U, F, P>(self, predicate: P, f: F) -> Vec<U>
    where
        F: FnMut(T) -> U,
        P: FnMut(&T) -> bool,
    {
        self.into_iter()
            .filter(predicate)
            .map(f)
            .collect()
    }
}
```

#### 使用示例

1. **设备信息转换**：
```rust
use crate::utils::convert_vec::ConvertVec;
use crate::models::device_info::UpDeviceInfo;

// 将UserDomain的设备信息转换为UpDevice的设备信息
let userdomain_devices: Vec<rust_userdomain::models::device_info::DeviceInfo> = get_devices();
let updevice_infos: Vec<UpDeviceInfo> = userdomain_devices.convert_vec(|device| {
    UpDeviceInfo::from(device)
});
```

2. **设备过滤和转换**：
```rust
// 过滤在线设备并转换为FFI模型
let online_devices: Vec<FFIDeviceInfo> = all_devices.filter_convert_vec(
    |device| device.get_basic().online(),
    |device| FFIDeviceInfo::from(device)
);
```

3. **批量状态转换**：
```rust
// 将设备状态转换为字符串
let device_states: Vec<String> = devices.convert_vec(|device| {
    format!("{:?}", device.get_connect_state())
});
```

## 扩展工具

### 1. 结果处理工具

```rust
/// 结果处理工具
pub trait ResultExt<T, E> {
    /// 记录错误并返回默认值
    fn log_error_or_default(self, default: T) -> T
    where
        T: Default,
        E: std::fmt::Display;
    
    /// 记录错误并执行回调
    fn log_error_or_else<F>(self, f: F) -> T
    where
        F: FnOnce(E) -> T,
        E: std::fmt::Display;
}

impl<T, E> ResultExt<T, E> for Result<T, E> {
    fn log_error_or_default(self, default: T) -> T
    where
        T: Default,
        E: std::fmt::Display,
    {
        match self {
            Ok(value) => value,
            Err(error) => {
                log::error!("操作失败: {}", error);
                default
            }
        }
    }
    
    fn log_error_or_else<F>(self, f: F) -> T
    where
        F: FnOnce(E) -> T,
        E: std::fmt::Display,
    {
        match self {
            Ok(value) => value,
            Err(error) => {
                log::error!("操作失败: {}", error);
                f(error)
            }
        }
    }
}
```

### 2. 字符串处理工具

```rust
/// 字符串处理工具
pub trait StringExt {
    /// 安全截取字符串
    fn safe_substring(&self, start: usize, len: usize) -> &str;
    
    /// 检查是否为空或仅包含空白字符
    fn is_empty_or_whitespace(&self) -> bool;
    
    /// 转换为设备ID格式
    fn to_device_id(&self) -> String;
}

impl StringExt for str {
    fn safe_substring(&self, start: usize, len: usize) -> &str {
        let end = std::cmp::min(start + len, self.len());
        let start = std::cmp::min(start, self.len());
        &self[start..end]
    }
    
    fn is_empty_or_whitespace(&self) -> bool {
        self.trim().is_empty()
    }
    
    fn to_device_id(&self) -> String {
        self.trim()
            .to_lowercase()
            .replace(' ', "_")
            .replace('-', "_")
    }
}
```

### 3. 时间处理工具

```rust
use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// 时间处理工具
pub struct TimeUtils;

impl TimeUtils {
    /// 获取当前时间戳（毫秒）
    pub fn current_timestamp_millis() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }
    
    /// 获取当前时间戳（秒）
    pub fn current_timestamp_secs() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
    }
    
    /// 格式化持续时间
    pub fn format_duration(duration: Duration) -> String {
        let secs = duration.as_secs();
        if secs < 60 {
            format!("{}秒", secs)
        } else if secs < 3600 {
            format!("{}分{}秒", secs / 60, secs % 60)
        } else {
            format!("{}小时{}分", secs / 3600, (secs % 3600) / 60)
        }
    }
    
    /// 检查时间戳是否过期
    pub fn is_expired(timestamp: u64, ttl_secs: u64) -> bool {
        let current = Self::current_timestamp_secs();
        current > timestamp + ttl_secs
    }
}
```

### 4. 集合处理工具

```rust
use std::collections::HashMap;
use std::hash::Hash;

/// 集合处理工具
pub trait CollectionExt<T> {
    /// 按指定大小分块
    fn chunk_by(self, size: usize) -> Vec<Vec<T>>;
    
    /// 去重
    fn dedup_by_key<K, F>(self, f: F) -> Vec<T>
    where
        K: Eq + Hash,
        F: FnMut(&T) -> K;
}

impl<T> CollectionExt<T> for Vec<T> {
    fn chunk_by(self, size: usize) -> Vec<Vec<T>> {
        if size == 0 {
            return vec![self];
        }
        
        self.chunks(size)
            .map(|chunk| chunk.to_vec())
            .collect()
    }
    
    fn dedup_by_key<K, F>(mut self, mut f: F) -> Vec<T>
    where
        K: Eq + Hash,
        F: FnMut(&T) -> K,
    {
        let mut seen = HashMap::new();
        let mut result = Vec::new();
        
        for item in self.drain(..) {
            let key = f(&item);
            if !seen.contains_key(&key) {
                seen.insert(key, ());
                result.push(item);
            }
        }
        
        result
    }
}
```

## 性能优化工具

### 1. 缓存工具

```rust
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};

/// 带过期时间的缓存
pub struct ExpiringCache<K, V> {
    data: Arc<RwLock<HashMap<K, (V, Instant)>>>,
    ttl: Duration,
}

impl<K, V> ExpiringCache<K, V>
where
    K: Eq + Hash + Clone,
    V: Clone,
{
    pub fn new(ttl: Duration) -> Self {
        Self {
            data: Arc::new(RwLock::new(HashMap::new())),
            ttl,
        }
    }
    
    pub fn get(&self, key: &K) -> Option<V> {
        let mut data = self.data.write().unwrap();
        
        if let Some((value, timestamp)) = data.get(key) {
            if timestamp.elapsed() < self.ttl {
                return Some(value.clone());
            } else {
                data.remove(key);
            }
        }
        
        None
    }
    
    pub fn put(&self, key: K, value: V) {
        let mut data = self.data.write().unwrap();
        data.insert(key, (value, Instant::now()));
    }
    
    pub fn cleanup_expired(&self) {
        let mut data = self.data.write().unwrap();
        let now = Instant::now();
        
        data.retain(|_, (_, timestamp)| now.duration_since(*timestamp) < self.ttl);
    }
}
```

### 2. 批处理工具

```rust
use tokio::sync::mpsc;
use tokio::time::{interval, Duration};

/// 批处理器
pub struct BatchProcessor<T> {
    sender: mpsc::UnboundedSender<T>,
}

impl<T> BatchProcessor<T>
where
    T: Send + 'static,
{
    pub fn new<F>(
        batch_size: usize,
        flush_interval: Duration,
        processor: F,
    ) -> Self
    where
        F: Fn(Vec<T>) + Send + 'static,
    {
        let (sender, mut receiver) = mpsc::unbounded_channel();
        
        tokio::spawn(async move {
            let mut batch = Vec::with_capacity(batch_size);
            let mut interval = interval(flush_interval);
            
            loop {
                tokio::select! {
                    item = receiver.recv() => {
                        match item {
                            Some(item) => {
                                batch.push(item);
                                if batch.len() >= batch_size {
                                    processor(std::mem::take(&mut batch));
                                }
                            }
                            None => break,
                        }
                    }
                    _ = interval.tick() => {
                        if !batch.is_empty() {
                            processor(std::mem::take(&mut batch));
                        }
                    }
                }
            }
            
            // 处理剩余项目
            if !batch.is_empty() {
                processor(batch);
            }
        });
        
        Self { sender }
    }
    
    pub fn send(&self, item: T) -> Result<(), mpsc::error::SendError<T>> {
        self.sender.send(item)
    }
}
```

## 测试工具

### 1. Mock工具

```rust
/// Mock数据生成器
pub struct MockDataGenerator;

impl MockDataGenerator {
    /// 生成Mock设备信息
    pub fn mock_device_info(device_id: &str) -> UpDeviceInfo {
        UpDeviceInfoBuilder::default()
            .device_id(device_id.to_string())
            .model("mock_device".to_string())
            .protocol("mock_protocol".to_string())
            .base_info(Self::mock_base_info())
            .basic(Self::mock_basic())
            .relation(Self::mock_relation())
            .build()
            .unwrap()
    }
    
    fn mock_base_info() -> UpDeviceBaseInfo {
        UpDeviceBaseInfoBuilder::default()
            .device_name("Mock Device".to_string())
            .model("mock_model".to_string())
            .manufacturer("Mock Manufacturer".to_string())
            .build()
            .unwrap()
    }
    
    fn mock_basic() -> UpDeviceBasic {
        UpDeviceBasicBuilder::default()
            .online(true)
            .controllable(true)
            .queryable(true)
            .build()
            .unwrap()
    }
    
    fn mock_relation() -> UpDeviceRelation {
        UpDeviceRelationBuilder::default()
            .family_id("mock_family".to_string())
            .build()
            .unwrap()
    }
}
```

## 最佳实践

### 1. 错误处理

```rust
// 使用ResultExt简化错误处理
let device_list = get_device_list()
    .log_error_or_default(Vec::new());

// 或者使用自定义回调
let device_count = get_device_count()
    .log_error_or_else(|_| 0);
```

### 2. 函数克隆

```rust
// 创建可重用的监听器
let logger = FnClone::new(|message: String| {
    log::info!("收到消息: {}", message);
});

// 在多个地方使用
event_bus.subscribe("channel1", logger.clone());
event_bus.subscribe("channel2", logger.clone());
```

### 3. 集合处理

```rust
// 批量处理设备
let device_chunks = devices.chunk_by(10);
for chunk in device_chunks {
    process_device_batch(chunk).await;
}

// 去重设备列表
let unique_devices = devices.dedup_by_key(|device| device.device_id());
```

### 4. 缓存使用

```rust
// 创建设备信息缓存
let device_cache = ExpiringCache::new(Duration::from_secs(300));

// 使用缓存
if let Some(device_info) = device_cache.get(&device_id) {
    return device_info;
}

let device_info = fetch_device_info(&device_id).await?;
device_cache.put(device_id, device_info.clone());
```
