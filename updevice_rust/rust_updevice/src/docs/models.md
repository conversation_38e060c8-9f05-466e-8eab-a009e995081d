# Models模块

Models模块位于`src/models`目录下，定义了UpDevice库中使用的各种数据结构和模型，是整个库的数据基础。

## 模块结构

```
src/models/
├── device_info.rs              # 设备信息模型
├── device_base_info.rs         # 设备基础信息
├── device_basic.rs             # 设备基本信息
├── device_config_state.rs      # 设备配置状态
├── device_connect_state.rs     # 设备连接状态
├── device_online_state.rs      # 设备在线状态
├── device_sleep_state.rs       # 设备睡眠状态
├── device_offline_cause.rs     # 设备离线原因
├── device_product.rs           # 设备产品信息
├── device_relation.rs          # 设备关系信息
├── device_permission.rs        # 设备权限信息
├── device_toolkit.rs           # 设备工具包
└── mod.rs
```

## 核心数据模型

### 1. UpDeviceInfo (设备信息)

`UpDeviceInfo`是设备的核心信息模型，包含设备的所有基本属性：

```rust
#[derive(Debug, Clone, Builder)]
pub struct UpDeviceInfo {
    /// 设备ID
    device_id: String,
    /// 设备基础信息
    base_info: UpDeviceBaseInfo,
    /// 设备基本信息
    basic: UpDeviceBasic,
    /// 设备产品信息
    product: UpDeviceProduct,
    /// 设备关系信息
    relation: UpDeviceRelation,
    /// 设备权限信息
    permission: UpDevicePermission,
    /// 协议类型
    protocol: String,
    /// 设备模型
    model: String,
    /// 设备类型ID
    type_id: Option<String>,
}

impl UpDeviceInfo {
    pub fn device_id(&self) -> String {
        self.device_id.clone()
    }
    
    pub fn protocol(&self) -> String {
        self.protocol.clone()
    }
    
    pub fn model(&self) -> String {
        self.model.clone()
    }
    
    pub fn type_id(&self) -> Option<String> {
        self.type_id.clone()
    }
    
    pub fn get_base_info(&self) -> &UpDeviceBaseInfo {
        &self.base_info
    }
    
    pub fn get_basic(&self) -> &UpDeviceBasic {
        &self.basic
    }
    
    pub fn get_product(&self) -> &UpDeviceProduct {
        &self.product
    }
    
    pub fn get_relation(&self) -> &UpDeviceRelation {
        &self.relation
    }
    
    pub fn get_permission(&self) -> &UpDevicePermission {
        &self.permission
    }
}
```

### 2. UpDeviceBaseInfo (设备基础信息)

```rust
#[derive(Debug, Clone, Builder)]
pub struct UpDeviceBaseInfo {
    /// 设备名称
    device_name: String,
    /// 设备图标
    device_icon: String,
    /// 设备模型
    model: String,
    /// 设备类型ID
    type_id: Option<String>,
    /// 设备版本
    version: String,
    /// 制造商
    manufacturer: String,
    /// 产品编号
    product_no: String,
}

impl UpDeviceBaseInfo {
    pub fn device_name(&self) -> String {
        self.device_name.clone()
    }
    
    pub fn device_icon(&self) -> String {
        self.device_icon.clone()
    }
    
    pub fn model(&self) -> String {
        self.model.clone()
    }
    
    pub fn type_id(&self) -> &Option<String> {
        &self.type_id
    }
    
    pub fn version(&self) -> String {
        self.version.clone()
    }
    
    pub fn manufacturer(&self) -> String {
        self.manufacturer.clone()
    }
    
    pub fn product_no(&self) -> String {
        self.product_no.clone()
    }
}
```

### 3. UpDeviceBasic (设备基本信息)

```rust
#[derive(Debug, Clone, Builder)]
pub struct UpDeviceBasic {
    /// 是否在线
    online: bool,
    /// 设备网络类型
    device_net_type: Option<String>,
    /// 设备聚合类型
    device_aggregate_type: String,
    /// 是否支持控制
    controllable: bool,
    /// 是否支持状态查询
    queryable: bool,
    /// 最后更新时间
    last_update_time: i64,
}

impl UpDeviceBasic {
    pub fn online(&self) -> bool {
        self.online
    }
    
    pub fn device_net_type(&self) -> &Option<String> {
        &self.device_net_type
    }
    
    pub fn device_aggregate_type(&self) -> String {
        self.device_aggregate_type.clone()
    }
    
    pub fn controllable(&self) -> bool {
        self.controllable
    }
    
    pub fn queryable(&self) -> bool {
        self.queryable
    }
    
    pub fn last_update_time(&self) -> i64 {
        self.last_update_time
    }
}
```

## 设备状态模型

### 1. 连接状态

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UpDeviceConnectState {
    /// 未连接
    Disconnected,
    /// 连接中
    Connecting,
    /// 已连接
    Connected,
    /// 连接失败
    ConnectFailed,
}

impl fmt::Display for UpDeviceConnectState {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            UpDeviceConnectState::Disconnected => write!(f, "Disconnected"),
            UpDeviceConnectState::Connecting => write!(f, "Connecting"),
            UpDeviceConnectState::Connected => write!(f, "Connected"),
            UpDeviceConnectState::ConnectFailed => write!(f, "ConnectFailed"),
        }
    }
}
```

### 2. 在线状态

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UpDeviceOnlineState {
    /// 离线
    Offline,
    /// 在线
    Online,
    /// 未知
    Unknown,
}

impl From<bool> for UpDeviceOnlineState {
    fn from(online: bool) -> Self {
        if online {
            UpDeviceOnlineState::Online
        } else {
            UpDeviceOnlineState::Offline
        }
    }
}
```

### 3. 睡眠状态

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UpDeviceSleepState {
    /// 清醒
    Awake,
    /// 睡眠
    Sleep,
    /// 深度睡眠
    DeepSleep,
}
```

### 4. 配置状态

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UpDeviceConfigState {
    /// 未知
    Unknown,
    /// 原生设备
    NativeDevice,
    /// 逻辑引擎设备
    LogicEngineDevice,
    /// 兼容设备
    CompatDevice,
}
```

### 5. 离线原因

```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum UpDeviceOfflineCause {
    /// 正常
    Normal,
    /// 网络断开
    NetworkDisconnected,
    /// 设备故障
    DeviceFailure,
    /// 电量不足
    LowBattery,
    /// 用户主动断开
    UserDisconnected,
    /// 超时
    Timeout,
}
```

## 设备关系模型

### 1. UpDeviceRelation (设备关系)

```rust
#[derive(Debug, Clone, Builder)]
pub struct UpDeviceRelation {
    /// 家庭ID
    family_id: String,
    /// 房间ID
    room_id: Option<String>,
    /// 父设备ID
    parent_device_id: Option<String>,
    /// 子设备列表
    sub_device_ids: Vec<String>,
    /// 设备组ID
    group_id: Option<String>,
}

impl UpDeviceRelation {
    pub fn family_id(&self) -> String {
        self.family_id.clone()
    }
    
    pub fn room_id(&self) -> &Option<String> {
        &self.room_id
    }
    
    pub fn parent_device_id(&self) -> &Option<String> {
        &self.parent_device_id
    }
    
    pub fn sub_device_ids(&self) -> &Vec<String> {
        &self.sub_device_ids
    }
    
    pub fn group_id(&self) -> &Option<String> {
        &self.group_id
    }
    
    pub fn is_sub_device(&self) -> bool {
        self.parent_device_id.is_some()
    }
    
    pub fn has_sub_devices(&self) -> bool {
        !self.sub_device_ids.is_empty()
    }
}
```

### 2. UpDevicePermission (设备权限)

```rust
#[derive(Debug, Clone, Builder)]
pub struct UpDevicePermission {
    /// 是否可控制
    controllable: bool,
    /// 是否可查询
    queryable: bool,
    /// 是否可分享
    shareable: bool,
    /// 是否可删除
    deletable: bool,
    /// 权限级别
    permission_level: PermissionLevel,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PermissionLevel {
    /// 所有者
    Owner,
    /// 管理员
    Admin,
    /// 普通用户
    User,
    /// 访客
    Guest,
}
```

## 设备产品模型

### UpDeviceProduct (设备产品信息)

```rust
#[derive(Debug, Clone, Builder)]
pub struct UpDeviceProduct {
    /// 产品ID
    product_id: String,
    /// 产品名称
    product_name: String,
    /// 产品类别
    category: String,
    /// 品牌
    brand: String,
    /// 产品图片
    product_image: String,
    /// 产品描述
    description: String,
}
```

## 设备工具包模型

### UpDeviceToolkit (设备工具包)

```rust
pub trait UpDeviceToolkit: Send + Sync {
    /// 启动SDK
    fn start_sdk(
        &self,
        area: Area,
        app_info: AppInfo,
        client_id: String,
        enable_http_dns: bool,
    ) -> Result<()>;
    
    /// 获取设备信息
    fn get_device_info(&self, device_id: &str) -> Result<DeviceInfo>;
    
    /// 附加设备
    fn attach_device(&self, device_id: &str) -> Result<()>;
    
    /// 分离设备
    fn detach_device(&self, device_id: &str) -> Result<()>;
    
    /// 执行设备命令
    async fn execute_command(
        &self,
        device_id: &str,
        command: UpDeviceCommand,
        timeout: u32,
        route: DeviceCtrlRoute,
    ) -> Result<DeviceFeedback>;
}

pub struct DeviceToolkit {
    // 内部实现
}

impl UpDeviceToolkit for DeviceToolkit {
    // 具体实现
}
```

## 数据转换

### 1. 从UserDomain模型转换

```rust
impl From<rust_userdomain::models::device_info::DeviceInfo> for UpDeviceInfo {
    fn from(device_info: rust_userdomain::models::device_info::DeviceInfo) -> Self {
        UpDeviceInfoBuilder::default()
            .device_id(device_info.device_id.unwrap_or_default())
            .protocol(device_info.protocol.unwrap_or_default())
            .model(device_info.model.unwrap_or_default())
            .type_id(device_info.type_id)
            .base_info(UpDeviceBaseInfo::from(device_info.base_info))
            .basic(UpDeviceBasic::from(device_info.basic))
            .product(UpDeviceProduct::from(device_info.product))
            .relation(UpDeviceRelation::from(device_info.relation))
            .permission(UpDevicePermission::from(device_info.permission))
            .build()
            .unwrap_or_default()
    }
}
```

### 2. 到FFI模型转换

```rust
impl From<UpDeviceInfo> for crate::features::ffi_models::DeviceInfo {
    fn from(device_info: UpDeviceInfo) -> Self {
        crate::features::ffi_models::DeviceInfo {
            device_id: device_info.device_id(),
            device_name: device_info.get_base_info().device_name(),
            model: device_info.model(),
            type_id: device_info.type_id(),
            online: device_info.get_basic().online(),
            family_id: device_info.get_relation().family_id(),
            // ... 其他字段转换
        }
    }
}
```

## 验证和约束

### 1. 数据验证

```rust
impl UpDeviceInfo {
    pub fn validate(&self) -> Result<(), ValidationError> {
        if self.device_id.is_empty() {
            return Err(ValidationError::EmptyDeviceId);
        }
        
        if self.model.is_empty() {
            return Err(ValidationError::EmptyModel);
        }
        
        self.base_info.validate()?;
        self.relation.validate()?;
        
        Ok(())
    }
}

impl UpDeviceRelation {
    pub fn validate(&self) -> Result<(), ValidationError> {
        if self.family_id.is_empty() {
            return Err(ValidationError::EmptyFamilyId);
        }
        
        // 检查父子设备关系的一致性
        if let Some(parent_id) = &self.parent_device_id {
            if parent_id == &self.family_id {
                return Err(ValidationError::InvalidParentDevice);
            }
        }
        
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ValidationError {
    #[error("Device ID cannot be empty")]
    EmptyDeviceId,
    
    #[error("Model cannot be empty")]
    EmptyModel,
    
    #[error("Family ID cannot be empty")]
    EmptyFamilyId,
    
    #[error("Invalid parent device")]
    InvalidParentDevice,
}
```

### 2. 默认值

```rust
impl Default for UpDeviceInfo {
    fn default() -> Self {
        UpDeviceInfoBuilder::default()
            .device_id(String::new())
            .protocol("unknown".to_string())
            .model("unknown".to_string())
            .type_id(None)
            .base_info(UpDeviceBaseInfo::default())
            .basic(UpDeviceBasic::default())
            .product(UpDeviceProduct::default())
            .relation(UpDeviceRelation::default())
            .permission(UpDevicePermission::default())
            .build()
            .unwrap()
    }
}

impl Default for UpDeviceBasic {
    fn default() -> Self {
        UpDeviceBasicBuilder::default()
            .online(false)
            .device_net_type(None)
            .device_aggregate_type(String::new())
            .controllable(true)
            .queryable(true)
            .last_update_time(0)
            .build()
            .unwrap()
    }
}
```

## 序列化支持

### 1. JSON序列化

```rust
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize, Builder)]
pub struct UpDeviceInfo {
    // 字段定义...
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpDeviceConnectState {
    #[serde(rename = "disconnected")]
    Disconnected,
    #[serde(rename = "connecting")]
    Connecting,
    #[serde(rename = "connected")]
    Connected,
    #[serde(rename = "connect_failed")]
    ConnectFailed,
}
```

### 2. 自定义序列化

```rust
impl Serialize for UpDeviceOnlineState {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        match self {
            UpDeviceOnlineState::Online => serializer.serialize_bool(true),
            UpDeviceOnlineState::Offline => serializer.serialize_bool(false),
            UpDeviceOnlineState::Unknown => serializer.serialize_str("unknown"),
        }
    }
}

impl<'de> Deserialize<'de> for UpDeviceOnlineState {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        // 自定义反序列化逻辑
        // 支持bool和string两种格式
    }
}
```

## 最佳实践

### 1. 使用Builder模式

```rust
// 推荐使用Builder创建复杂对象
let device_info = UpDeviceInfoBuilder::default()
    .device_id("device_123".to_string())
    .model("smart_light".to_string())
    .base_info(base_info)
    .basic(basic)
    .relation(relation)
    .build()?;
```

### 2. 状态转换

```rust
impl UpDeviceConnectState {
    pub fn can_transition_to(&self, target: UpDeviceConnectState) -> bool {
        match (self, target) {
            (UpDeviceConnectState::Disconnected, UpDeviceConnectState::Connecting) => true,
            (UpDeviceConnectState::Connecting, UpDeviceConnectState::Connected) => true,
            (UpDeviceConnectState::Connecting, UpDeviceConnectState::ConnectFailed) => true,
            (UpDeviceConnectState::Connected, UpDeviceConnectState::Disconnected) => true,
            _ => false,
        }
    }
}
```

### 3. 内存优化

```rust
// 使用Arc共享不可变数据
pub type SharedDeviceInfo = Arc<UpDeviceInfo>;

// 使用Cow避免不必要的克隆
use std::borrow::Cow;

pub fn get_device_name(&self) -> Cow<str> {
    if self.base_info.device_name.is_empty() {
        Cow::Borrowed("Unknown Device")
    } else {
        Cow::Borrowed(&self.base_info.device_name)
    }
}
```
