# DataSource模块

DataSource模块位于`src/data_source`目录下，负责数据的获取、缓存和事件通知，是连接外部数据源和内部业务逻辑的桥梁。

## 模块结构

```
src/data_source/
├── device_list_data_source.rs    # 设备列表数据源
├── user_data_source.rs           # 用户数据源
├── config_data_source.rs         # 配置数据源
├── event.rs                      # 数据源事件
└── mod.rs
```

## 核心组件

### 1. UpDeviceDataSource (设备数据源)

`UpDeviceDataSource`负责设备列表的获取、缓存管理和变化通知。

#### 接口定义

```rust
#[mry::mry]
#[async_trait]
pub trait UpDeviceDataSource: Any + Send + Sync {
    /// 获取当前家庭id
    fn get_current_family_id(&self) -> Option<String>;
    
    /// 获取缓存设备列表
    fn get_cache_device_list(&self) -> Vec<UpDeviceInfo>;
    
    /// 获取远程设备列表
    async fn get_remote_device_list(&self) -> Result<Vec<UpDeviceInfo>>;
    
    /// 订阅设备列表变化
    fn subscribe_device_list_change(&self, listener: FnClone<DeviceDataSourceEvent>);
}
```

#### 实现类

```rust
pub struct UpDeviceDataSourceImpl {
    event_bus: OnceCell<EventBus<DeviceDataSourceEvent>>,
}

impl UpDeviceDataSourceImpl {
    pub fn new() -> Self {
        Self {
            event_bus: OnceCell::new(),
        }
    }
    
    fn get_event_bus(&self) -> &EventBus<DeviceDataSourceEvent> {
        self.event_bus.get_or_init(|| EventBus::new())
    }
}
```

#### 数据获取流程

```mermaid
sequenceDiagram
    participant DM as DeviceManager
    participant DS as DeviceDataSource
    participant UD as UserDomain
    participant Cache as 缓存
    participant Remote as 远程服务

    DM->>DS: get_cache_device_list()
    DS->>UD: get_device_list_from_cache()
    UD->>Cache: 读取缓存
    Cache-->>UD: 设备列表
    UD-->>DS: 设备信息列表
    DS-->>DM: Vec<UpDeviceInfo>

    Note over DM: 获取远程数据
    DM->>DS: get_remote_device_list()
    DS->>UD: get_device_list_from_remote()
    UD->>Remote: API请求
    Remote-->>UD: 设备数据
    UD->>Cache: 更新缓存
    UD-->>DS: 设备信息列表
    DS-->>DM: Vec<UpDeviceInfo>
```

#### 具体实现

```rust
#[async_trait]
impl UpDeviceDataSource for UpDeviceDataSourceImpl {
    fn get_current_family_id(&self) -> Option<String> {
        if let Some(current_family) = UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_current_family()
        {
            return Some(current_family.family_info.family_id);
        }
        None
    }

    fn get_cache_device_list(&self) -> Vec<UpDeviceInfo> {
        let device_list = UserDomainManager::get_instance()
            .get_user_domain()
            .get_device()
            .get_device_list_from_cache();
        
        device_list.into_iter()
            .map(|device_info| UpDeviceInfo::from(device_info))
            .collect()
    }

    async fn get_remote_device_list(&self) -> Result<Vec<UpDeviceInfo>> {
        let device_list = UserDomainManager::get_instance()
            .get_user_domain()
            .get_device()
            .get_device_list_from_remote()
            .await
            .map_err(|e| DeviceError::NetworkError(e.to_string()))?;
        
        Ok(device_list.into_iter()
            .map(|device_info| UpDeviceInfo::from(device_info))
            .collect())
    }

    fn subscribe_device_list_change(&self, listener: FnClone<DeviceDataSourceEvent>) {
        // 订阅UserDomain的设备列表变化事件
        UserDomainManager::get_instance()
            .get_user_domain()
            .get_device()
            .subscribe_device_list_change(FnClone::new(move |_| {
                // 转换事件并通知监听器
                listener.call(DeviceDataSourceEvent::DeviceListChanged(vec![]));
            }));
    }
}
```

### 2. UpUserDataSource (用户数据源)

`UpUserDataSource`负责用户认证信息的管理和状态变化通知。

#### 接口定义

```rust
#[async_trait]
pub trait UpUserDataSource: Send + Sync {
    /// 获取用户域状态
    fn get_user_domain_state(&self) -> UserDomainState;
    
    /// 获取OAuth数据
    fn get_user_domain_oauth_data(&self) -> AuthData;
    
    /// 订阅用户信息变化
    fn subscribe_user_info_change(&self, listener: FnClone<UserDataSourceEvent>);
}
```

#### 用户状态管理

```mermaid
stateDiagram-v2
    [*] --> NotLogin
    NotLogin --> Login: 登录成功
    Login --> TokenUpdate: 令牌刷新
    TokenUpdate --> Login: 刷新完成
    Login --> NotLogin: 登出
    NotLogin --> [*]
```

#### 实现类

```rust
pub struct UpUserDataSourceImpl;

impl UpUserDataSourceImpl {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl UpUserDataSource for UpUserDataSourceImpl {
    fn get_user_domain_state(&self) -> UserDomainState {
        UserDomainManager::get_instance()
            .get_user_domain()
            .get_state()
    }

    fn get_user_domain_oauth_data(&self) -> AuthData {
        UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_oauth_data()
    }

    fn subscribe_user_info_change(&self, listener: FnClone<UserDataSourceEvent>) {
        // 订阅登录事件
        UserDomainManager::get_instance()
            .get_user_domain()
            .subscribe_user_domain_event(FnClone::new(move |event| {
                match event {
                    UserDomainEvent::Login => {
                        let oauth_data = UserDomainManager::get_instance()
                            .get_user_domain()
                            .get_user()
                            .get_oauth_data();
                        listener.call(UserDataSourceEvent::Login(
                            oauth_data.uhome_user_id,
                            oauth_data.uhome_access_token,
                        ));
                    }
                    UserDomainEvent::Logout => {
                        listener.call(UserDataSourceEvent::Logout);
                    }
                    UserDomainEvent::TokenRefresh => {
                        let oauth_data = UserDomainManager::get_instance()
                            .get_user_domain()
                            .get_user()
                            .get_oauth_data();
                        listener.call(UserDataSourceEvent::TokenUpdate(
                            oauth_data.uhome_access_token,
                        ));
                    }
                }
            }));
    }
}
```

### 3. ConfigDataSource (配置数据源)

`ConfigDataSource`负责设备配置信息的获取和管理。

#### 接口定义

```rust
#[async_trait]
pub trait ConfigDataSource: Send + Sync {
    /// 获取设备配置
    async fn get_device_config(&self, device_id: &str) -> Result<DeviceConfig>;
    
    /// 获取设备模型配置
    async fn get_device_model_config(&self, model: &str) -> Result<ModelConfig>;
    
    /// 缓存设备配置
    fn cache_device_config(&self, device_id: &str, config: DeviceConfig);
}
```

#### 实现类

```rust
pub struct ConfigDataSourceImpl {
    config_cache: DashMap<String, DeviceConfig>,
    model_cache: DashMap<String, ModelConfig>,
}

impl ConfigDataSourceImpl {
    pub fn new() -> Self {
        Self {
            config_cache: DashMap::new(),
            model_cache: DashMap::new(),
        }
    }
}

#[async_trait]
impl ConfigDataSource for ConfigDataSourceImpl {
    async fn get_device_config(&self, device_id: &str) -> Result<DeviceConfig> {
        // 1. 先检查缓存
        if let Some(config) = self.config_cache.get(device_id) {
            return Ok(config.clone());
        }
        
        // 2. 从LogicEngine数据源获取
        let config_data_source = logic_engine::data_source::config_data_source::get_instance();
        let device_config = config_data_source
            .get_device_config(device_id)
            .await
            .map_err(|e| DeviceError::NetworkError(e.to_string()))?;
        
        // 3. 缓存配置
        self.config_cache.insert(device_id.to_string(), device_config.clone());
        
        Ok(device_config)
    }

    async fn get_device_model_config(&self, model: &str) -> Result<ModelConfig> {
        // 类似设备配置的获取逻辑
        if let Some(config) = self.model_cache.get(model) {
            return Ok(config.clone());
        }
        
        // 从远程获取模型配置
        let model_config = self.fetch_model_config_from_remote(model).await?;
        self.model_cache.insert(model.to_string(), model_config.clone());
        
        Ok(model_config)
    }

    fn cache_device_config(&self, device_id: &str, config: DeviceConfig) {
        self.config_cache.insert(device_id.to_string(), config);
    }
}
```

### 4. 数据源事件

#### 事件类型定义

```rust
#[derive(Debug, Clone)]
pub enum DeviceDataSourceEvent {
    /// 设备列表变化
    DeviceListChanged(Vec<UpDeviceInfo>),
}

#[derive(Debug, Clone)]
pub enum UserDataSourceEvent {
    /// 用户登录
    Login(String, String), // user_id, access_token
    /// 用户登出
    Logout,
    /// 令牌更新
    TokenUpdate(String), // new_access_token
}
```

#### 事件监听器

```rust
pub type DeviceDataSourceListener = FnClone<DeviceDataSourceEvent>;
pub type UserDataSourceListener = FnClone<UserDataSourceEvent>;
```

#### 事件总线

```rust
pub fn get_event_bus() -> &'static EventBus<DeviceDataSourceEvent> {
    static EVENT_BUS: OnceCell<EventBus<DeviceDataSourceEvent>> = OnceCell::new();
    EVENT_BUS.get_or_init(|| EventBus::new())
}
```

## 数据流转

### 设备列表数据流

```mermaid
graph TD
    A[远程API] --> B[UserDomain]
    B --> C[设备缓存]
    C --> D[DeviceDataSource]
    D --> E[DeviceManager]
    E --> F[设备实例]
    
    G[缓存更新] --> H[事件通知]
    H --> I[DeviceManager更新]
    I --> J[UI刷新]
```

### 用户状态数据流

```mermaid
graph TD
    A[用户登录] --> B[UserDomain]
    B --> C[OAuth数据更新]
    C --> D[UserDataSource]
    D --> E[登录事件]
    E --> F[DeviceManager]
    F --> G[连接远程设备]
    
    H[令牌刷新] --> I[TokenUpdate事件]
    I --> J[更新访问令牌]
    
    K[用户登出] --> L[Logout事件]
    L --> M[清理设备列表]
```

## 缓存策略

### 1. 设备列表缓存

```rust
impl UpDeviceDataSourceImpl {
    fn get_cache_device_list(&self) -> Vec<UpDeviceInfo> {
        // 优先从UserDomain缓存获取
        let device_list = UserDomainManager::get_instance()
            .get_user_domain()
            .get_device()
            .get_device_list_from_cache();
        
        // 转换为UpDeviceInfo格式
        device_list.into_iter()
            .map(|device_info| UpDeviceInfo::from(device_info))
            .collect()
    }
}
```

### 2. 配置缓存

```rust
impl ConfigDataSourceImpl {
    async fn get_device_config(&self, device_id: &str) -> Result<DeviceConfig> {
        // 多级缓存策略
        // 1. 内存缓存
        if let Some(config) = self.config_cache.get(device_id) {
            return Ok(config.clone());
        }
        
        // 2. 持久化缓存（如果有）
        if let Some(config) = self.load_from_persistent_cache(device_id).await? {
            self.config_cache.insert(device_id.to_string(), config.clone());
            return Ok(config);
        }
        
        // 3. 远程获取
        let config = self.fetch_from_remote(device_id).await?;
        self.cache_device_config(device_id, config.clone());
        
        Ok(config)
    }
}
```

## 错误处理

### 1. 网络错误处理

```rust
async fn get_remote_device_list(&self) -> Result<Vec<UpDeviceInfo>> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_device()
        .get_device_list_from_remote()
        .await
    {
        Ok(device_list) => {
            Ok(device_list.into_iter()
                .map(|device_info| UpDeviceInfo::from(device_info))
                .collect())
        }
        Err(e) => {
            error!("Failed to get remote device list: {}", e);
            // 降级到缓存数据
            Ok(self.get_cache_device_list())
        }
    }
}
```

### 2. 数据转换错误

```rust
impl From<rust_userdomain::models::device_info::DeviceInfo> for UpDeviceInfo {
    fn from(device_info: rust_userdomain::models::device_info::DeviceInfo) -> Self {
        // 安全的数据转换，处理可能的字段缺失
        UpDeviceInfo::builder()
            .device_id(device_info.device_id.unwrap_or_default())
            .model(device_info.model.unwrap_or_default())
            .type_id(device_info.type_id)
            .build()
            .unwrap_or_else(|e| {
                warn!("Failed to convert device info: {}", e);
                UpDeviceInfo::default()
            })
    }
}
```

## 测试支持

### Mock数据源

```rust
pub struct MockDeviceDataSource {
    device_list: Vec<UpDeviceInfo>,
    current_family_id: Option<String>,
}

impl MockDeviceDataSource {
    pub fn new() -> Self {
        Self {
            device_list: vec![],
            current_family_id: None,
        }
    }
    
    pub fn set_device_list(&mut self, devices: Vec<UpDeviceInfo>) {
        self.device_list = devices;
    }
}

#[async_trait]
impl UpDeviceDataSource for MockDeviceDataSource {
    fn get_current_family_id(&self) -> Option<String> {
        self.current_family_id.clone()
    }

    fn get_cache_device_list(&self) -> Vec<UpDeviceInfo> {
        self.device_list.clone()
    }

    async fn get_remote_device_list(&self) -> Result<Vec<UpDeviceInfo>> {
        Ok(self.device_list.clone())
    }

    fn subscribe_device_list_change(&self, _listener: FnClone<DeviceDataSourceEvent>) {
        // Mock实现，不做实际订阅
    }
}
```

## 最佳实践

### 1. 数据源初始化

```rust
// 在应用启动时设置数据源
let device_data_source = Box::new(UpDeviceDataSourceImpl::new());
UpDeviceInjection::get_instance().set_device_data_source(device_data_source);

let user_data_source = Box::new(UpUserDataSourceImpl::new());
UpDeviceInjection::get_instance().set_user_data_source(user_data_source);
```

### 2. 事件订阅

```rust
// 订阅数据源事件
let device_data_source = UpDeviceInjection::get_instance().get_device_data_source();
device_data_source.subscribe_device_list_change(FnClone::new(|event| {
    match event {
        DeviceDataSourceEvent::DeviceListChanged(devices) => {
            info!("设备列表更新: {} 个设备", devices.len());
            // 处理设备列表变化
        }
    }
}));
```

### 3. 缓存管理

```rust
// 定期清理过期缓存
impl ConfigDataSourceImpl {
    pub fn cleanup_expired_cache(&self) {
        let now = Instant::now();
        self.config_cache.retain(|_, config| {
            now.duration_since(config.cached_at) < Duration::from_hours(1)
        });
    }
}
```

### 4. 错误恢复

```rust
// 实现数据源的降级策略
async fn get_device_list_with_fallback(&self) -> Vec<UpDeviceInfo> {
    // 1. 尝试获取远程数据
    if let Ok(devices) = self.get_remote_device_list().await {
        return devices;
    }
    
    // 2. 降级到缓存数据
    let cached_devices = self.get_cache_device_list();
    if !cached_devices.is_empty() {
        return cached_devices;
    }
    
    // 3. 返回空列表
    vec![]
}
```
