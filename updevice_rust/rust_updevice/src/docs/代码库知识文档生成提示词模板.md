# 代码库知识文档生成提示词模板

## 基础提示词

```
阅读{代码库名称}代码库，熟悉代码和逻辑，完成后在{文档目录路径}创建docs目录，生成代码库的知识文档，文档应该按照模块组织，每个主要模块一个单独的文档文件（每个文档命名按照模块名命名，内容要详细），并提供一个总体的README文档作为入口，格式为markdown文档，对于重要的逻辑需要有必要的时序图或者流程图，需要有整体架构图。

要求：
1. 所有回复使用中文
2. 文档内容要详细完整，包含代码示例
3. 使用Mermaid图表展示架构和流程
4. 包含最佳实践和使用指南
5. 提供错误处理和性能优化建议
```

## 详细提示词模板

### 1. 完整版提示词

```
你是一名经验丰富的软件架构师和技术文档专家。请阅读{代码库名称}代码库，深入分析代码结构、设计模式和业务逻辑，然后生成完整的知识文档。

## 任务要求

### 基本要求
- 所有回复都使用中文
- 在{文档目录路径}创建docs目录
- 按模块组织文档，每个主要模块一个文档文件
- 使用markdown格式
- 内容要详细完整，适合新人学习和老手参考

### 文档结构
1. **README.md** - 总体入口文档
   - 项目概述和核心特性
   - 整体架构图（使用Mermaid）
   - 快速开始指南
   - 文档导航链接

2. **architecture.md** - 整体架构设计
   - 架构原则和设计理念
   - 分层架构图和组件关系
   - 设计模式应用
   - 并发与异步设计
   - 性能优化策略
   - 安全考虑

3. **{模块名}.md** - 各模块详解
   - 模块概述和职责
   - 核心组件和接口
   - 重要流程的时序图/流程图
   - 代码示例和使用方法
   - 最佳实践
   - 常见问题和解决方案

### 内容要求
- **架构图表**：使用Mermaid绘制清晰的架构图、时序图、状态图
- **代码示例**：提供完整可运行的代码示例
- **设计分析**：深入分析设计模式、架构决策的原因
- **实用指南**：包含快速开始、最佳实践、错误处理
- **技术深度**：涵盖并发控制、内存管理、性能优化
- **扩展性**：说明如何扩展和定制功能

### 分析重点
1. **模块职责**：每个模块的核心功能和边界
2. **数据流转**：数据在模块间的流转过程
3. **生命周期**：对象和资源的创建、使用、销毁
4. **错误处理**：异常情况的处理策略
5. **性能考虑**：性能瓶颈和优化方案
6. **扩展点**：可扩展的接口和机制

请开始分析{代码库名称}代码库并生成文档。
```

### 2. 针对特定类型项目的提示词

#### Rust项目专用
```
阅读{代码库名称} Rust代码库，这是一个{项目类型描述}项目。请深入分析Rust特有的设计模式和最佳实践，生成详细的知识文档。

特别关注：
1. **所有权和借用**：分析内存管理策略
2. **并发安全**：Arc、Mutex、RwLock等的使用
3. **异步编程**：async/await、Future、tokio的应用
4. **错误处理**：Result类型、错误传播、自定义错误
5. **trait系统**：trait定义、实现、泛型约束
6. **宏系统**：过程宏、声明宏的使用
7. **FFI接口**：如果有跨语言调用，详细说明接口设计
8. **测试策略**：单元测试、集成测试、性能测试

文档要求同基础模板，额外包含Rust生态系统的最佳实践。
```

#### 跨平台项目专用
```
阅读{代码库名称}跨平台代码库，分析其跨平台架构设计和实现策略。

重点分析：
1. **平台抽象**：如何抽象平台差异
2. **接口设计**：统一接口的设计原则
3. **数据序列化**：跨平台数据交换格式
4. **构建系统**：多平台构建配置
5. **测试策略**：跨平台测试方案
6. **部署方案**：各平台的部署策略

生成的文档要包含平台适配指南和扩展新平台的方法。
```

#### 库/SDK项目专用
```
阅读{代码库名称}库/SDK代码库，这是一个供其他开发者使用的{功能描述}库。

重点关注：
1. **API设计**：公开接口的设计原则
2. **易用性**：如何降低使用门槛
3. **扩展性**：如何支持功能扩展
4. **向后兼容**：版本升级策略
5. **文档完整性**：API文档、示例代码
6. **错误处理**：用户友好的错误信息
7. **性能考虑**：库的性能影响
8. **集成方式**：如何集成到不同项目

文档要包含详细的使用指南和集成示例。
```

### 3. 快速版提示词

```
快速生成{代码库名称}的知识文档：

1. 创建docs目录结构
2. 生成README.md（包含架构图）
3. 为每个主要模块生成文档
4. 包含代码示例和最佳实践
5. 使用Mermaid图表展示关键流程

要求：中文文档，内容详细，适合团队使用。
```

## 使用指南

### 1. 选择合适的提示词
- **完整版**：适用于大型项目，需要详细文档
- **特定类型**：根据项目技术栈选择
- **快速版**：适用于小型项目或快速生成

### 2. 自定义参数
替换模板中的占位符：
- `{代码库名称}`：实际的项目名称
- `{文档目录路径}`：文档存放路径
- `{项目类型描述}`：项目的具体类型
- `{功能描述}`：项目的主要功能

### 3. 补充信息
根据项目特点，可以在提示词中补充：
- 特殊的技术栈要求
- 特定的文档格式需求
- 团队的文档规范
- 重点关注的技术领域

### 4. 迭代优化
- 根据生成结果调整提示词
- 针对特定模块补充详细要求
- 根据团队反馈优化文档结构

## 示例用法

### 示例1：分析Rust Web框架
```
阅读actix-web代码库，这是一个Rust Web框架项目。请深入分析其异步Web服务架构，生成详细的知识文档。

特别关注：
1. Actor模型的应用
2. 异步请求处理流程
3. 中间件机制设计
4. 路由系统实现
5. 性能优化策略

在src/docs/创建文档，包含完整的架构分析和使用指南。
```

### 示例2：分析移动端SDK
```
阅读flutter_rust_bridge代码库，这是一个Flutter与Rust互操作的SDK项目。

重点分析：
1. FFI接口设计
2. 数据类型映射
3. 异步调用机制
4. 错误处理策略
5. 代码生成工具

生成面向SDK用户的详细文档，包含集成指南和最佳实践。
```

## 注意事项

1. **确保AI有足够的上下文**：提供项目背景信息
2. **明确文档用途**：是给新人入门还是给专家参考
3. **指定技术重点**：根据项目特点强调关键技术
4. **要求具体格式**：明确图表类型、代码示例格式
5. **设置质量标准**：要求详细程度、完整性标准

## 模板扩展

可以根据具体需求扩展模板：
- 添加特定技术栈的分析要求
- 增加行业特定的关注点
- 定制文档格式和结构
- 补充团队规范要求
