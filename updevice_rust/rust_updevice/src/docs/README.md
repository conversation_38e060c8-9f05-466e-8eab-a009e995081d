# UpDevice Rust 代码库知识文档

## 概述

UpDevice Rust是一个跨平台的智能设备管理库，基于Rust语言开发，支持Android、iOS和HarmonyOS三个平台。该库提供了统一的设备管理接口，包括设备发现、连接、控制、状态监控等核心功能。

## 整体架构

```mermaid
graph TB
    subgraph "应用层"
        A[Android App]
        B[iOS App]
        C[HarmonyOS App]
    end

    subgraph "FFI接口层"
        D[Android FFI]
        E[iOS FFI]
        F[HarmonyOS FFI]
    end

    subgraph "API层"
        G[DeviceManager]
        H[DeviceInjection]
        I[DeviceFilter]
        J[Event System]
    end

    subgraph "核心业务层"
        K[Device Core]
        L[Factory Pattern]
        M[Daemon Process]
    end

    subgraph "设备类型层"
        N[EngineDevice]
        O[AggregateDevice]
        P[WashingDevice]
        Q[CommonDevice]
    end

    subgraph "数据源层"
        R[DeviceDataSource]
        S[UserDataSource]
        T[ConfigDataSource]
    end

    subgraph "基础设施层"
        U[Models]
        V[Utils]
        W[Logic Engine]
        X[Task Manager]
    end

    A --> D
    B --> E
    C --> F
    D --> G
    E --> G
    F --> G
    G --> K
    H --> G
    I --> G
    J --> G
    K --> L
    K --> M
    L --> N
    L --> O
    L --> P
    L --> Q
    M --> N
    M --> O
    M --> P
    M --> Q
    G --> R
    G --> S
    G --> T
    K --> U
    K --> V
    N --> W
    K --> X
```

## 核心模块

### 1. [API模块](./api.md)
- **DeviceManager**: 设备管理器，提供设备列表管理、设备获取、事件订阅等功能
- **DeviceInjection**: 依赖注入容器，管理各种组件的生命周期
- **DeviceFilter**: 设备过滤器，支持按家庭ID、父设备ID等条件过滤
- **Event**: 事件系统，处理设备状态变化、列表更新等事件

### 2. [Device模块](./device.md)
- **UpDevice Trait**: 设备抽象接口，定义设备的基本操作
- **DeviceCore**: 设备核心实现，管理设备状态、属性、命令执行
- **设备类型**: EngineDevice、AggregateDevice、WashingDevice等具体设备实现

### 3. [Factory模块](./factory.md)
- **DeviceFactory**: 设备工厂，根据设备信息创建对应的设备实例
- **DeviceCreator**: 设备创建器，支持多工厂链式创建

### 4. [Daemon模块](./daemon.md)
- **DeviceDaemon**: 设备守护进程，管理设备的准备、释放、生命周期
- **DevicePrepare**: 设备准备器，处理设备的初始化和资源准备

### 5. [DataSource模块](./data_source.md)
- **DeviceDataSource**: 设备数据源，提供设备列表获取、缓存管理
- **UserDataSource**: 用户数据源，管理用户登录状态、令牌更新
- **ConfigDataSource**: 配置数据源，提供设备配置信息

### 6. [Features模块](./features.md)
- **跨平台FFI**: 支持Android、iOS、HarmonyOS的原生接口
- **FlatBuffers**: 跨平台数据序列化
- **平台适配**: 各平台特定的实现和优化

### 7. [Models模块](./models.md)
- **设备信息模型**: DeviceInfo、DeviceBaseInfo等
- **设备状态模型**: ConnectState、OnlineState、SleepState等
- **设备关系模型**: DeviceRelation、DevicePermission等

### 8. [Utils模块](./utils.md)
- **工具函数**: 类型转换、函数克隆等辅助功能
- **通用组件**: 跨模块共享的工具类

## 技术特性

### 异步编程
- 基于Tokio异步运行时
- 支持并发设备操作
- 异步事件处理机制

### 内存安全
- Rust语言的内存安全保证
- Arc/Mutex等线程安全机制
- 零拷贝数据传输

### 跨平台支持
- 统一的Rust核心逻辑
- 平台特定的FFI接口
- FlatBuffers跨平台序列化

### 设计模式
- 工厂模式：设备创建
- 观察者模式：事件系统
- 策略模式：设备过滤
- 依赖注入：组件管理

## 依赖关系

```mermaid
graph LR
    A[rust_updevice] --> B[logic_engine]
    A --> C[task_manager]
    A --> D[rust_storage]
    A --> E[rust_userdomain]
    A --> F[rust_resource]
    A --> G[rust_usdk]
    A --> H[flatbuffers]
    A --> I[tokio]
    A --> J[dashmap]
    A --> K[parking_lot]
```

## 快速开始

### 初始化
```rust
use rust_updevice::api::device_injection::UpDeviceInjection;
use rust_usdk::usdk_toolkit::toolkit::{AppInfo, Area};

// 初始化设备管理器
let app_info = AppInfo::new("your_app_id", "your_app_secret");
let area = Area::China;
let client_id = "your_client_id".to_string();

UpDeviceInjection::get_instance().init_device_manager(
    area,
    app_info,
    client_id,
    DeviceEnvironment::DeviceEnvProduction
);
```

### 获取设备列表
```rust
let device_manager = UpDeviceInjection::get_instance().get_device_manager();
let devices = device_manager.get_device_list(None);
```

### 订阅设备变化
```rust
let subscription_id = device_manager.subscribe_device_list_change(
    true,
    |event| {
        match event {
            UpDeviceEvent::DeviceListChanged(devices) => {
                println!("设备列表已更新，设备数量: {}", devices.len());
            }
        }
    }
);
```

## 测试

项目使用Cucumber进行BDD测试，测试文件位于`tests/`目录：

```bash
# 运行所有测试
cargo test

# 运行特定feature测试
cargo test --test cucumber
```

## 构建

支持多平台构建：

```bash
# Android
cargo build --features android

# iOS
cargo build --features ios

# HarmonyOS
cargo build --features ohos
```

## 文档导航

### 架构设计
- [整体架构设计](./architecture.md) - 系统架构、设计模式、性能优化等

### 模块详解
- [API模块详解](./api.md) - 设备管理器、依赖注入、事件系统
- [Device模块详解](./device.md) - 设备抽象接口、设备类型、生命周期
- [Factory模块详解](./factory.md) - 工厂模式、设备创建、扩展机制
- [Daemon模块详解](./daemon.md) - 守护进程、设备准备、状态管理
- [DataSource模块详解](./data_source.md) - 数据源、缓存策略、事件通知
- [Features模块详解](./features.md) - 跨平台FFI、序列化、平台适配
- [Models模块详解](./models.md) - 数据模型、状态定义、类型转换
- [Utils模块详解](./utils.md) - 工具函数、辅助组件、性能优化

### 开发工具
- [代码库知识文档生成提示词模板](./代码库知识文档生成提示词模板.md) - 用于生成其他代码库文档的AI提示词模板
