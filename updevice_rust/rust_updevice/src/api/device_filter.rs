use crate::device::up_device::UpDevice;

pub trait UpDeviceFilter {
    fn accept(&self, device: &dyn UpDevice) -> bool;
}
// 使用家庭ID过滤设备
pub struct DeviceFilterByFamilyId {
    family_id: String,
}

impl DeviceFilterByFamilyId {
    pub fn new(family_id: String) -> Self {
        DeviceFilterByFamilyId { family_id }
    }
}

impl UpDeviceFilter for DeviceFilterByFamilyId {
    fn accept(&self, device: &dyn UpDevice) -> bool {
        // 家庭ID一样 或者是 共享设备
        device.family_id() == self.family_id || device.get_info().get_device_card_is_aggregate_device()
    }
}
// 使用设备范围过滤设备
pub struct DeviceFilterByDeviceIds {
    device_ids: Vec<String>,
}

impl DeviceFilterByDeviceIds {
    pub fn new(device_ids: Vec<String>) -> Self {
        DeviceFilterByDeviceIds { device_ids }
    }
}

impl UpDeviceFilter for DeviceFilterByDeviceIds {
    fn accept(&self, device: &dyn UpDevice) -> bool {
        self.device_ids.contains(&device.device_id())
    }
}
// 使用父设备ID过滤设备
pub struct DeviceFilterByParentId {
    parent_id: String,
}
impl DeviceFilterByParentId {
    pub fn new(parent_id: String) -> Self {
        DeviceFilterByParentId { parent_id }
    }
}
impl UpDeviceFilter for DeviceFilterByParentId {
    fn accept(&self, device: &dyn UpDevice) -> bool {
        match device.get_info().parent_id() { 
            Some(parent_id) => self.parent_id == parent_id,
            None => false
        }
    }
}
