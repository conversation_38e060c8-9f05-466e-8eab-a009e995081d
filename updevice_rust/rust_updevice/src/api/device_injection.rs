use std::ops::DerefMut;
use std::sync::Arc;
use log::debug;
use logic_engine::data_source::config_data_source::ConfigDataSource;

use once_cell::sync::{<PERSON>zy, OnceCell};
use task_manager::event_bus::EventBus;

use crate::api::device_manager::UpDeviceManager;
use crate::api::event::UpDeviceEvent;
use crate::data_source::config_data_source::ConfigDataSourceImpl;
use crate::data_source::device_list_data_source::{UpDeviceDataSource, UpDeviceDataSourceImpl};
use crate::data_source::user_data_source::{UpUserDataSource, UpUserDataSourceImpl};
use crate::factory::device_factory::DeviceFactory;
use rust_usdk::usdk_toolkit::toolkit::{AppInfo, Area};
use crate::models::device_toolkit::{DeviceToolkit, UpDeviceToolkit};

#[derive(Clone)]
pub enum DeviceEnvironment {
    /// 生产
    DeviceEnvProduction = 0,
    /// 验收
    DeviceEnvAcceptance = 1,
    /// 未知
    Unknown = 2,
}

impl From<i32> for DeviceEnvironment {
    fn from(value: i32) -> Self {
        match value {
            0 => DeviceEnvironment::DeviceEnvProduction,
            1 => DeviceEnvironment::DeviceEnvAcceptance,
            _ => DeviceEnvironment::Unknown,
        }
    }
}

pub struct UpDeviceInjection {
    event_bus: OnceCell<EventBus<UpDeviceEvent>>,
    device_manager: OnceCell<UpDeviceManager>,
    device_data_source: OnceCell<Box<dyn UpDeviceDataSource>>,
    device_toolkit: OnceCell<Box<dyn UpDeviceToolkit>>,
    user_data_source: OnceCell<Box<dyn UpUserDataSource>>,
    config_data_source: OnceCell<Arc<dyn ConfigDataSource>>,
    client_id: OnceCell<String>,
}
impl UpDeviceInjection {
    pub fn get_instance() -> &'static mut UpDeviceInjection {
        unsafe {
            static mut INSTANCE: Lazy<UpDeviceInjection> = Lazy::new(UpDeviceInjection::new);
            INSTANCE.deref_mut()
        }
    }
    fn new() -> Self {
        Self {
            device_manager: OnceCell::new(),
            device_data_source: OnceCell::new(),
            device_toolkit: OnceCell::new(),
            user_data_source: OnceCell::new(),
            config_data_source: OnceCell::new(),
            event_bus: OnceCell::new(),
            client_id: OnceCell::new(),
        }
    }
    pub fn init_device_manager(
        &self, 
        area: Area, 
        app_info: AppInfo, 
        client_id: String, 
        environment: DeviceEnvironment
    ) {
        let enable_http_dns = if let DeviceEnvironment::DeviceEnvAcceptance = environment {
            // 验收环境下 设置http_nds 为false
            false
        } else {
            true
        };

        let _ = self
            .get_device_toolkit()
            .start_sdk(area, app_info, client_id.clone(), enable_http_dns);
        self.client_id.set(client_id).unwrap_or(());
        self.event_bus.set(EventBus::new()).unwrap_or(());
        self.device_manager
            .set(UpDeviceManager::new())
            .unwrap_or(());
        self.get_device_manager()
            .append_device_factory(Box::new(DeviceFactory::new()))
    }

    pub fn is_initialized(&self) -> bool { 
        self.device_manager.get().is_some()
    }
    
    pub fn get_device_manager(&self) -> &UpDeviceManager {
        self.device_manager.get().unwrap()
    }
    pub fn get_device_manager_mut(&mut self) -> &mut UpDeviceManager {
        self.device_manager.get_mut().unwrap()
    }
    pub fn get_device_data_source(&self) -> &dyn UpDeviceDataSource {
        self.device_data_source
            .get_or_init(|| Box::new(UpDeviceDataSourceImpl::new()))
            .as_ref()
    }

    pub fn get_device_data_source_mut(&mut self) -> &mut dyn UpDeviceDataSource {
        self.device_data_source.get_mut().unwrap().as_mut()
    }

    pub fn set_device_data_source(&mut self, data_source: Box<dyn UpDeviceDataSource>) {
        let is_test = std::env::var("CARGO_TEST").is_ok();
        if !is_test && self.device_data_source.get().is_some() {
            debug!("device_data_source is already initialized");
            return;
        }
        self.device_data_source.take();
        self.device_data_source.set(data_source).unwrap_or(());
    }

    pub fn get_user_data_source(&self) -> &dyn UpUserDataSource {
        self.user_data_source
            .get_or_init(|| Box::new(UpUserDataSourceImpl::new()))
            .as_ref()
    }

    pub fn set_user_data_source(&self, data_source: Box<dyn UpUserDataSource>) {
        self.user_data_source.set(data_source).unwrap_or(());
    }

    pub fn get_device_toolkit(&self) -> &dyn UpDeviceToolkit {
        self.device_toolkit
            .get_or_init(|| Box::new(DeviceToolkit::new()))
            .as_ref()
    }

    pub fn get_device_toolkit_mut(&mut self) -> &mut dyn UpDeviceToolkit {
        self.device_toolkit.get_mut().unwrap().as_mut()
    }

    pub fn set_device_toolkit(&mut self, toolkit: Box<dyn UpDeviceToolkit>) {
        let is_test = std::env::var("CARGO_TEST").is_ok();
        if !is_test && self.device_toolkit.get().is_some() {
            debug!("device_toolkit is already initialized");
            return;
        }
        self.device_toolkit.take();
        self.device_toolkit.set(toolkit).unwrap_or(());
    }

    pub fn get_config_data_source(&self) -> Arc<dyn ConfigDataSource> {
        self.config_data_source
            .get_or_init(|| Arc::new(ConfigDataSourceImpl::new()))
            .clone()
    }
    pub fn get_config_data_source_mut(&mut self) -> Arc<dyn ConfigDataSource> {
        self.config_data_source.get_mut().unwrap().clone()
    }
    pub fn set_config_data_source(&self, data_source: Arc<dyn ConfigDataSource>) {
        self.config_data_source.set(data_source).unwrap_or(());
    }
    pub fn get_event_bus(&self) -> &EventBus<UpDeviceEvent> {
        self.event_bus.get().unwrap()
    }
    pub fn get_client_id(&self) -> &str {
        self.client_id.get().unwrap()
    }
}
