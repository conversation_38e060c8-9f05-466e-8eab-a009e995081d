use std::sync::Arc;
use crate::daemon::device_attach::DefaultDeviceAttach;
use crate::daemon::device_prepare::DevicePrepare;
use crate::daemon::extend_api_prepare::DefaultDevicePrepare;
use crate::device::device_core::DeviceCore;
use crate::device::empty_device_extend::EmptyDeviceExtend;
use crate::device::extend_api::ExtendApi;
use crate::device::up_device::{UpDevice, UpDeviceCore};
use crate::models::device_info::UpDeviceInfo;

pub struct UpAggregateDevice {
    device_core: Arc<DeviceCore>,
    extend_api: Arc<dyn ExtendApi>,
}

impl UpAggregateDevice {
    pub fn new(unique_id: String, device_info: UpDeviceInfo) -> Self {
        let device_prepare = DevicePrepare::new(
            device_info.device_id(),
            Box::new(DefaultDeviceAttach::new()),
            Box::new(DefaultDevicePrepare::new()),
        );
        UpAggregateDevice {
            device_core: Arc::new(DeviceCore::new(unique_id, device_info, device_prepare)),
            extend_api: Arc::new(EmptyDeviceExtend {}),
        }
    }
}

impl UpDevice for UpAggregateDevice {
    fn get_device_core(&self) -> Arc<dyn UpDeviceCore> {
        self.device_core.clone()
    }

    fn get_extend_api(&self) -> Arc<dyn ExtendApi> {
        self.extend_api.clone()
    }
}
