use crate::device::aggregate_device::UpAggregateDevice;
use crate::device::compat::voice_box::voice_box_dot::VoiceBoxDot;
use crate::device::compat::voice_box::voice_box_dot2::VoiceBoxDot2;
use crate::device::engine_device::UpEngineDevice;
use crate::device::not_net_device::UpNotNetDevice;
use crate::device::up_device::UpDevice;
use crate::device::washing_device::UpWashingDevice;
use crate::factory::utils::type_ids::{TypeIds, VoiceBoxTypeIds};
use crate::models::device_info::UpDeviceInfo;

use super::utils::type_ids::WashingMachineTypeIds;

#[mry::mry]
pub trait UpDeviceFactory: Send + Sync {
    fn create_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>>;
}

pub struct DeviceFactory;

impl DeviceFactory {
    const NON_NET_DEVICE: &'static str = "nonNetDevice";

    pub fn new() -> Self {
        DeviceFactory {}
    }

    fn create_voice_box(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        let mut device = Option::<Box<dyn UpDevice>>::None;

        if TypeIds::is_voice_box_dot(device_info.type_id()) {
            device = Some(Box::new(VoiceBoxDot::new(device_id, device_info.clone())));
        } else if TypeIds::is_voice_box_dot2(device_info.type_id())
            || TypeIds::is_voice_box_dot3(device_info.type_id())
        {
            device = Some(Box::new(VoiceBoxDot2::new(device_id, device_info.clone())));
        }

        device
    }

    fn create_washing_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        let mut device = Option::<Box<dyn UpDevice>>::None;

        if device_info.type_id().is_some()
            && TypeIds::is_washing_machine(&device_info.type_id().unwrap())
        {
            device = Some(Box::new(UpWashingDevice::new(
                device_id.to_string(),
                device_info.clone(),
            )));
        }

        device
    }

    fn create_aggregate_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        let mut device = Option::<Box<dyn UpDevice>>::None;
        if !device_info
            .get_basic()
            .device_aggregate_type().is_empty()
        {
            device = Some(Box::new(UpAggregateDevice::new(
                device_id.to_string(),
                device_info.clone(),
            )));
        }
        device
    }

    fn create_not_net_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        let mut device = Option::<Box<dyn UpDevice>>::None;

        if device_info
            .get_basic()
            .device_net_type()
            .unwrap_or("".to_string())
            == Self::NON_NET_DEVICE
        {
            device = Some(Box::new(UpNotNetDevice::new(
                device_id.to_string(),
                device_info.clone(),
            )));
        }

        device
    }

    fn create_engine_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        Some(Box::new(UpEngineDevice::new(
            device_id,
            device_info.clone(),
        )))
    }
}

impl UpDeviceFactory for DeviceFactory {
    fn create_device(
        &self,
        device_id: &str,
        device_info: &UpDeviceInfo,
    ) -> Option<Box<dyn UpDevice>> {
        let mut device = self.create_voice_box(device_id, device_info);

        if device.is_none() {
            device = self.create_washing_device(device_id, device_info);
        }

        if device.is_none() {
            device = self.create_aggregate_device(device_id, device_info);
        }

        if device.is_none() {
            device = self.create_not_net_device(device_id, device_info);
        }

        if device.is_none() {
            device = self.create_engine_device(device_id, device_info);
        }

        device
    }
}

impl Default for DeviceFactory {
    fn default() -> Self {
        Self::new()
    }
}
