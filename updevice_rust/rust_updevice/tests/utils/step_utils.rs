use std::collections::HashMap;
use std::str::FromStr;
use std::sync::Arc;

use cucumber::gherkin::Step;
use logic_engine::device::command::Command;
use logic_engine::device::device_attribute::UpDeviceAttribute;
use logic_engine::device::device_caution::UpDeviceCaution;
use logic_engine::device::device_command::UpDeviceCommand;
use logic_engine::device_config::business_attr::{BusinessAttr, CmdPara};
use logic_engine::device_config::modifier::{Modifier, ModifierAction, ModifierTrigger};
use logic_engine::engine::attribute::LEAttribute;
use logic_engine::engine::attribute::value_range::date_value_range::LEDateValueRange;
use logic_engine::engine::attribute::value_range::LEValueRange;
use logic_engine::engine::attribute::value_range::list_value_range::LEListValueRange;
use logic_engine::engine::attribute::value_range::step_double_value_range::LEDoubleStepValueRange;
use logic_engine::engine::attribute::value_range::time_value_range::LETimeValueRange;
use logic_engine::engine::caution::LECaution;
use once_cell::sync::OnceCell;
use rust_userdomain::models::device_info::{
    DeviceAuth, DeviceInfo, DeviceOwnerInfo, DevicePermission,
};
use serde_json::Value;

use rust_updevice::api::device_injection::UpDeviceInjection;
use rust_updevice::api::error::Result;
use rust_updevice::api::event::UpDeviceEvent;
use rust_updevice::daemon::preparing_state::PreparingState;
use rust_updevice::data_source::device_list_data_source::MockUpDeviceDataSource;
use rust_updevice::device::up_device::UpDevice;
use rust_updevice::models::device_info::UpDeviceInfo;
use rust_updevice::models::device_toolkit::MockUpDeviceToolkit;
use rust_usdk::toolkit_ffi::uhsd_usr_model::{uhsd_usr_dev_connect_state_e, uhsd_usr_dev_online_state_e, uhsd_usr_device_bind_state_e, uhsd_usr_device_offline_cause_e, uhsd_usr_device_sleep_state_e, DeviceModelInfo, ErrorInfo, UhsdPair};
use rust_usdk::toolkit_ffi::uhsd_usr_model::DeviceInfo as UhsdDeviceInfo;

use crate::utils::device_test_holder::UpDeviceTestHolder;

pub struct StepUtils {}

impl StepUtils {}

impl StepUtils {
    pub fn new() -> Self {
        StepUtils {}
    }

    pub fn verify_notify_times(&self, count_str: String, key: &str) {
        let expected_times = count_str.parse::<usize>().unwrap();
        let actual_times = UpDeviceTestHolder::get_instance().get_notify_times(&key);
        assert_eq!(expected_times, actual_times);
    }

    pub fn assert_equal_device_list(&self, step: &Step, device_list: Vec<Arc<dyn UpDevice>>) {
        let mut device_map = HashMap::new();
        for device in device_list {
            device_map.insert(device.device_id(), device);
        }

        assert_eq!(
            step.table().unwrap().rows.iter().skip(1).len(),
            device_map.values().len()
        );

        for row in step.table().unwrap().rows.iter().skip(1) {
            //期望的设备列表为空
            if row.is_empty() {
                //则直接校验实际的设备列表是不是空，是则断言成功
                assert_eq!(true, device_map.is_empty());
                return;
            }
            let device_id = row[1].clone();
            let device = device_map.get(&device_id);
            assert_eq!(true, device.is_some());
            let device = device.unwrap();
            assert_eq!(row[0].clone(), device.protocol());
            assert_eq!(
                row[2].clone(),
                device
                    .get_info()
                    .type_id()
                    .unwrap_or_else(|| "".to_string())
            );
            assert_eq!(
                row[4].clone(),
                device
                    .get_info()
                    .type_code()
                    .unwrap_or_else(|| "".to_string())
            );
            assert_eq!(row[5].clone(), device.get_info().model());
            assert_eq!(row[6].clone(), device.get_info().product_code());
            assert_eq!(row[7].clone(), device.get_info().parent_id().unwrap());
            // assert_eq!(row[8].clone(), device.get_info().sub_device_id());
        }
    }

    pub fn assert_equal_device_info_list(&self, step: &Step, info_list: Vec<UpDeviceInfo>) {
        let mut info_map = HashMap::new();
        for info in info_list {
            info_map.insert(info.device_id(), info);
        }

        assert_eq!(
            step.table().unwrap().rows.iter().skip(1).len(),
            info_map.values().len()
        );

        for row in step.table().unwrap().rows.iter().skip(1) {
            //期望的设备列表为空
            if row.is_empty() {
                //则直接校验实际的设备列表是不是空，是则断言成功
                assert_eq!(true, info_map.is_empty());
                return;
            }
            let device_id = row[1].clone();
            let info = info_map.get(&device_id);
            assert_eq!(true, info.is_some());
            let info = info.unwrap();
            assert_eq!(
                row[2].clone(),
                info.type_id().unwrap_or_else(|| "".to_string())
            );
            assert_eq!(
                row[4].clone(),
                info.type_code().unwrap_or_else(|| "".to_string())
            );
            assert_eq!(row[5].clone(), info.model());
            assert_eq!(row[6].clone(), info.product_code());
            assert_eq!(row[7].clone(), info.parent_id().unwrap());
            assert_eq!(row[8].clone(), info.sub_device_id());
        }
    }

    pub fn assert_equal_device_info(&self, step: &Step, info: Option<UpDeviceInfo>) {
        let is_some = info.is_some();
        let info = info.unwrap();
        for row in step.table().unwrap().rows.iter().skip(1) {
            //期望的设备列表为空
            if row.is_empty() {
                //则直接校验实际的设备信息是不是空，是则断言成功
                assert_eq!(false, is_some);
                return;
            }
            assert_eq!(true, is_some);
            assert_eq!(
                row[2].clone(),
                info.type_id().unwrap_or_else(|| "".to_string())
            );
            assert_eq!(
                row[4].clone(),
                info.type_code().unwrap_or_else(|| "".to_string())
            );
            assert_eq!(row[5].clone(), info.model());
            assert_eq!(row[6].clone(), info.product_code());
            assert_eq!(row[7].clone(), info.parent_id().unwrap());
            assert_eq!(row[8].clone(), info.sub_device_id());
        }
    }

    pub fn assert_equal_cautions(
        &self,
        actual_cautions: &Vec<LECaution>,
        expected_cautions: &Vec<LECaution>,
    ) {
        assert_eq!(actual_cautions.len(), expected_cautions.len());
        for (index, expected_caution) in expected_cautions.iter().enumerate() {
            let actual_caution = &actual_cautions[index];
            assert_eq!(expected_caution.name(), actual_caution.name());
            assert_eq!(expected_caution.clear(), actual_caution.clear());
        }
    }

    pub fn assert_equal_business_functions(
        &self,
        actual_business_functions: &Vec<BusinessAttr>,
        expected_business_functions: &Vec<BusinessAttr>,
    ) {
        assert_eq!(
            actual_business_functions.len(),
            expected_business_functions.len()
        );
        for (index, expected_business_attr) in expected_business_functions.iter().enumerate() {
            let actual_business_attr = &actual_business_functions[index];
            assert_eq!(expected_business_attr.name, actual_business_attr.name);
            assert_eq!(expected_business_attr.desc, actual_business_attr.desc);
            assert_eq!(
                expected_business_attr.cmd_para.name,
                actual_business_attr.cmd_para.name
            );
        }
    }

    pub fn assert_equal_modifier_configs(
        &self,
        actual_modifier_configs: &Vec<Modifier>,
        expected_modifier_configs: &Vec<Modifier>,
    ) {
        assert_eq!(
            actual_modifier_configs.len(),
            expected_modifier_configs.len()
        );
        for (index, expected_modifier) in expected_modifier_configs.iter().enumerate() {
            let actual_modifier = &actual_modifier_configs[index];
            assert_eq!(expected_modifier.priority, actual_modifier.priority);
            assert_eq!(
                expected_modifier.trigger.operator,
                actual_modifier.trigger.operator
            );
            self.assert_equal_modifier_action_configs(
                &actual_modifier.actions,
                &expected_modifier.actions,
            );
        }
    }

    pub fn assert_equal_modifier_action_configs(
        &self,
        actual_modifier_actions: &Vec<ModifierAction>,
        expected_modifier_actions: &Vec<ModifierAction>,
    ) {
        assert_eq!(
            actual_modifier_actions.len(),
            expected_modifier_actions.len()
        );
        for (index, expected_action) in expected_modifier_actions.iter().enumerate() {
            let actual_action = &actual_modifier_actions[index];
            assert_eq!(expected_action.name, actual_action.name);
            assert_eq!(expected_action.rewrite_fields, actual_action.rewrite_fields);
            assert_eq!(expected_action.writable, actual_action.writable);
            assert_eq!(expected_action.default_value, actual_action.default_value);
        }
    }

    pub fn convert_device_info_from_step(&self, step: &Step) -> Vec<UpDeviceInfo> {
        let mut result: Vec<UpDeviceInfo> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let extra_json_string = row[9].clone();
            let extra = json_string_to_hash_map(&extra_json_string);

            let owner_info = DeviceOwnerInfo {
                user_id: "".to_string(),
                mobile: get_value_by_hashmap(&extra, "DI-Relation.phoneNum"),
                user_nick_name: "".to_string(),
                uc_user_id: get_value_by_hashmap(&extra, "DI-Relation.ucUserId"),
            };

            let auth = DeviceAuth {
                control: get_bool_value_by_hashmap(&extra, "DI-Permission.ctrl"),
                set: get_bool_value_by_hashmap(&extra, "DI-Permission.edit"),
                view: get_bool_value_by_hashmap(&extra, "DI-Permission.view"),
            };

            let permission = DevicePermission {
                auth,
                auth_type: get_value_by_hashmap(&extra, "DI-Permission.authType"),
            };

            let user_domain_device_info = DeviceInfo {
                device_id: row[1].clone(),
                device_name: get_value_by_hashmap(&extra, "DI-Basic.displayName"),
                dev_name: "".to_string(),
                device_type: Some(row[4].clone()),
                family_id: get_value_by_hashmap(&extra, "DI-Relation.familyId")
                    .trim_matches('"')
                    .to_string(),
                owner_id: get_value_by_hashmap(&extra, "DI-Relation.ownerId"),
                permission,
                wifi_type: Some(row[2].clone()),
                device_net_type: get_option_value_by_hashmap(&extra, "DI-Basic.deviceNetType"),
                bind_time: get_value_by_hashmap(&extra, "DI-Basic.bindTime"),
                is_online: get_bool_value_by_hashmap(&extra, "DI-Basic.online"),
                owner_info,
                sub_device_ids: vec![row[8].clone()],
                parent_id: Some(row[7].clone()),
                device_role: get_option_value_by_hashmap(&extra, "DI-Basic.deviceRole"),
                device_role_type: get_option_value_by_hashmap(&extra, "DI-Basic.deviceRoleType"),
                apptype_name: get_value_by_hashmap(&extra, "DI-Basic.apptypeName"),
                apptype_code: get_value_by_hashmap(&extra, "DI-Basic.apptypeCode"),
                category_grouping: get_value_by_hashmap(&extra, "DI-Product.categoryGrouping"),
                barcode: get_option_value_by_hashmap(&extra, "DI-Product.barcode"),
                bind_type: get_option_value_by_hashmap(&extra, "DI-Product.bind_type"),
                brand: get_value_by_hashmap(&extra, "DI-Product.brand"),
                image_addr1: get_value_by_hashmap(&extra, "DI-Product.imageAddr1"),
                model: row[5].clone(),
                prod_no: row[6].clone(),
                room_name: get_value_by_hashmap(&extra, "DI-Basic.room"),
                room_id: get_value_by_hashmap(&extra, "DI-Basic.roomId"),
                access_type: get_option_value_by_hashmap(&extra, "DI-Product.accessType"),
                config_type: get_value_by_hashmap(&extra, "DI-Product.configType"),
                communication_mode: None,
                device_floor_id: get_value_by_hashmap(&extra, "DI-Relation.devFloorId"),
                device_floor_order_id: get_value_by_hashmap(&extra, "DI-Relation.devFloorOrderId"),
                device_floor_name: get_value_by_hashmap(&extra, "DI-Relation.devFloorName"),
                apptype_icon: get_value_by_hashmap(&extra, "DI-Basic.apptypeIcon"),
                device_group_id: get_option_value_by_hashmap(&extra, "DI-Basic.deviceGroupId"),
                device_group_type: get_option_value_by_hashmap(&extra, "DI-Basic.deviceGroupType"),
                no_keep_alive: get_bool_value_by_hashmap(&extra, "DI-Product.noKeepAlive"),
                two_groping_name: "".to_string(),
                card_page_img: "".to_string(),
                support_flag: 1,
                shared_device_flag: 0, // TODO 待修改
                share_device_card_info: vec![], // TODO 待修改
                attachment_sort_code: 0, // TODO 待修改
                device_share_support_flag: false, // TODO 待修改
                card_sort:0,
                card_status:0,
                aggregation_parent_id: "".to_string(), // TODO 待修改
                support_aggregation_flag: 0, // TODO 待修改
                device_aggregate_type: "".to_string(), // TODO 待修改
                rebind: 0, // TODO 待修改
            };
            let device_info = user_domain_device_info.into();
            result.push(device_info);
        }
        result
    }

    pub fn convert_attribute_list_from_step(&self, step: &Step) -> Vec<UpDeviceAttribute> {
        let mut result: Vec<UpDeviceAttribute> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let attribute = UpDeviceAttribute::new(row[0].clone(), row[1].clone());
            result.push(attribute);
        }
        result
    }

    pub fn convert_usdk_device_model_info_from_step(&self, step: &Step) -> DeviceModelInfo {
        let mut attr_list = Vec::new();
        let mut alarm_list = Vec::new();
        let mut alarm_timestamp_ms = 0;
        let mut event_list = Vec::new();
        let mut err_info = None;
        for row in step.table().unwrap().rows.iter().skip(1) {
            let attr_map = self.get_string_map(row[0].clone());
            for (key, value) in attr_map.into_iter() {
                let attribute = UhsdPair {
                    name: key.clone(),
                    value: value.clone(),
                };
                attr_list.push(attribute);
            }
            let alarm_map = self.get_string_map(row[1].clone());
            for (key, value) in alarm_map.into_iter() {
                let alarm = UhsdPair {
                    name: key.clone(),
                    value: value.clone(),
                };
                alarm_list.push(alarm);
            }
            alarm_timestamp_ms = row[2].parse().unwrap();
            let event_map = self.get_string_map(row[3].clone());
            for (key, value) in event_map.into_iter() {
                let event = UhsdPair {
                    name: key.clone(),
                    value: value.clone(),
                };
                event_list.push(event);
            }
            let event_info = self.get_string_map_with_split(row[4].clone(), ";", ":");
            err_info = Some(ErrorInfo {
                code: event_info
                    .get("code")
                    .unwrap_or(&"0".to_string())
                    .clone()
                    .parse()
                    .unwrap(),
                desc: event_info.get("desc").unwrap_or(&"".to_string()).clone(),
                pair_list: Vec::new(),
            });
        }
        let device_model_info = DeviceModelInfo {
            attr_list: {
                attr_list.sort_by(|a, b| a.name.cmp(&b.name));
                attr_list
            },
            alarm_list,
            alarm_timestamp_ms,
            event_list,
            err_info: if err_info.is_some() {
                err_info.unwrap()
            } else {
                ErrorInfo::default()
            },
        };
        device_model_info
    }
    pub fn convert_usdk_device_info_from_step(&self, step: &Step) -> UhsdDeviceInfo {
        let row = step.table().unwrap().rows.iter().skip(1).next().unwrap();
        UhsdDeviceInfo {
            device_id: row[0].clone(),
            type_id: row[1].clone(),
            product_code: row[2].clone(),
            bind_state: uhsd_usr_device_bind_state_e::UHSD_USR_DEV_BIND_STATE_ERR,
            state_code: 0,
            sleep_state: uhsd_usr_device_sleep_state_e::UHSD_USR_DEVICE_SLEEP_STATE_ERR,
            offline_cause: uhsd_usr_device_offline_cause_e::UHSD_USR_DEVICE_OFFLINE_CAUSE_NULL,
            offline_days: 0,
            online_state: uhsd_usr_dev_online_state_e::UHSD_USR_DEV_ONLINE_STATE_ERR,
            connect_state: uhsd_usr_dev_connect_state_e::UHSD_USR_DEV_CONNECT_STATE_ERR,
            is_group: 0,
            device_name: "TODO 未适配单元测试，只新增了字段".to_string(),
        }
    }
    pub fn convert_uhsd_pair_list_from_step(&self, step: &Step) -> Vec<UhsdPair> {
        let mut result: Vec<UhsdPair> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let uhsd_pair = UhsdPair {
                name: row[0].clone(),
                value: row[1].clone(),
            };
            result.push(uhsd_pair);
        }
        result
    }

    pub fn convert_device_change_event_from_step(&self, step: &Step) -> Vec<UpDeviceEvent> {
        let mut result: Vec<UpDeviceEvent> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let device_id = row[1].clone();
            let event_name = row[0].clone();
            match event_name.as_str() {
                "CautionChanged" => {
                    result.push(UpDeviceEvent::CautionChanged { device_id });
                }
                "ConnectStateChanged" => {
                    result.push(UpDeviceEvent::ConnectStateChanged { device_id });
                }
                "OnlineStateChanged" => {
                    result.push(UpDeviceEvent::OnlineStateChanged { device_id });
                }
                "AttributeChanged" => {
                    result.push(UpDeviceEvent::AttributeChanged { device_id });
                }
                _ => {}
            }
        }
        result
    }

    pub fn convert_attribute_list_from_string(
        &self,
        attribute_list_string: String,
    ) -> Vec<UpDeviceAttribute> {
        let mut result: Vec<UpDeviceAttribute> = Vec::new();
        if attribute_list_string.is_empty() {
            return result;
        }
        for attribute in attribute_list_string.split(';') {
            let attribute_pair: Vec<&str> = attribute.split(',').collect();
            let attribute = UpDeviceAttribute::new(
                attribute_pair[0].to_string(),
                attribute_pair[1].to_string(),
            );
            result.push(attribute);
        }
        result
    }

    pub fn convert_uhsdk_pair_list_from_string(&self, pair_list_string: String) -> Vec<UhsdPair> {
        let mut result: Vec<UhsdPair> = Vec::new();
        if pair_list_string.is_empty() {
            return result;
        }
        for pair in pair_list_string.split(';') {
            let pair_pair: Vec<&str> = pair.split(',').collect();
            let pair = UhsdPair {
                name: pair_pair[0].to_string(),
                value: pair_pair[1].to_string(),
            };
            result.push(pair);
        }
        result
    }

    pub fn convert_map_from_string(&self, map_string: String) -> HashMap<String, String> {
        let mut map: HashMap<String, String> = HashMap::new();
        if map_string.is_empty() {
            return map;
        }
        for map_item in map_string.split(';') {
            let key_value: Vec<&str> = map_item.split(',').collect();
            map.insert(key_value[0].to_string(), key_value[1].to_string());
        }
        map
    }

    pub fn convert_alarm_list_from_step(&self, step: &Step) -> Vec<UpDeviceCaution> {
        let mut result: Vec<UpDeviceCaution> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            if row.len() < 3 {
                continue;
            }
            let attribute = UpDeviceCaution::new(row[0].clone(), row[1].clone(), row[2].clone());
            result.push(attribute);
        }
        result
    }

    pub fn convert_to_attribute_list_by_step_table(&self, step: &Step) -> Vec<LEAttribute> {
        let mut attribute_list: Vec<LEAttribute> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let name = row[0].clone();
            let value = row[1].clone();
            let value_range_type = row[2].clone();
            let writable = identification_letter_string_to_bool(row[3].clone());
            let default_value = string_to_option_string(row[4].clone());
            let value_range_value = self.convert_to_value_range_by(&value_range_type);

            let attribute = LEAttribute::new(
                name,
                Some(Vec::new()),
                value_range_value,
                "".to_string(),
                false,
                writable,
                false,
                default_value,
                None,
                Some(value),
            );
            attribute_list.push(attribute);
        }
        attribute_list
    }

    fn convert_to_value_range_by(&self, value_range_type: &str) -> Arc<dyn LEValueRange> {
        let le_value_range: Arc<dyn LEValueRange>;
        match value_range_type {
            "LIST" => {
                let list_value = LEListValueRange::new(vec![]);
                le_value_range = Arc::new(list_value);
            }
            "STEP" => {
                let step_value = LEDoubleStepValueRange::new(
                    "0".to_string(),
                    "0".to_string(),
                    "0".to_string(),
                    None,
                    None,
                );
                le_value_range = Arc::new(step_value);
            }
            "TIME" => {
                let time_value = LETimeValueRange::new("".to_string(), 0, 0, 0, 0, 0, 0);
                le_value_range = Arc::new(time_value);
            }
            "DATE" => {
                let date_value =
                    LEDateValueRange::new("".to_string(), "".to_string(), "".to_string());
                le_value_range = Arc::new(date_value);
            }
            _ => {
                panic!("value range type is mismatch！")
            }
        }

        le_value_range
    }

    pub fn convert_cautions_from_step(&self, step: &Step) -> Vec<LECaution> {
        let mut cautions: Vec<LECaution> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let name = row[0].clone();
            let code = string_to_option_string(row[1].clone());
            let desc = string_to_option_string(row[2].clone());
            let time = string_to_option_string(row[3].clone());
            let clear = identification_letter_string_to_bool(row[4].clone());
            let caution = LECaution::new(name, code, desc, clear, time);
            cautions.push(caution);
        }
        cautions
    }

    pub fn convert_business_list_from_step(&self, step: &Step) -> Vec<BusinessAttr> {
        let mut business_list: Vec<BusinessAttr> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let name = row[0].clone();
            let desc = row[1].clone();
            let cmd_name = row[2].clone();

            let cmd_para = CmdPara {
                name: cmd_name,
                attr_name_list: vec![],
            };
            let business = BusinessAttr {
                name,
                desc,
                desc1: None,
                desc2: None,
                desc3: None,
                desc4: None,
                detail_desc: None,
                alias: None,
                cmd_para,
            };
            business_list.push(business);
        }
        business_list
    }

    pub fn convert_modifier_configs_from_step(&self, step: &Step) -> Vec<Modifier> {
        let mut modifier_list: Vec<Modifier> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let priority = row[0].clone().parse();
            let trigger_operator = row[1].clone();
            let actions_json = row[2].clone();

            let trigger = ModifierTrigger {
                operator: trigger_operator,
                conditions: None,
                alarms: None,
            };
            let actions = serde_json::from_str(&actions_json);

            let modifier = Modifier {
                priority: if priority.is_ok() {
                    Some(priority.unwrap())
                } else {
                    None
                },
                trigger,
                actions: if actions.is_ok() {
                    actions.unwrap()
                } else {
                    vec![]
                },
            };
            modifier_list.push(modifier);
        }
        modifier_list
    }

    pub fn convert_event_from_step(&self, step: &Step, device_id: String) -> Vec<String> {
        let mut result: Vec<String> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let event_name = row[0].clone();
            match event_name.as_str() {
                "EVENT_ATTRIBUTES_CHANGE" => result.push(
                    UpDeviceEvent::AttributeChanged {
                        device_id: device_id.clone(),
                    }
                        .to_string(),
                ),
                "EVENT_DEVICE_CAUTION" => result.push(
                    UpDeviceEvent::CautionChanged {
                        device_id: device_id.clone(),
                    }
                        .to_string(),
                ),
                "EVENT_CONNECTION_CHANGE" => result.push(
                    UpDeviceEvent::ConnectStateChanged {
                        device_id: device_id.clone(),
                    }
                        .to_string(),
                ),
                "EVENT_ONLINE_STATE_CHANGE" => result.push(
                    UpDeviceEvent::OnlineStateChanged {
                        device_id: device_id.clone(),
                    }
                        .to_string(),
                ),
                "EVENT_BASE_INFO_CHANGE" => result.push(
                    UpDeviceEvent::BaseInfoChanged {
                        device_id: device_id.clone(),
                    }
                        .to_string(),
                ),
                _ => {}
            }
        }
        result
    }

    pub fn get_string_map(&self, map_string: String) -> HashMap<String, String> {
        let mut map: HashMap<String, String> = HashMap::new();
        for map_item in map_string.split(',') {
            let key_value: Vec<&str> = map_item.split('=').collect();
            map.insert(key_value[0].to_string(), key_value[1].to_string());
        }
        map
    }

    pub fn get_string_map_with_split(
        &self,
        map_string: String,
        split1: &str,
        split2: &str,
    ) -> HashMap<String, String> {
        let mut map: HashMap<String, String> = HashMap::new();
        for map_item in map_string.split(split1) {
            let key_value: Vec<&str> = map_item.split(split2).collect();
            map.insert(key_value[0].to_string(), key_value[1].to_string());
        }
        map
    }

    pub fn get_string_vec(&self, vec_string: String) -> Vec<String> {
        vec_string.split(',').map(|s| s.to_string()).collect()
    }

    pub(crate) fn convert_device_state_map(&self, step: &Step) -> HashMap<String, PreparingState> {
        let mut state_map = HashMap::new();
        if let Some(table) = step.table.as_ref() {
            for row in table.rows.iter().skip(1) {
                if row.len() < 3 {
                    panic!("convert_device_state_map: table row length error!")
                }
                let device_id = row[1].clone();
                let state = PreparingState::from_str(&row[2])
                    .expect("convert_device_state_map: illegal device status!");
                state_map.insert(device_id, state);
            }
        }
        state_map
    }
    pub(crate) fn assert_devices_state(
        &self,
        expected_states: HashMap<String, PreparingState>,
        result: &Result<Vec<Arc<dyn UpDevice>>>,
    ) {
        match result {
            Ok(actual_devices) => {
                for actual_device in actual_devices {
                    let device_id = actual_device.device_id();
                    if let Some(expected_state) = expected_states.get(&device_id) {
                        let actual_state = actual_device.get_device_core().get_state();
                        assert_eq!(expected_state, &actual_state);
                    }
                }
            }
            Err(_) => {
                panic!("assert_devices_state: update device list failed!")
            }
        }
    }

    pub fn convert_logic_engine_commands_from_step(&self, step: &Step) -> Vec<Command> {
        let mut commands: Vec<Command> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let map = self.get_string_map_with_split(row[1].clone(), ";", ",");
            for (key, value) in map {
                let command = Command::new(key, value, true);
                commands.push(command);
            }
        }
        commands
    }

    pub fn convert_engine_commands_from_step(&self, step: &Step) -> Vec<Command> {
        let mut commands: Vec<Command> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let command = Command::new(row[0].clone(), row[1].clone(), false);
            commands.push(command);
        }
        commands
    }

    pub fn convert_to_up_device_commands_from_step(&self, step: &Step) -> Vec<UpDeviceCommand> {
        let mut commands: Vec<UpDeviceCommand> = Vec::new();
        for row in step.table().unwrap().rows.iter().skip(1) {
            let name = row[0].clone();
            let mut option_name = None;
            if !name.is_empty() {
                option_name = Some(name);
            }
            let map = self.get_string_map_with_split(row[1].clone(), ";", ",");
            let mut attributes: Vec<UpDeviceAttribute> = Vec::new();
            for (key, value) in map {
                let attribute = UpDeviceAttribute::new(key.clone(), value);
                attributes.push(attribute);
            }
            let command = UpDeviceCommand::new(option_name, attributes);
            commands.push(command);
        }
        commands
    }
}

static STEP_UTILS: OnceCell<StepUtils> = OnceCell::new();

pub fn get_step_utils() -> &'static StepUtils {
    STEP_UTILS.get_or_init(|| StepUtils::new())
}

pub fn get_mock_device_data_source_mut<'a>() -> &'a mut MockUpDeviceDataSource {
    UpDeviceInjection::get_instance()
        .get_device_data_source_mut()
        .as_any_mut()
        .downcast_mut::<MockUpDeviceDataSource>()
        .unwrap()
}

pub fn get_mock_device_toolkit_mut<'a>() -> &'a mut MockUpDeviceToolkit {
    UpDeviceInjection::get_instance()
        .get_device_toolkit_mut()
        .as_any_mut()
        .downcast_mut::<MockUpDeviceToolkit>()
        .unwrap()
}

pub fn identification_letter_string_to_bool(str: String) -> bool {
    if str == "TRUE" || str == "true" || str == "1" {
        true
    } else {
        false
    }
}

pub fn identification_string_to_bool(str: String) -> bool {
    if str == "成功" {
        true
    } else {
        false
    }
}

fn json_string_to_hash_map(json_str: &str) -> HashMap<String, String> {
    let value = serde_json::from_str(json_str).unwrap();
    let mut result = HashMap::new();

    if let Value::Object(map) = value {
        for (key, value) in map {
            let value_str = match value {
                Value::String(string) => string,
                _ => "".to_string(),
            };
            result.insert(key.to_string(), value_str);
        }
    }
    result
}

pub fn get_value_by_hashmap(hashmap: &HashMap<String, String>, key: &str) -> String {
    if let Some(value) = hashmap.get(key) {
        return value.clone();
    }
    "".to_string()
}

fn get_bool_value_by_hashmap(hashmap: &HashMap<String, String>, key: &str) -> bool {
    let value = get_value_by_hashmap(hashmap, key);
    identification_letter_string_to_bool(value)
}

fn get_option_value_by_hashmap(hashmap: &HashMap<String, String>, key: &str) -> Option<String> {
    let value = get_value_by_hashmap(hashmap, key);
    if value.is_empty() {
        None
    } else {
        Some(value)
    }
}

fn string_to_option_string(str: String) -> Option<String> {
    if str.is_empty() {
        None
    } else {
        Some(str)
    }
}

pub fn assert_attribute_list_equal(actual: &Vec<Arc<LEAttribute>>, expect: &Vec<LEAttribute>) {
    assert_eq!(actual.len(), expect.len());

    for (index, expect_attribute) in expect.iter().enumerate() {
        let actual_attribute = &actual[index];
        assert_attribute_equal(actual_attribute, expect_attribute);
    }
}

pub fn assert_attribute_equal(actual: &Arc<LEAttribute>, expect: &LEAttribute) {
    assert_eq!(actual.name(), expect.name());
    assert_eq!(actual.writable(), expect.writable());
    assert_eq!(actual.value(), expect.value());
}
