#!/bin/bash

# 检查目录是否存在
if [ ! -d "$1" ]; then
  echo "目录不存在：$1"
  exit 1
fi

# 扫描目录下的所有 .ts 文件
find "$1" -type f -name "*.ts" | while read -r file; do
  # 使用临时文件存储修改后的内容
  temp_file=$(mktemp)

  # 设置标志，确保只替换第一次出现的行
  replaced=false

  # 逐行读取文件
  while IFS= read -r line; do
    # 检查是否为目标行，且只替换第一次出现的行
    if [[ "$line" == "import * as flatbuffers from 'flatbuffers';" && "$replaced" == false ]]; then
      # 替换该行
      echo "import * as flatbuffers from '@ohos/flatbuffers';" >> "$temp_file"
      replaced=true
    else
      # 其他行保持不变
      echo "$line" >> "$temp_file"
    fi
  done < "$file"

  # 如果文件发生了修改，使用临时文件替换原文件
  if [ "$replaced" = true ]; then
    mv "$temp_file" "$file"
    echo "已更新文件：$file"
  else
    rm "$temp_file"
  fi
done

echo "脚本执行完成。"
