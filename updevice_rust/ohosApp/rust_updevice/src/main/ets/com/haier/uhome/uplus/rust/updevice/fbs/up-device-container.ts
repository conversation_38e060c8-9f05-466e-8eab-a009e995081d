// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import { BoolWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/updevice/fbs/bool-wrapper.js';
import { Int32Wrapper } from '../../../../../../../com/haier/uhome/uplus/rust/updevice/fbs/int32-wrapper.js';
import { NoneWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/updevice/fbs/none-wrapper.js';
import { StrWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/updevice/fbs/str-wrapper.js';


export enum UpDeviceContainer {
  NONE = 0,
  Int32Wrapper = 1,
  StrWrapper = 2,
  BoolWrapper = 3,
  NoneWrapper = 4
}

export function unionToUpDeviceContainer(
  type: UpDeviceContainer,
  accessor: (obj:BoolWrapper|Int32Wrapper|NoneWrapper|StrWrapper) => BoolWrapper|Int32Wrapper|NoneWrapper|StrWrapper|null
): BoolWrapper|Int32Wrapper|NoneWrapper|StrWrapper|null {
  switch(UpDeviceContainer[type]) {
    case 'NONE': return null; 
    case 'Int32Wrapper': return accessor(new Int32Wrapper())! as Int32Wrapper;
    case 'StrWrapper': return accessor(new StrWrapper())! as StrWrapper;
    case 'BoolWrapper': return accessor(new BoolWrapper())! as BoolWrapper;
    case 'NoneWrapper': return accessor(new NoneWrapper())! as NoneWrapper;
    default: return null;
  }
}

export function unionListToUpDeviceContainer(
  type: UpDeviceContainer, 
  accessor: (index: number, obj:BoolWrapper|Int32Wrapper|NoneWrapper|StrWrapper) => BoolWrapper|Int32Wrapper|NoneWrapper|StrWrapper|null, 
  index: number
): BoolWrapper|Int32Wrapper|NoneWrapper|StrWrapper|null {
  switch(UpDeviceContainer[type]) {
    case 'NONE': return null; 
    case 'Int32Wrapper': return accessor(index, new Int32Wrapper())! as Int32Wrapper;
    case 'StrWrapper': return accessor(index, new StrWrapper())! as StrWrapper;
    case 'BoolWrapper': return accessor(index, new BoolWrapper())! as BoolWrapper;
    case 'NoneWrapper': return accessor(index, new NoneWrapper())! as NoneWrapper;
    default: return null;
  }
}
