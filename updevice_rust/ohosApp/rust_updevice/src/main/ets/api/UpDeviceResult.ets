import { ByteBuffer } from "@ohos/flatbuffers";
import { UpDeviceFlat } from "../com/haier/uhome/uplus/rust/updevice/fbs";
import { UpDeviceLog } from "./UpDeviceLog";
import { UpDeviceCode, UpDeviceRustResult, ErrorMessage, RustConstant } from "./util";

export class UpDeviceResult<T> {
  code: number;
  error: string;
  data?: T;
  extraCode?: string;

  constructor(code: number, error: string, data?: T, extraCode?: string) {
    this.code = code;
    this.error = error;
    this.data = data;
    this.extraCode = extraCode;
  }

  isSuccess(): boolean {
    return this.code === NapiCode.Success;
  }

  static Success<T>(data?: T): UpDeviceResult<T> {
    return new UpDeviceResult<T>(NapiCode.Success, "Success", data);
  }

  static Error<T>(code: number, error: string, data?: T, extraCode?: string): UpDeviceResult<T> {
    if (code == NapiCode.Success) {
      throw Error('code can not be Success(0)');
    }
    return new UpDeviceResult<T>(code, error, data, extraCode);
  }
}

export enum NapiCode {
  Success = 0,
  IllegalParameters = 100,
  UserTaskJoinError = 101,
  UserTaskFaild = 102,
  HttpTokenIvalidate = 103,
  HttpRequstFaild = 104,
  RefreshUserFaild = 105,
  ParametersError = 106,
  ExecuteError = 107,
  NapiCallError = 108,
  SerdeJsonError = 109,
  LogicEngineNotExist = 120004,
}

export function handleRustVoidBuffer(buf: ArrayBuffer | null, tag: string, method: string) {
  handleRustVoidBufferResult(buf, tag, method);
}

export function handleRustVoidBufferResult(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UpDeviceRustResult<void> {
  if (buf) {
    const flat = UpDeviceFlat.getRootAsUpDeviceFlat(new ByteBuffer(new Uint8Array(buf)));
    const code = flat.code() ?? UpDeviceCode.NoCode;
    const error = flat.error() ?? ErrorMessage.NoError;
    UpDeviceLog.debug(tag, `${method}: ${code}, ${flat.error()}`);
    return new UpDeviceRustResult(code, error);
  } else {
    UpDeviceLog.warn(tag, `${method}: ${RustConstant.Call_Failure}`);
    return new UpDeviceRustResult(UpDeviceCode.RustCallFailure, ErrorMessage.RustCallFailure);
  }
}