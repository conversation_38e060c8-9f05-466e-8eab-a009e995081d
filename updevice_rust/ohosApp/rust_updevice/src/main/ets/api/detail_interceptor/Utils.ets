import { uri } from '@kit.ArkTS';

export const PARAM_DEVICE_ID: string = "deviceId";
export const MODEL_CHECK_KEY: string = "modelCheckType";

export const PARAM_IS_BINDING_SUCCESS: string = "bindSuccess";

export function getDeviceId(url: string): string | undefined {
  try {
    const uriObj = new uri.URI(url);
    const deviceId = uriObj.getQueryValue(PARAM_DEVICE_ID);
    return deviceId;
  } catch (e) {
    console.error("[updevice_harmony] deviceId was not found in url");
    return;
  }
}

export function getModelCheckType(url: string): string {
  try {
    const uriObj = new uri.URI(url);
    const modelCheckType = uriObj.getQueryValue(MODEL_CHECK_KEY);
    if (modelCheckType && modelCheckType.length > 0) {
      return modelCheckType;
    }
  } catch (e) {
    console.error("[updevice_harmony] modelCheckType was not found in url");
  }
  return "1";
}

export function getBindSuccessFlag(url: string): boolean {
  try {
    const uriObj = new uri.URI(url);
    const bindFlag = uriObj.getQueryValue(PARAM_IS_BINDING_SUCCESS);
    return bindFlag === "1";
  } catch (e) {
    console.warn("[updevice_harmony] bindFlag was not found in url");
    return false;
  }
}