import { ResourceInfo, ResourceManager, ResourceType } from '@uplus/rust_resource';
import { ERROR_DEVICE_PREPARE_OPENRES, PrepareResult } from '@uplus/uppluginfoundation';
import { UpDevice } from '../device/UpDevice';
import { DeviceHandler } from './DeviceHandler';
import { getModelCheckType } from './Utils';
import { Page } from '@uplus/upvdn';

const RESOURCE_ERROR: string = "加载失败，请稍后再试";

export class DeviceResourceHandler extends DeviceHandler {
  needHandleAsync(page: Page): boolean {
    console.log("[updevice_harmony] DeviceResourceHandler start.");
    console.log("[updevice_harmony] DeviceResourceHandler before resourceInfo ---> ",
      JSON.stringify(this.resourceInfo));

    // 通过modelCheckType 判断
    const modelCheckType = getModelCheckType(page.getOriginUrl());
    const checkDevice = this.getDeviceByModelCheckType(modelCheckType);
    try {
      if (!this.resourceInfo && checkDevice) {
        console.log("[updevice_harmony] DeviceResourceHandler ---> device:", JSON.stringify(checkDevice));
        console.log(`[updevice_harmony] DeviceResourceHandler ---> search device[${checkDevice.deviceId}] local resource.`)
        this.resourceInfo = this.getResourceInfo(checkDevice);
        console.log("[updevice_harmony] DeviceResourceHandler resourceInfo---> ", JSON.stringify(this.resourceInfo));
      }
      if (this.next) {
        this.next.targetDevice = this.targetDevice;
        this.next.relativeDevice = this.relativeDevice;
        this.next.resourceInfo = this.resourceInfo;
      }
      const nextFlag = this.next?.needHandleAsync(page) ?? false;
      return !this.resourceInfo || nextFlag;
    } catch (err) {
      console.error(`[updevice_harmony] DeviceResourceHandler searchDeviceResourceList or getLatestInstalledResource error:${err}`);
      return false;
    }

  }

  handle(url: string, result: PrepareResult): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        if (this.next) {
          this.next.targetDevice = this.targetDevice;
          this.next.relativeDevice = this.relativeDevice;
        }
        const modelCheckType = getModelCheckType(url);
        const checkDevice = this.getDeviceByModelCheckType(modelCheckType);
        //本地设备资源包未找到
        if (checkDevice && !this.resourceInfo) {
          console.log(`[updevice_harmony] DeviceResourceHandler:device resource not found ---> search device[${checkDevice.deviceId}] remote resource.`)
          this.resourceInfo = await this.getResourceInfoAsync(checkDevice);
        }
        const resourceName = this.resourceInfo?.name;
        if (!this.resourceInfo || !resourceName) {
          reject(RESOURCE_ERROR);
          return;
        }

        const redirectUrl = this.createUrlByOriginUrl(url);
        if (!redirectUrl) {
          reject(RESOURCE_ERROR);
          console.log(`[updevice_harmony] DeviceResourceHandler ---> redirectUrl createError.`)
          return;
        }
        result.errorType = ERROR_DEVICE_PREPARE_OPENRES;
        result.resInfoName = resourceName;
        result.tarUrl = await this.bas64EncodeUrl(redirectUrl);
        console.log("[updevice_harmony] DeviceResourceHandler ---> handleDeviceResult:", JSON.stringify(result));
        await this.next?.handle(url, result);
        resolve();
      } catch (err) {
        reject(err);
      }
    });
  }

  private getResourceInfo(device: UpDevice | undefined): ResourceInfo | undefined {
    if (!device) {
      return;
    }
    const deviceInfo = device.deviceInfo;
    const model = deviceInfo.deviceBaseInfo.model;
    const typeId = deviceInfo.deviceBaseInfo.typeId;
    const productCode = deviceInfo.deviceBaseInfo.productCode;
    const typeCode = deviceInfo.deviceBaseInfo.typeCode;
    const deviceNetType = deviceInfo.deviceBasic.deviceNetType;
    const resourceInfos = ResourceManager.getInstance().getResource().searchDeviceResourceList(
      ResourceType.MPaaS,
      model,
      typeId,
      productCode,
      typeCode,
      deviceNetType,
      "",//pid暂不支持，可为空字符串
    );
    if (!resourceInfos || resourceInfos.length < 1) {
      return;
    }
    const resourceInfo = resourceInfos[0];
    const latestInstalledResource = ResourceManager.getInstance()
      .getResource()
      .getLatestInstalledResource(resourceInfo.name, ResourceType.MPaaS);
    if (latestInstalledResource) {
      return latestInstalledResource;
    } else {
      return;
    }
  }

  private getResourceInfoAsync(device: UpDevice): Promise<ResourceInfo> {
    return new Promise(async (resolve, reject) => {
      const deviceInfo = device.deviceInfo;
      const model = deviceInfo.deviceBaseInfo.model;
      const typeId = deviceInfo.deviceBaseInfo.typeId;
      const productCode = deviceInfo.deviceBaseInfo.productCode;
      const typeCode = deviceInfo.deviceBaseInfo.typeCode;
      const deviceNetType = deviceInfo.deviceBasic.deviceNetType;
      try {
        const result = await ResourceManager.getInstance().getResource().requestDeviceResourceList(
          ResourceType.MPaaS,
          model,
          typeId,
          productCode,
          typeCode,
          deviceNetType,
          "",//pid暂不支持，可为空字符串
        );
        const resourceInfos = result.value;
        if (result.isSuccess() && resourceInfos && resourceInfos.length > 0) {
          resolve(resourceInfos[0]);
        } else {
          console.error(`DeviceResourceHandler requestDeviceResourceList error:${JSON.stringify(result)}`);
          reject(RESOURCE_ERROR);
        }
      } catch (err) {
        console.error(`DeviceResourceHandler requestDeviceResourceList error:${JSON.stringify(err)}`);
        reject(RESOURCE_ERROR);
      }
    });
  }
}