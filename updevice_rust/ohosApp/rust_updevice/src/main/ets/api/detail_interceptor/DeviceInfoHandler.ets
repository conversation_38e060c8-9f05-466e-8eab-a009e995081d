import { PrepareResult } from '@uplus/uppluginfoundation';
import { UpDevice } from '../device/UpDevice';
import { UpDeviceManager } from '../UpDeviceManager';
import { DeviceHandler } from './DeviceHandler';
import { getBindSuccessFlag, getDeviceId } from './Utils';
import { Page } from '@uplus/upvdn';

export const INFO_ERROR: string = "设备信息缺失，请稍后重试";

export class DeviceInfoHandler extends DeviceHandler {
  needHandleAsync(page: Page): boolean {
    console.log(`[updevice_harmony] DeviceInfoHandler start. path:${page.getOriginUrl()}`);
    const deviceId = getDeviceId(page.getOriginUrl());
    if (!deviceId || deviceId.length <= 0) {
      return false;
    }
    try {
      if (!this.targetDevice) {
        console.log(`[updevice_harmony] DeviceInfoHandler ---> getDevice[${deviceId}].`)
        //根据deviceId获取UpDevice对象
        const originDevice = this.getDevice(deviceId);
        if (!originDevice) {
          return false;
        }
        // 初始化设备信息
        this.initializeDevices(originDevice);
      }

      const nextFlag = this.next?.needHandleAsync(page) ?? false;
      // 是否需要更新设备列表
      const deviceFlag = this.isNeedGetDeviceAsync(this.targetDevice);
      return deviceFlag || nextFlag;
    } catch (err) {
      console.error(`[updevice_harmony] DeviceInfoHandler getDeviceById error:${err}`);
      return false;
    }
  }

  // 需要将哪些数据带入到下一个页面
  private initializeDevices(device: UpDevice): void {
    const tempDevice = this.getParentDevice(device);
    this.targetDevice = tempDevice || device;
    this.relativeDevice = tempDevice ? device : undefined;
    console.log("[updevice_harmony] DeviceInfoHandler initializeDevices -> targetDevice:", JSON.stringify(this.targetDevice));
    if (this.relativeDevice) {
      console.log("[updevice_harmony] DeviceInfoHandler initializeDevices -> relativeDevice:", JSON.stringify(this.relativeDevice));
    }
    if (this.next) {
      this.next.targetDevice = this.targetDevice;
      this.next.relativeDevice = this.relativeDevice;
    }
  }

  handle(url: string, result: PrepareResult): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const bindSuccess = getBindSuccessFlag(url);
        const deviceId = getDeviceId(url);
        if (!deviceId) {
          reject(INFO_ERROR);
          return;
        }
        let originDevice = this.getDevice(deviceId);
        if (bindSuccess || !originDevice || originDevice.deviceInfo.deviceBaseInfo.model.length === 0) {
          console.log(`[updevice_harmony] DeviceInfoHandler device[${deviceId}] not found. ---> getDeviceAsync.`);
          originDevice = await this.getDeviceAsync(deviceId);
        }
        const deviceInfo = originDevice.deviceInfo;
        if (deviceInfo.deviceBaseInfo.model.length === 0) {
          reject(INFO_ERROR);
          return;
        }
        // 初始化设备信息
        this.initializeDevices(originDevice);
        await this.next?.handle(url, result);
        resolve();
      } catch (err) {
        reject(err);
      }
    });
  }

  // 获取父类设备
  private getParentDevice(device: UpDevice) : UpDevice|undefined {
    if (!device?.deviceInfo?.deviceBaseInfo?.parentId || !device?.deviceInfo?.deviceBasic?.deviceRole) {
      return undefined;
    }
    const parentId = device.deviceInfo.deviceBaseInfo.parentId;
    const deviceRole = device.deviceInfo.deviceBasic.deviceRole;
    if ("3" === deviceRole && parentId) {
      return this.getDevice(parentId);
    }
    return undefined;
  }

  private getDeviceAsync(deviceId: string): Promise<UpDevice> {
    return new Promise(async (resolve, reject) => {
      try {
        const result = await UpDeviceManager.updateDeviceList(false, undefined, false);
        const devices = result?.data;
        if (!devices) {
          console.error(`DeviceInfoHandler updateDeviceList error:empty data.`);
          reject(INFO_ERROR);
          return;
        }
        const newDevice = devices.find((val) => val.deviceId == deviceId);
        if (!newDevice) {
          console.error(`DeviceInfoHandler getDeviceAsync error:device[${deviceId}] not found.`);
          reject(INFO_ERROR);
          return;
        }
        resolve(newDevice);
      } catch (error) {
        console.error(`DeviceInfoHandler updateDeviceList error:${JSON.stringify(error)}`);
        reject(INFO_ERROR);
      }
    });
  }

  private isNeedGetDeviceAsync(device?: UpDevice): boolean {
    return !device || !device.deviceInfo || device.deviceInfo.deviceBaseInfo.model.length === 0;
  }
}