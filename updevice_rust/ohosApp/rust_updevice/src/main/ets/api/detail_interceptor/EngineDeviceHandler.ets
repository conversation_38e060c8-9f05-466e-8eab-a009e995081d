import { UpDevice } from '../device/UpDevice';
import { UpDeviceAttribute } from '../models/UpDeviceAttribute';
import { UpDeviceConfigState } from '../models/UpDeviceConfigState';
import { UpDeviceManager } from '../UpDeviceManager';
import { DeviceHandler } from './DeviceHandler';
import { UpDeviceCaution } from '../models/UpDeviceCaution';
import { UpDeviceOnlineState } from '../models/UpDeviceOnlineState';
import { UpDeviceConnectState } from '../models/UpDeviceConnectState';
import { UpDeviceInfo } from '../models/UpDeviceInfo';
import { UpDeviceSleepState } from '../models/UpDeviceSleepState';
import { UpDeviceOfflineCause } from '../models/UpDeviceOfflineCause';
import { ERROR_DEVICE_PREPARE_GOTOPAGE, PrepareResult } from '@uplus/uppluginfoundation';
import { getModelCheckType } from './Utils';
import { Page } from '@uplus/upvdn';

const TIMEOUT: number = 15000; //毫秒
const NON_NET_DEVICE: string = "nonNetDevice"; //毫秒
type UpDeviceReporter = (device: UpDevice | undefined) => void;

export class EngineDeviceHandler extends DeviceHandler {
  private subscriptionId?: string;

  needHandleAsync(page: Page): boolean {
    console.log(`[updevice_harmony] EngineDeviceHandler start. path:${page.getOriginUrl()}`);
    const checkDevice = this.initializeModelCheckDevice(page.getOriginUrl());
    const nextFlag = this.next?.needHandleAsync(page) ?? false;
    // 非网器直接查资源包
    const deviceNetType = checkDevice?.deviceInfo.deviceBasic.deviceNetType;
    if (deviceNetType && deviceNetType === NON_NET_DEVICE) {
      console.log(`[updevice_harmony] EngineDeviceHandler deviceId:${checkDevice?.deviceId} deviceNetType:${deviceNetType}`);
      return nextFlag;
    }

    // 设备不支持直接去不支持的页面
    const state = checkDevice?.getConfigState();
    console.log(`[updevice_harmony] EngineDeviceHandler deviceId:${checkDevice?.deviceId} state:${state}`);

    if (state && state === UpDeviceConfigState.NotSupport) {
      return false;
    }
    // 设备未开始准备，将开启准备流程
    return (state === UpDeviceConfigState.Unknown) || nextFlag;
  }

  handle(url: string, result: PrepareResult): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const checkDevice = this.initializeModelCheckDevice(getModelCheckType(url));
        if (!checkDevice) {
          reject("设备未准备好，请稍后重试");
          return;
        }
        // 非网器直接查资源包
        const deviceNetType = checkDevice.deviceInfo.deviceBasic.deviceNetType;
        console.log("[updevice_harmony] EngineDeviceHandler deviceNetType：", deviceNetType);
        if (deviceNetType === NON_NET_DEVICE) {
          await this.next?.handle(url, result);
          resolve();
          return;
        }

        // targetDevice 准备流程已经执行完毕
        const state = checkDevice.getConfigState();
        if (state !== UpDeviceConfigState.Unknown) {
          await this.next?.handle(url, result);
          resolve();
          return;
        }

        // targetDevice 等待准备
        const deviceId = this.targetDevice?.deviceId;
        if (!deviceId) {
          reject("设备未准备好，请稍后重试");
          return;
        }
        UpDeviceManager.priorityPrepareDevice(deviceId);
        console.info(`[updevice_harmony] EngineDeviceHandler:device not prepared ---> device[${deviceId}] add front to prepare.`);
        let refreshState = await this.listenDeviceState(deviceId);
        if (refreshState === UpDeviceConfigState.Unknown) {
          reject("设备未准备好，请稍后重试");
          return;
        }
        if (refreshState === UpDeviceConfigState.NotSupport) {
          result.errorType = ERROR_DEVICE_PREPARE_GOTOPAGE;
          result.tarUrl = await this.bas64EncodeUrl(this.createUnSupportUrl(deviceId));
          resolve();
          return;
        }
        //逻辑引擎已准备好，交给下一个责任链
        await this.next?.handle(url, result);
        resolve();
      } catch (err) {
        reject(err);
      }
    });
  }

  /**
   * 监听设备准备状态，
   * @param deviceId
   * @returns
   */
  private listenDeviceState(deviceId: string): Promise<UpDeviceConfigState> {
    return new Promise((resolve, _reject) => {
      const timeoutId = setTimeout(() => {
        //处理超时
        this.unsubscribeDevice();
        resolve(UpDeviceConfigState.Unknown);
      }, TIMEOUT);
      this.subscribeDevice(deviceId, (device) => {
        const state = device?.configState;
        if (state && (state === UpDeviceConfigState.NotSupport || state === UpDeviceConfigState.Support)) {
          //准备完成
          clearTimeout(timeoutId);
          this.unsubscribeDevice();
          resolve(state);
        }
      });
    });
  }

  private subscribeDevice(deviceId: string, listener: UpDeviceReporter) {
    const result = UpDeviceManager.subscribeDeviceChange(deviceId, {
      onAttributeChanged: (deviceId: string, _attributes: Array<UpDeviceAttribute>) => {
        listener(this.getDevice(deviceId));
      },
      onCautionChanged: (deviceId: string, _cautions: Array<UpDeviceCaution>) => {
        listener(this.getDevice(deviceId));
      },
      onOnlineStateChanged: (deviceId: string, _onlineState: UpDeviceOnlineState) => {
        listener(this.getDevice(deviceId));
      },
      onConnectStateChanged: (deviceId: string, _connectState: UpDeviceConnectState) => {
        listener(this.getDevice(deviceId));
      },
      onBaseInfoChanged: (deviceId: string, _info: UpDeviceInfo) => {
        listener(this.getDevice(deviceId));
      },
      onSleepStateChanged: (deviceId: string, _state: UpDeviceSleepState) => {
        listener(this.getDevice(deviceId));
      },
      onOfflineCauseChanged: (deviceId: string, _cause: UpDeviceOfflineCause) => {
        listener(this.getDevice(deviceId));
      },
      onOfflineDaysChanged: (deviceId: string, _days: number) => {
        listener(this.getDevice(deviceId));
      }
    });
    const data = result.data;
    if (result.isSuccess() && data) {
      this.subscriptionId = data;
    }
  }

  private unsubscribeDevice() {
    const subscriptionId = this.subscriptionId;
    if (subscriptionId) {
      UpDeviceManager.unsubscribeDeviceChange(subscriptionId);
    }
  }
}