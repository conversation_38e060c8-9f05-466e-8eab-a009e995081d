import { <PERSON><PERSON><PERSON>, <PERSON>, PageUri } from '@uplus/upvdn';
import { PageStage } from '@uplus/upvdn/src/main/ets/navigator/PageStage';
import { DeviceHandler } from './DeviceHandler';
import { UpDeviceConfigState } from '../models/UpDeviceConfigState';
import { getBindSuccessFlag, getDeviceId, getModelCheckType, PARAM_DEVICE_ID, PARAM_IS_BINDING_SUCCESS, MODEL_CHECK_KEY } from './Utils';

const TARGET_HTTPS_URL: string = "https://uplus.haier.com/uplusapp/DeviceList/DetailView.html";
const TARGET_HTTP_URL: string = "http://uplus.haier.com/uplusapp/DeviceList/DetailView.html";

// const URL_SOURCE_KEY: string = "up_url_source"; //快捷方式

export class DeviceDetailPatch implements LogicPatch {
  private deviceHandler: DeviceHandler;

  constructor(deviceHandler: <PERSON><PERSON><PERSON>and<PERSON>) {
    this.deviceHandler = deviceHandler;
  }

  getName(): string {
    return "DeviceDetailPatch";
  }

  getPriority(): number {
    return 1;
  }

  isNeedPatch(page: Page): boolean {
    const originUrl = page.getPageUri().toString();
    console.info("[updevice_harmony] originUrl:" + originUrl);
    return originUrl.startsWith(TARGET_HTTPS_URL) || originUrl.startsWith(TARGET_HTTP_URL);
  }

  patch(page: Page): boolean {
    this.deviceHandler.clearCache();
    const deviceId = getDeviceId(page.getOriginUrl());
    if (!deviceId || deviceId.length <= 0) {
      return true;
    }
    let newPageUrl: string | undefined;
    const isBindSuccess = getBindSuccessFlag(page.getOriginUrl());
    const modelCheckType = getModelCheckType(page.getOriginUrl());
    const isJumpLoadingUi = this.deviceHandler.needHandleAsync(page);
    if (isBindSuccess || isJumpLoadingUi) { //绑定成功跳转强制刷新设备列表
      //耗时跳转下载UI
      newPageUrl =
        `hainer://resoureprepare?isDevice=true&${PARAM_DEVICE_ID}=${deviceId}&${MODEL_CHECK_KEY}=${modelCheckType}&${PARAM_IS_BINDING_SUCCESS}=${isBindSuccess ?
          "1" : "0"}`;
    } else {
      //非耗时处理
      let configState = this.deviceHandler.targetDevice?.configState;
      if (configState && configState === UpDeviceConfigState.NotSupport) {
        //跳转设备不支持详情页
        newPageUrl = this.deviceHandler.createUnSupportUrl(deviceId);
      } else if (this.deviceHandler.targetDevice && this.deviceHandler.isSpecialDevice(this.deviceHandler.targetDevice)) {
        //跳转安防设备详情页
        newPageUrl = this.deviceHandler.createSpecialDeviceDetailUrl(this.deviceHandler.targetDevice);
      } else {
        //直接跳转设备详情页
        newPageUrl = this.deviceHandler.createUrlByPage(page);
      }
    }
    console.info("[updevice_harmony] newUrl:" + newPageUrl);
    if (newPageUrl) {
      let newPage = PageUri.create(newPageUrl);
      page.getPageUri().set(newPage);
      page.moveToStage(PageStage.VDNS);
    } else {
      console.error("[updevice_harmony] createDeviceDetailUrl error: updevice or resourceInfo not found");
    }
    return true;
  }

  removeTrigger(_page: Page): void {
  }
}