import { PrepareResult } from "@uplus/uppluginfoundation";
import { Page } from "@uplus/upvdn";
import { UpDevice } from "../device/UpDevice";
import { UpDeviceAttribute } from "../models/UpDeviceAttribute";
import { UpDeviceCaution } from "../models/UpDeviceCaution";
import { UpDeviceConfigState } from "../models/UpDeviceConfigState";
import { UpDeviceConnectState } from "../models/UpDeviceConnectState";
import { UpDeviceInfo } from "../models/UpDeviceInfo";
import { UpDeviceOfflineCause } from "../models/UpDeviceOfflineCause";
import { UpDeviceOnlineState } from "../models/UpDeviceOnlineState";
import { UpDeviceSleepState } from "../models/UpDeviceSleepState";
import { UpDeviceManager } from "../UpDeviceManager";
import { DeviceHandler } from "./DeviceHandler";
import { HashMap } from "@kit.ArkTS";

type UpDeviceReporter = (device: UpDevice | undefined) => void;
const TIMEOUT: number = 15000; //毫秒

export class RelativeDevicePrepareHandler extends DeviceHandler {
  // 所有相关设备的订阅ID
  // <deviceId: subscriptionId>
  private subscriptionIds: HashMap<string, string> = new HashMap();

  needHandleAsync(page: Page): boolean {
    console.log(`[updevice_harmony] RelativeDevicePrepareHandler start. path:${page.getOriginUrl()}`);
    if (this.next) {
      this.next.targetDevice = this.targetDevice;
      this.next.relativeDevice = this.relativeDevice;
    }
    const nextFlag = this.next?.needHandleAsync(page) ?? false;
    // targetDevice下有设备没有准备完毕，进入耗时逻辑
    return !this.isAllRelativeDevicesReady() || nextFlag;
  }

  // 所有相关的设备是否都准备完毕
  // return true：都准备完毕
  private isAllRelativeDevicesReady():boolean {
    let relativeDevices:UpDevice[] = this.getRelativeDevices();
    let allRelativeDevicesReady = 0;
    // 遍历 relativeDevices
    for (let i = 0;i<relativeDevices.length;i++) {
      const device = relativeDevices[i];
      if (device.getConfigState() !== UpDeviceConfigState.Unknown) {
        allRelativeDevicesReady ++;
      }
    }
    return allRelativeDevicesReady === relativeDevices.length;
  }

  private getRelativeDevices(): UpDevice[] {
    const parentDeviceId = this.targetDevice?.getDeviceId();
    if (!parentDeviceId) {
      return [];
    }
    return UpDeviceManager.getSubDeviceListByParentId(parentDeviceId);
  }

  handle(url: string, result: PrepareResult): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {

        if (this.next) {
          this.next.targetDevice = this.targetDevice;
          this.next.relativeDevice = this.relativeDevice;
        }

        const relativeDevices = this.getRelativeDevices();
        // 获取所有[没有准备]的设备ID
        const unPrepareDevices = relativeDevices.filter((device)=>{
          return device.getConfigState() === UpDeviceConfigState.Unknown;
        });

        if (unPrepareDevices.length == 0) {
          // 所有相关的设备都已经完成准备，进入下一个流程；
          await this.next?.handle(url, result);
          resolve();
          return;
        }
        // 监听所有设备的准备状态
        const isAllRelativeDevicesReady = await this.listenRelativeDevicesPrepareState(unPrepareDevices);

        // 解除所有设备的订阅
        this.unsubscribeAllRelativeDevices();
        // 超过设定的时间未准备完成
        if (!isAllRelativeDevicesReady) {
          reject("设备未准备好，请稍后重试");
          return;
        }

        await this.next?.handle(url, result);
        resolve();
        return;
      } catch (err) {
        reject(err);
      }
    });
  }

  /**
   * 监听设备准备状态，
   * @param deviceId
   * @returns
   */
  private listenRelativeDevicesPrepareState(devices: UpDevice[]): Promise<boolean> {
    return new Promise((resolve, _reject) => {
      const timeoutId = setTimeout(() => {
        resolve(false);
      }, TIMEOUT);

      for (let i = 0;i<devices.length;i++) {
        const deviceId = devices[i]?.deviceId;
        if (!deviceId) {
          continue;
        }
        // 优先准备
        UpDeviceManager.priorityPrepareDevice(deviceId);
        console.info(`[updevice_harmony] RelativeDevicePrepareHandler:device not prepared ---> device[${deviceId}] add front to prepare.`);

        this.subscribeDevice(deviceId, (completeDevice) => {
          const state = completeDevice?.configState;
          if (state && (state === UpDeviceConfigState.NotSupport || state === UpDeviceConfigState.Support)) {
            // 有设备准备完成
            if (completeDevice) {
              this.unsubscribeDevice(completeDevice.deviceId);
            }
            // 如果收到所有订阅设备的回调，则继续跳转页面
            if (this.subscriptionIds.isEmpty()) {
              resolve(true);
              clearTimeout(timeoutId);
            }
          }
        });
      }
      console.info(`[updevice_harmony] RelativeDevicePrepareHandler listenRelativeDevicesPrepareState finished.`);
    });
  }

  private subscribeDevice(deviceId: string, listener: UpDeviceReporter) {
    const result = UpDeviceManager.subscribeDeviceChange(deviceId, {
      onAttributeChanged: (deviceId: string, _attributes: Array<UpDeviceAttribute>) => {
        listener(this.getDevice(deviceId));
      },
      onCautionChanged: (deviceId: string, _cautions: Array<UpDeviceCaution>) => {
        listener(this.getDevice(deviceId));
      },
      onOnlineStateChanged: (deviceId: string, _onlineState: UpDeviceOnlineState) => {
        listener(this.getDevice(deviceId));
      },
      onConnectStateChanged: (deviceId: string, _connectState: UpDeviceConnectState) => {
        listener(this.getDevice(deviceId));
      },
      onBaseInfoChanged: (deviceId: string, _info: UpDeviceInfo) => {
        listener(this.getDevice(deviceId));
      },
      onSleepStateChanged: (deviceId: string, _state: UpDeviceSleepState) => {
        listener(this.getDevice(deviceId));
      },
      onOfflineCauseChanged: (deviceId: string, _cause: UpDeviceOfflineCause) => {
        listener(this.getDevice(deviceId));
      },
      onOfflineDaysChanged: (deviceId: string, _days: number) => {
        listener(this.getDevice(deviceId));
      }
    });
    const data = result.data;
    if (result.isSuccess() && data) {
      this.subscriptionIds[deviceId] = data;
      console.log(`[updevice_harmony] RelativeDevicePrepareHandler Add subscription ID: ${deviceId}:${data}}`);
    }
  }

  private unsubscribeAllRelativeDevices(){
    for (let i = 0;i<this.subscriptionIds.length;i++) {
      this.unsubscribeDevice(this.subscriptionIds[i]);
    }
    this.subscriptionIds = new HashMap();
  }

  private unsubscribeDevice(deviceId:string) {
    this.subscriptionIds.remove(deviceId);
    console.log(`[updevice_harmony] RelativeDevicePrepareHandler Removed subscription ID: ${deviceId}`);
  }
}