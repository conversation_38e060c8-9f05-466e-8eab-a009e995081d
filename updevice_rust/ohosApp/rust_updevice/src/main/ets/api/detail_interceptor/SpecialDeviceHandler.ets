import { ERROR_DEVICE_PREPARE_GOTOPAGE, PrepareResult } from '@uplus/uppluginfoundation';
import { <PERSON><PERSON>Handler } from './DeviceHandler';
import { getModelCheckType } from './Utils';
import { Page } from '@uplus/upvdn';

/**
 * 安防设备处理器
 */
export class SpecialDeviceHandler extends DeviceHandler {
  needHandleAsync(page: Page): boolean {
    console.log(`[updevice_harmony] SpecialDeviceHandler start. path:${page.getOriginUrl()}`);
    const checkDevice = this.initializeModelCheckDevice(getModelCheckType(page.getOriginUrl()));
    if (this.isSpecialDevice(checkDevice)) {
      //是安防设备，不再去校验H5资源包是否存在
      console.info(`[updevice_harmony] SpecialDeviceHandler[${checkDevice?.deviceId}] needHandleAsync ---> start.`);
      return false;
    }
    return this.next?.needHandleAsync(page) ?? false;
  }

  handle(url: string, result: PrepareResult): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {

        const checkDevice = this.initializeModelCheckDevice(getModelCheckType(url));
        if (!checkDevice) {
          reject("设备未准备好，请稍后重试");
          return;
        }

        if (!this.isSpecialDevice(checkDevice)) {
          await this.next?.handle(url, result);
          resolve();
          return;
        }
        //是安防设备，不再去下载安装H5资源包
        const tarUrl = this.createSpecialDeviceDetailUrl(checkDevice);
        if (!tarUrl) {
          console.error(`[updevice_harmony] SpecialDeviceHandler[${checkDevice?.deviceId}] tarUrl is null.`);
          reject("加载失败，请稍后重试");
          return;
        }
        console.info(`[updevice_harmony] SpecialDeviceHandler[${checkDevice?.deviceId}] ---> handle url:${tarUrl}`);
        result.errorType = ERROR_DEVICE_PREPARE_GOTOPAGE;
        result.tarUrl = await this.bas64EncodeUrl(tarUrl);
        resolve();
      } catch (err) {
        reject(err);
      }
    });
  }
}