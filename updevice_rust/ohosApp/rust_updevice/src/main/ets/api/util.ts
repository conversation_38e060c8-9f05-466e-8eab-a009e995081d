import { ByteBuffer } from '@ohos/flatbuffers';
import { UpDeviceFlat } from '../com/haier/uhome/uplus/rust/updevice/fbs';

export function isValidEnumValue<T extends object>(value: number, enumObject: T): boolean {
  if (value) {
    const values = Object.values(enumObject);
    return values.includes(value);
  } else {
    return false;
  }
}

export enum RustConstant {
  Action = "action",
  UniqueId = "listener_id",
  LibName = "lib_updevice",
  Call_Failure = "rust method call failure",
}

export enum UpDeviceCode {
  Success = "000000",
  IllegalParameters = "900003",
  RustCallFailure = "999999",
  NoCode = "999998"
}

export enum ErrorMessage {
  RustCallFailure = "buf is null",
  NoCode = "no flat code",
  NoError = "no flat error",
}

export class UpDeviceRustResult<T> {
  code: string;
  error: string;
  data?: T;

  constructor(code: string, error: string, data?: T) {
    this.code = code;
    this.error = error;
    this.data = data;
  }

  isSuccess(): boolean {
    return this.code === UpDeviceCode.Success;
  }
}

export function handleRustFbsWrapperBuffer<T>(
  buf: ArrayBuffer | null,
  containerConstructor: new () => T,
): UpDeviceRustResult<T> {
  if (buf) {
    const flat = UpDeviceFlat.getRootAsUpDeviceFlat(new ByteBuffer(new Uint8Array(buf)));
    const code = flat.code() ?? UpDeviceCode.NoCode;
    const error = flat.error() ?? ErrorMessage.NoError;
    if (UpDeviceCode.Success == code) {
      const container = flat.container(new containerConstructor()) as T | null;
      return new UpDeviceRustResult(code, error, container);
    } else {
      return new UpDeviceRustResult(code, error);
    }
  } else {
    return new UpDeviceRustResult(UpDeviceCode.RustCallFailure, ErrorMessage.RustCallFailure);
  }
}

export function handleRustWrapperBuffer<W extends { value(): T }, T>(
  buf: ArrayBuffer | null,
  wrapperConstructor: new () => W,
): UpDeviceRustResult<T> {
  if (buf) {
    const flat = UpDeviceFlat.getRootAsUpDeviceFlat(new ByteBuffer(new Uint8Array(buf)));
    const code = flat.code() ?? UpDeviceCode.NoCode;
    const error = flat.error() ?? ErrorMessage.NoError;
    if (UpDeviceCode.Success == code) {
      const container = flat.container(new wrapperConstructor()) as W | null;
      return new UpDeviceRustResult(code, error, container?.value());
    } else {
      return new UpDeviceRustResult(code, error);
    }
  } else {
    return new UpDeviceRustResult(UpDeviceCode.RustCallFailure, ErrorMessage.RustCallFailure);
  }
}

/**
 * NAPI 很多参数使用的是 class 对象，这里把对象转为 map
 * 复杂类型（对象、数组、函数等）均不加入
 */
export function argsToMap(obj: object): Map<string, string> {
  const map = new Map<string, string>();
  for (const [key, value] of Object.entries(obj)) {
    // 排除字段值为 undefined 的情况
    if (value === undefined) {
      continue;
    }
    const t = typeof value;
    // 仅处理 string, number, boolean 类型，并将其转换为字符串加入到 Map 中
    if (t === 'string' || t === 'number' || t === 'boolean') {
      map.set(key, String(value));
    }
    // 其他复杂类型（对象、数组、函数等）均不加入
  }
  return map;
}
