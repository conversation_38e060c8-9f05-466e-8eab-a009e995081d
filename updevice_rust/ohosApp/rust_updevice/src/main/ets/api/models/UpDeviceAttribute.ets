export class UpDeviceAttribute {
  name: string;
  value: string;

  constructor(name: string, value: string) {
    this.name = name;
    this.value = value;
  }

  getName(): string {
    return this.name;
  }

  getValue(): string {
    return this.value;
  }

  static fromObject(obj: object): UpDeviceAttribute {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceAttribute(
      values[0] as string,
      values[1] as string
    );
  }
}