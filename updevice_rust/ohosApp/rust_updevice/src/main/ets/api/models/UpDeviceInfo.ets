import { ArrayList, HashMap } from '@kit.ArkTS';
import { Type } from 'class-transformer';

export class UpDeviceBaseInfo {
  protocol: string;
  deviceId: string;
  typeId: string;
  typeCode: string;
  model: string;
  productCode: string;
  parentId: string;

  constructor(
    protocol: string,
    deviceId: string,
    typeId: string,
    typeCode: string,
    model: string,
    productCode: string,
    parentId: string,
  ) {
    this.protocol = protocol;
    this.deviceId = deviceId;
    this.typeId = typeId;
    this.typeCode = typeCode;
    this.model = model;
    this.productCode = productCode;
    this.parentId = parentId;
  }

  getProtocol(): string {
    return this.protocol;
  }

  getDeviceId(): string {
    return this.deviceId;
  }

  getTypeId(): string {
    return this.typeId;
  }

  getTypeCode(): string {
    return this.typeCode;
  }

  getModel(): string {
    return this.model;
  }

  getProductCode(): string {
    return this.productCode;
  }

  getParentId(): string {
    return this.parentId;
  }

  static fromObject(obj: object): UpDeviceBaseInfo {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceBaseInfo(
      values[0] as string,
      values[1] as string,
      values[2] as string,
      values[3] as string,
      values[4] as string,
      values[5] as string,
      values[6] as string
    );
  }
}

export class UpDeviceBasic {
  displayName: string;
  appTypeName: string;
  appTypeCode: string;
  roomName: string; // -> relation?
  roomId: string; // -> relation?
  online: boolean;
  subDeviceIds: ArrayList<string>;
  deviceRole: string | null;
  deviceRoleType: string | null;
  deviceNetType: string | null;
  deviceGroupId: string | null;
  deviceGroupType: string | null;
  bindTime: string;
  deviceAggregateType: string;
  attachmentSortCode: number;
  constructor(
    displayName: string,
    appTypeName: string,
    appTypeCode: string,
    roomName: string,
    roomId: string,
    online: boolean,
    subDeviceIds: ArrayList<string>,
    deviceRole: string | null,
    deviceRoleType: string | null,
    deviceNetType: string | null,
    deviceGroupId: string | null,
    deviceGroupType: string | null,
    bindTime: string,
    deviceAggregateType: string,
    attachmentSortCode: number
  ) {
    this.displayName = displayName;
    this.appTypeName = appTypeName;
    this.appTypeCode = appTypeCode;
    this.roomName = roomName;
    this.roomId = roomId;
    this.online = online;
    this.subDeviceIds = subDeviceIds;
    this.deviceRole = deviceRole;
    this.deviceRoleType = deviceRoleType;
    this.deviceNetType = deviceNetType;
    this.deviceGroupId = deviceGroupId;
    this.deviceGroupType = deviceGroupType;
    this.bindTime = bindTime;
    this.deviceAggregateType = deviceAggregateType;
    this.attachmentSortCode = attachmentSortCode;
  }

  getDisplayName(): string {
    return this.displayName;
  }

  getAppTypeName(): string {
    return this.appTypeName;
  }

  getAppTypeCode(): string {
    return this.appTypeCode;
  }

  getRoomName(): string {
    return this.roomName;
  }

  getRoomId(): string {
    return this.roomId;
  }

  getOnline(): boolean {
    return this.online;
  }

  getSubDeviceIds(): ArrayList<string> {
    return this.subDeviceIds;
  }

  getDeviceRole(): string | null {
    return this.deviceRole;
  }

  getDeviceRoleType(): string | null {
    return this.deviceRoleType;
  }

  getDeviceNetType(): string | null {
    return this.deviceNetType;
  }

  getDeviceGroupId(): string | null {
    return this.deviceGroupId;
  }

  getDeviceGroupType(): string | null {
    return this.deviceGroupType;
  }

  getBindTime(): string {
    return this.bindTime;
  }

  getDeviceAggregateType(): string {
    return this.deviceAggregateType;
  }

  getAttachmentSortCode(): number {
    return this.attachmentSortCode;
  }


  static fromObject(obj: object): UpDeviceBasic {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceBasic(
      values[0] as string,
      values[1] as string,
      values[2] as string,
      values[3] as string,
      values[4] as string,
      values[5] as boolean,
      values[6] as ArrayList<string>,
      values[7] as string | null,
      values[8] as string | null,
      values[9] as string | null,
      values[10] as string |null,
      values[11] as string | null,
      values[12] as string,
      values[13] as string,
      values[14] as number
    );
  }
}

export class UpDevicePermission {
  authType: string;
  isControllable: boolean;
  isEditable: boolean;
  isViewable: boolean;

  constructor(
    authType: string,
    isControllable: boolean,
    isEditable: boolean,
    isViewable: boolean,
  ) {
    this.authType = authType;
    this.isControllable = isControllable;
    this.isEditable = isEditable;
    this.isViewable = isViewable;
  }

  getAuthType(): string {
    return this.authType;
  }

  getIsControllable(): boolean {
    return this.isControllable;
  }

  getIsEditable(): boolean {
    return this.isEditable;
  }

  getIsViewable(): boolean {
    return this.isViewable;
  }

  static fromObject(obj: object): UpDevicePermission {
    const values: Array<Object> = Object.values(obj);
    return new UpDevicePermission(
      values[0] as string,
      values[1] as boolean,
      values[2] as boolean,
      values[3] as boolean
    );
  }
}

export class UpDeviceShareCardInfo {
  familyId: string;
  cardSort: number;
  cardStatus: number;
  constructor(familyId:string, cardSort:number, cardStatus:number) {
    this.familyId = familyId;
    this.cardSort = cardSort;
    this.cardStatus = cardStatus;
  }

  static fromObject(obj: object): UpDeviceShareCardInfo {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceShareCardInfo(
      values[0] as string,
      values[1] as number,
      values[2] as number,
    );
  }
}

export class UpDeviceProduct {
  barCode: string | null;
  brand: string;
  category: string;
  categoryCode: string;
  categoryGrouping: string;
  twoGroupingName: string;
  deviceType: string;
  imageUrl: string;
  bindType: string;
  accessType: string;
  communicationMode: string;
  configType: string;
  appTypeIcon: string;
  noKeepAlive: boolean;
  cardPageIcon: string;
  cardPageImg: string;
  smallCardSort: number;
  largeCardSort: number;
  cardSort:number;
  cardStatus:number;
  aggregationParentId:string;
  supportAggregationFlag:string;
  isSupportShare:boolean;
  cardInfoMap:HashMap<string, UpDeviceShareCardInfo>;
  isShareDevice:boolean;
  rebind:number;
  constructor(
    barCode: string | null,
    brand: string,
    category: string,
    categoryCode: string,
    categoryGrouping: string,
    twoGroupingName: string,
    deviceType: string,
    imageUrl: string,
    bindType: string,
    accessType: string,
    communicationMode: string,
    configType: string,
    appTypeIcon: string,
    noKeepAlive: boolean,
    cardPageIcon: string,
    cardPageImg: string,
    smallCardSort: number,
    largeCardSort: number,
    cardSort: number,
    cardStatus:number,
    aggregationParentId: string,
    supportAggregationFlag: string,
    isShareDevice:boolean,
    cardInfoMap:HashMap<string, UpDeviceShareCardInfo>,
    isSupportShare:boolean,
    rebind:number,
  ) {
    this.barCode = barCode;
    this.brand = brand;
    this.category = category;
    this.categoryCode = categoryCode;
    this.categoryGrouping = categoryGrouping;
    this.twoGroupingName = twoGroupingName;
    this.deviceType = deviceType;
    this.imageUrl = imageUrl;
    this.bindType = bindType;
    this.accessType = accessType;
    this.communicationMode = communicationMode;
    this.configType = configType;
    this.appTypeIcon = appTypeIcon;
    this.noKeepAlive = noKeepAlive;
    this.cardPageIcon = cardPageIcon;
    this.cardPageImg = cardPageImg;
    this.smallCardSort = smallCardSort;
    this.largeCardSort = largeCardSort;
    this.cardSort = cardSort;
    this.cardStatus = cardStatus;
    this.aggregationParentId = aggregationParentId;
    this.supportAggregationFlag = supportAggregationFlag;
    this.isShareDevice = isShareDevice;
    this.cardInfoMap = cardInfoMap;
    this.isSupportShare = isSupportShare;
    this.rebind = rebind;
  }

  getBarCode(): string | null {
    return this.barCode;
  }

  getBrand(): string {
    return this.brand;
  }

  getCategory(): string {
    return this.category;
  }

  getCategoryCode(): string {
    return this.categoryCode;
  }

  getCategoryGrouping(): string {
    return this.categoryGrouping;
  }

  getTwoGroupingName(): string {
    return this.twoGroupingName;
  }

  getDeviceType(): string {
    return this.deviceType;
  }

  getImageUrl(): string {
    return this.imageUrl;
  }

  getBindType(): string {
    return this.bindType;
  }

  getAccessType(): string {
    return this.accessType;
  }

  getCommunicationMode(): string {
    return this.communicationMode;
  }

  getConfigType(): string {
    return this.configType;
  }

  getAppTypeIcon(): string {
    return this.appTypeIcon;
  }

  getNoKeepAlive(): boolean {
    return this.noKeepAlive;
  }

  getCardPageIcon(): string {
    return this.cardPageIcon;
  }

  getCardPageImg(): string {
    return this.cardPageImg;
  }

  getSmallCardSort(): number {
    return this.smallCardSort;
  }

  getLargeCardSort(): number {
    return this.largeCardSort;
  }

  getCardSort(): number {
    return this.cardSort;
  }

  getCardStatus(): number {
    return this.cardStatus;
  }
  getAggregationParentId(): string {
    return this.aggregationParentId;
  }
  getSupportAggregationFlag(): string {
    return this.supportAggregationFlag;
  }
  getIsShareDevice(): boolean {
    return this.isShareDevice;
  }

  getIsSupportShare(): boolean {
    return this.isSupportShare;
  }

  getRebind():boolean {
    return this.rebind == 1;
  }

  static fromObject(obj: object): UpDeviceProduct {
    const values: Array<Object> = Object.values(obj);

    // TODO 外部不使用，flat buffer改造的时候可以不实现
    const cardInfoValues: HashMap<string, object> = values[23] as HashMap<string, object>;
    let cardInfoMap:HashMap<string, UpDeviceShareCardInfo> = new HashMap();
    if (cardInfoValues) {
      cardInfoValues.forEach((value, key) => {
        if (value) {
          const cardInfo = UpDeviceShareCardInfo.fromObject(value);
          if (cardInfo) {
            cardInfoMap.set(key, cardInfo);
          }
        }
      });
    }
    return new UpDeviceProduct(
      values[0] as string | null,
      values[1] as string,
      values[2] as string,
      values[3] as string,
      values[4] as string,
      values[5] as string,
      values[6] as string,
      values[7] as string,
      values[8] as string,
      values[9] as string,
      values[10] as string,
      values[11] as string,
      values[12] as string,
      values[13] as boolean,
      values[14] as string,
      values[15] as string,
      values[16] as number,
      values[17] as number,
      values[18] as number,
      values[19] as number,
      values[20] as string,
      values[21] as string,
      values[22] as boolean,
      cardInfoMap,
      values[24] as boolean,
      values[25] as number
    );
  }
}

export class UpDeviceRelation {
  ownerId: string;
  ownerPhone: string;
  familyId: string;
  ucUserId: string;
  floorId: string;
  floorName: string;
  floorOrderId: string;

  constructor(
    ownerId: string,
    ownerPhone: string,
    familyId: string,
    ucUserId: string,
    floorId: string,
    floorName: string,
    floorOrderId: string
  ) {
    this.ownerId = ownerId;
    this.ownerPhone = ownerPhone;
    this.familyId = familyId;
    this.ucUserId = ucUserId;
    this.floorId = floorId;
    this.floorName = floorName;
    this.floorOrderId = floorOrderId;
  }

  getOwnerId(): string {
    return this.ownerId;
  }

  getOwnerPhone(): string {
    return this.ownerPhone;
  }

  getFamilyId(): string {
    return this.familyId;
  }

  getUcUserId(): string {
    return this.ucUserId;
  }

  getFloorId(): string {
    return this.floorId;
  }

  getFloorName(): string {
    return this.floorName;
  }

  getFloorOrderId(): string {
    return this.floorOrderId;
  }

  static fromObject(obj: object): UpDeviceRelation {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceRelation(
      values[0] as string,
      values[1] as string,
      values[2] as string,
      values[3] as string,
      values[4] as string,
      values[5] as string,
      values[6] as string
    );
  }
}
export class UpDeviceInfo {
  @Type(() => UpDeviceBaseInfo)
  deviceBaseInfo: UpDeviceBaseInfo;
  @Type(() => UpDeviceBasic)
  deviceBasic: UpDeviceBasic;
  @Type(() => UpDevicePermission)
  devicePermission: UpDevicePermission;
  @Type(() => UpDeviceProduct)
  deviceProduct: UpDeviceProduct;
  @Type(() => UpDeviceRelation)
  deviceRelation: UpDeviceRelation;

  static fromObject(obj: object): UpDeviceInfo {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceInfo(
      UpDeviceBaseInfo.fromObject(values[0] as object),
      UpDeviceBasic.fromObject(values[1] as object),
      UpDevicePermission.fromObject(values[2] as object),
      UpDeviceProduct.fromObject(values[3] as object),
      UpDeviceRelation.fromObject(values[4] as object)
    );
  }

  constructor(
    baseInfo: UpDeviceBaseInfo,
    basic: UpDeviceBasic,
    permission: UpDevicePermission,
    product: UpDeviceProduct,
    relation: UpDeviceRelation,
  ) {
    this.deviceBaseInfo = baseInfo;
    this.deviceBasic = basic;
    this.devicePermission = permission;
    this.deviceProduct = product;
    this.deviceRelation = relation;
  }

  getBaseInfo(): UpDeviceBaseInfo {
    return this.deviceBaseInfo;
  }

  getBasic(): UpDeviceBasic {
    return this.deviceBasic;
  }

  getPermission(): UpDevicePermission {
    return this.devicePermission;
  }

  getProduct(): UpDeviceProduct {
    return this.deviceProduct;
  }

  getRelation(): UpDeviceRelation {
    return this.deviceRelation;
  }
}
