export class Caution {
  name: String;
  code?: String;
  time?: String;
  desc?: String;
  clear?: Boolean;

  constructor(name: String, code?: String, time?: String, desc?: String, clear?: Boolean) {
    this.name = name;
    this.code = code;
    this.time = time;
    this.desc = desc;
    this.clear = clear;
  }

  static fromObject(obj: object): Caution {
    const values: Array<Object> = Object.values(obj);
    return new Caution(
      values[0] as string,
      values[1] as string | undefined,
      values[2] as string | undefined,
      values[3] as string | undefined,
      values[4] as boolean | undefined
    );
  }
}