export class UpDeviceCaution {
  name: string;
  value: string;
  time: string;

  constructor(name: string, value: string, time: string) {
    this.name = name;
    this.value = value;
    this.time = time;
  }

  getName(): string {
    return this.name;
  }

  getValue(): string {
    return this.value;
  }

  getTime(): string {
    return this.time;
  }

  static fromObject(obj: object): UpDeviceCaution {
    const values: Array<Object> = Object.values(obj);
    return new UpDeviceCaution(
      values[0] as string,
      values[1] as string,
      values[2] as string
    );
  }
}