import { Type } from 'class-transformer';

export class Attribute {
  name: String;
  value?: String;
  code?: Array<String>;
  desc?: String;
  defaultValue?: String;
  readable: Boolean;
  writable: Boolean;
  invisible: Boolean;
  @Type(() => ValueRange)
  valueRange: ValueRange;
  operationType?: String;

  constructor(name: String, readable: Boolean, writable: Boolean, invisible: Boolean, valueRange: ValueRange,
    value?: String, code?: Array<String>, desc?: String, defaultValue?: String,
    operationType?: String) {
    this.name = name;
    this.value = value;
    this.code = code;
    this.desc = desc;
    this.defaultValue = defaultValue;
    this.readable = readable;
    this.writable = writable;
    this.invisible = invisible;
    this.valueRange = valueRange;
    this.operationType = operationType;
  }

  static fromObject(obj: object): Attribute {
    const values: Array<Object> = Object.values(obj);
    return new Attribute(
      values[0] as string,
      values[5] as boolean,
      values[6] as boolean,
      values[7] as boolean,
      ValueRange.fromObject(values[8] as object),
      values[1] as string | undefined,
      values[2] as Array<string> | undefined,
      values[3] as string | undefined,
      values[4] as string | undefined,
      values[9] as string | undefined
    );
  }
}

export class ValueRange {
  type: String;
  @Type(() => DataList)
  dataList?: Array<DataList>;
  @Type(() => DataStep)
  dataStep?: DataStep;
  @Type(() => DataTime)
  dataTime?: DataTime;
  @Type(() => DataDate)
  dataDate?: DataDate;

  constructor(type: String, dataList?: Array<DataList>, dataStep?: DataStep, dataTime?: DataTime, dataDate?: DataDate) {
    this.type = type;
    this.dataList = dataList;
    this.dataStep = dataStep;
    this.dataTime = dataTime;
    this.dataDate = dataDate;
  }

  static fromObject(obj: object): ValueRange {
    const values: Array<Object> = Object.values(obj);
    return new ValueRange(
      values[0] as string,
      DataList.fromObjectArray(values[1] as Array<object> | undefined),
      DataStep.fromObject(values[2] as object | undefined),
      DataTime.fromObject(values[3] as object | undefined),
      DataDate.fromObject(values[4] as object | undefined)
    );
  }
}

export class DataList {
  data: String;
  code?: String;
  desc?: String;

  constructor(data: String, code?: String, desc?: String) {
    this.data = data;
    this.code = code;
    this.desc = desc;
  }

  static fromObjectArray(arr: Array<object> | undefined): Array<DataList> {
    if (arr == undefined) return [];
    return arr.map((obj) => DataList.fromObject(obj));
  }

  static fromObject(obj: object): DataList {
    const values: Array<Object> = Object.values(obj);
    return new DataList(
      values[0] as string,
      values[1] as string | undefined,
      values[2] as string | undefined
    );
  }
}

export class DataStep {
  dataType: String;
  step: String;
  minValue: String;
  maxValue: String;
  fallback?: String;
  @Type(() => Transform)
  transform?: Transform;
  unit?: String;

  constructor(dataType: String, step: String, minValue: String, maxValue: String, fallback?: String,
    transform?: Transform, unit?: String) {
    this.dataType = dataType;
    this.step = step;
    this.minValue = minValue;
    this.maxValue = maxValue;
    this.fallback = fallback;
    this.transform = transform;
    this.unit = unit;
  }

  static fromObject(obj: object | undefined): DataStep | undefined {
    if (obj == undefined) return undefined;
    const values: Array<Object> = Object.values(obj);
    return new DataStep(
      values[0] as string,
      values[1] as string,
      values[2] as string,
      values[3] as string,
      values[4] as string | undefined,
      Transform.fromObject(values[5] as object | undefined),
      values[6] as string | undefined
    );
  }
}

export class Transform {
  k: String;
  c: String;

  constructor(k: String, c: String) {
    this.k = k;
    this.c = c;
  }

  static fromObject(obj: object | undefined): Transform | undefined{
    if (obj == undefined) return undefined;
    const values: Array<Object> = Object.values(obj);
    return new Transform(
      values[0] as string,
      values[1] as string
    );
  }
}

export class DataTime {
  format: String;
  minHour: number;
  maxHour: number;
  minMinute: number;
  maxMinute: number;
  minSecond: number;
  maxSecond: number;

  constructor(format: String, minHour: number, maxHour: number, minMinute: number, maxMinute: number, minSecond: number,
    maxSecond: number) {
    this.format = format;
    this.minHour = minHour;
    this.maxHour = maxHour;
    this.minMinute = minMinute;
    this.maxMinute = maxMinute;
    this.minSecond = minSecond;
    this.maxSecond = maxSecond;
  }

  static fromObject(obj: object | undefined): DataTime | undefined {
    if (obj == undefined) return undefined;
    const values: Array<Object> = Object.values(obj);
    return new DataTime(
      values[0] as string,
      values[1] as number,
      values[2] as number,
      values[3] as number,
      values[4] as number,
      values[5] as number,
      values[6] as number
    );
  }
}

export class DataDate {
  format: String;
  beginDate: String;
  endDate: String;

  constructor(format: String, beginDate: String, endDate: String) {
    this.format = format;
    this.beginDate = beginDate;
    this.endDate = endDate;
  }

  static fromObject(obj: object | undefined): DataDate | undefined {
    if (obj == undefined) return undefined;
    const values: Array<Object> = Object.values(obj);
    return new DataDate(
      values[0] as string,
      values[1] as string,
      values[2] as string
    );
  }
}