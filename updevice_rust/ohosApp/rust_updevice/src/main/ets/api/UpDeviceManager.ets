import { UpDevice } from './device/UpDevice';
import { UpDeviceInfo } from './models/UpDeviceInfo';
import { handleRustVoidBufferResult, NapiCode, UpDeviceResult } from './UpDeviceResult';
import { <PERSON><PERSON><PERSON>List, JSON } from '@kit.ArkTS';
import { UpDeviceChangeListener } from './listener/UpDeviceChangeListener';
import rustDevice from 'librust_uplus.so';
import { Args } from './models/Args';
import { plainToInstance } from 'class-transformer';
import "reflect-metadata";
import { NapiResult } from './models/NapiResult';
import { UpDeviceAttribute } from './models/UpDeviceAttribute';
import { UpDeviceCaution } from './models/UpDeviceCaution';
import { stringToUpDeviceOnlineState } from './models/UpDeviceOnlineState';
import { stringToUpDeviceConnectState } from './models/UpDeviceConnectState';
import { DeviceInfoHandler } from './detail_interceptor/DeviceInfoHandler';
import { EngineDeviceHandler } from './detail_interceptor/EngineDeviceHandler';
import { DeviceResourceHandler } from './detail_interceptor/DeviceResoureHandler';
import { RelativeDevicePrepareHandler } from './detail_interceptor/RelativeDevicePrepareHandler';
import { DeviceDetailPatch } from './detail_interceptor/DeviceDetailPatch';
import { VirtualDomain } from '@uplus/upvdn/Index';
import { UpDeviceSleepState } from './models/UpDeviceSleepState';
import { UpDeviceOfflineCause } from './models/UpDeviceOfflineCause';
import { common } from '@kit.AbilityKit';
import { SpecialDeviceHandler } from './detail_interceptor/SpecialDeviceHandler';
import { UHSDPalManager } from 'uhsdpal';
import { ResourceUIInteractiveManager } from '@uplus/uppluginfoundation';
import { UpPrivacyManager } from './privacy/UpPrivacyManager';
import { PalLogLevel } from 'uhsdpal/src/main/ets/utils/HalLogger';
import { RustChannel } from '@uplus/rust_ffi';
import { FileLogger } from '@uplus/uplog';
import { RustConstant } from './util';

export const TAG = "RustUpDevice";

/**
 * 设备管理
 */
export class UpDeviceManager {
  static fileLogger?: FileLogger;

  static init(
    context: common.UIAbilityContext,
    appId: string,
    appKey: string,
    appVersion: string,
    app_debug_mode: boolean,
    clientId: string,
    environment: number,
    debug?: boolean,
  ): boolean {
    debug = debug ?? true; // 如果未传递，则使用默认值 false
    try {
      UpDeviceManager.fileLogger = new FileLogger("rust-updevice", debug ?? true);
      UHSDPalManager.register(context);
      // 设置日志开关
      UHSDPalManager.setLogLevel(app_debug_mode ? PalLogLevel.debug : PalLogLevel.error);
      let params = new Map<string, string>();
      params.set(RustConstant.Action, "init");
      params.set('app_id', appId);
      params.set('app_key', appKey);
      params.set('app_version', appVersion);
      params.set('app_debug_mode', String(app_debug_mode));
      params.set('client_id', clientId);
      params.set('environment', environment.toString());
      const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
      const result = handleRustVoidBufferResult(buf, TAG, "init");
      if (result.isSuccess()) {
        const deviceInfoHandler = new DeviceInfoHandler();
        const engineDeviceHandler = new EngineDeviceHandler();
        const relativeDeviceHandler = new RelativeDevicePrepareHandler();
        const specialDeviceHandler = new SpecialDeviceHandler();
        const deviceResourceHandler = new DeviceResourceHandler();
        deviceInfoHandler.setNext(engineDeviceHandler);
        engineDeviceHandler.setNext(relativeDeviceHandler);
        relativeDeviceHandler.setNext(specialDeviceHandler);
        specialDeviceHandler.setNext(deviceResourceHandler);
        //下载UI模块注册设备处理器
        ResourceUIInteractiveManager.getInstance().registerResourcePrepare(deviceInfoHandler);
        console.log("[updevice_harmony] registerDeviceHandle.");
        VirtualDomain.getInstance().registerLogicPatch(new DeviceDetailPatch(deviceInfoHandler));
        console.log("[updevice_harmony] registerLogicPatch.");
        UpPrivacyManager.getInstance().init();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      console.error("[updevice_harmony]init updevice from arkts: ", e);
      return false;
    }
  }

  /**
   * 根据 {@param FamilyId} 获取设备列表
   * @param familyId 家庭ID
   * @returns 家庭设备列表
   */
  static getDeviceListByFamilyId(familyId: string, withEngineAttributes: boolean = false): Array<UpDevice> {
    try {
      let args: object = new Args();
      args["family_id"] = familyId;
      args["with_engine_attributes"] = withEngineAttributes;
      let napi = rustDevice.updeviceActionDispatch("get_device_list_by_family_id", args) as NapiResult;
      let devices = plainToInstance(UpDevice, JSON.parse(napi.stringValue) as object[]);
      return devices;
    } catch (e) {
      console.error("getDeviceListByFamilyId: ", e);
      return new Array();
    }
  }

  /**
   * 使用 familyId 获取设备信息列表
   * @param familyId 家庭ID
   * @returns 家庭设备信息列表
   */
  static getDeviceInfoListByFamilyId(familyId: string): Array<UpDeviceInfo> {
    try {
      let args: object = new Args();
      args["family_id"] = familyId;
      let napi = rustDevice.updeviceActionDispatch("get_device_info_list_by_family_id", args) as NapiResult;
      let devices = plainToInstance(UpDeviceInfo, JSON.parse(napi.stringValue) as object[]);
      return devices;
    } catch (e) {
      console.error("getDeviceInfoListByFamilyId: ", e);
      return new Array();
    }
  }

  /**
   * 使用 deviceId 获取设备
   * @param deviceId 设备ID
   * @returns 未找到设备时返回 null
   */
  static getDeviceById(deviceId: string, withEngineAttributes: boolean = false): UpDevice | null {
    try {
      let args: object = new Args();
      args["device_id"] = deviceId;
      args["with_engine_attributes"] = withEngineAttributes;
      let napi = rustDevice.updeviceActionDispatch("get_device_by_id", args) as NapiResult;
      if (napi.stringValue == undefined) {
        return null;
      } else {
        let device = plainToInstance(UpDevice, JSON.parse(napi.stringValue) as object);
        return device;
      }
    } catch (e) {
      console.error("getDeviceById: ", e);
      return null;
    }
  }

  /**
   * 根据 deviceId 获取设备信息
   * @param deviceId 设备ID
   * @returns 未找到设备时返回 null
   */
  static getDeviceInfoById(deviceId: string): UpDeviceInfo | null {
    try {
      let args: object = new Args();
      args["device_id"] = deviceId;
      let napi = rustDevice.updeviceActionDispatch("get_device_by_id", args) as NapiResult;
      if (napi.stringValue == undefined) {
        return null;
      } else {
        let deviceInfo = plainToInstance(UpDeviceInfo, JSON.parse(napi.stringValue) as object);
        return deviceInfo;
      }
    } catch (e) {
      console.error("getDeviceInfoById: ", e);
      return null;
    }
  }

  /**
   * 根据父设备ID获取子设备列表
   * @param deviceId 父设备ID
   * @returns 子设备列表
   */
  static getSubDeviceListByParentId(deviceId: string): Array<UpDevice> {
    try {
      let args: object = new Args();
      args["parent_id"] = deviceId;
      let napi = rustDevice.updeviceActionDispatch("get_sub_device_list_by_parent_id", args) as NapiResult;
      let devices = plainToInstance(UpDevice, JSON.parse(napi.stringValue) as object[]);
      return devices;
    } catch (e) {
      console.error("getSubDeviceListByParentId: ", e);
      return new Array();
    }
  }

  /**
   * 根据子设备ID获取子设备列表
   * @param deviceId 子设备ID
   * @returns 子设备列表
   */
  static getSubDeviceListBySubDeviceId(deviceId: string): Array<UpDevice> {
    try {
      let args: object = new Args();
      args["sub_device_id"] = deviceId;
      let napi = rustDevice.updeviceActionDispatch("get_sub_device_list_by_sub_device_id", args) as NapiResult;
      let devices = plainToInstance(UpDevice, JSON.parse(napi.stringValue) as object[]);
      return devices;
    } catch (e) {
      console.error("getSubDeviceListBySubDeviceId: ", e);
      return new Array();
    }
  }

  /**
   * 订阅设备列表变化
   * @param listener 设备列表变化监听
   * @param immediate true 表示监听后立即返回设备列表
   * @returns 成功订阅时返回 Success 和订阅ID，其他情况下返回具体的错误信息
   */
  static subscribeDeviceListChange(
    listener: (devices: Array<UpDevice>) => void,
    immediate: boolean
  ): UpDeviceResult<string> {
    try {
      let args: object = new Args();
      args["immediate"] = immediate;
      let napi =
        rustDevice.updeviceActionDispatchWithCallback("subscribe_device_list_change", args, (list: Array<string>) => {
          handleDeviceListChange(list, listener);
        }) as NapiResult;
      let subscribeId = napi.stringValue;
      if (subscribeId == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.stringValue is undefined");
      } else {
        return UpDeviceResult.Success(subscribeId);
      }
    } catch (e) {
      console.error("subscribeDeviceListChange: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 取消设备列表变化订阅
   * @param subscriptionId 订阅ID
   * @returns 成功取消订阅时返回 Success，其他情况下返回具体的错误信息
   */
  static unsubscribeDeviceListChange(subscriptionId: string): UpDeviceResult<void> {
    try {
      let args: object = new Args();
      args["subscription_id"] = subscriptionId;
      let napi = rustDevice.updeviceActionDispatch("unsubscribe_device_list_change", args) as NapiResult;
      let unsubscribe = napi.booleanValue;
      if (unsubscribe == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.booleanValue is undefined");
      } else {
        return UpDeviceResult.Success();
      }
    } catch (e) {
      console.error("unsubscribeDeviceListChange: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 使用设备ID订阅设备变化
   * @param deviceId 设备ID
   * @param listener 设备变化监听
   * @returns 成功订阅时返回 Success 和订阅ID，其他情况下返回具体的错误信息
   */
  static subscribeDeviceChange(deviceId: string, listener: UpDeviceChangeListener,
    immediate: boolean = true): UpDeviceResult<string> {
    try {
      let args: object = new Args();
      args["device_id"] = deviceId;
      let napi =
        rustDevice.updeviceActionDispatchWithCallback("subscribe_device_change", args, (list: Array<string>) => {
          UpDeviceManager.handleUpDeviceChangeListenerData(list, listener, "subscribeDeviceChange");
        }) as NapiResult;
      let subscribeId = napi.stringValue;
      if (subscribeId == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.stringValue is undefined");
      }
      if (immediate) {
        listener.onAttributeChanged(deviceId, []);
      }
      return UpDeviceResult.Success(subscribeId);
    } catch (e) {
      console.error("subscribeDeviceChange: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  private static handleUpDeviceChangeListenerData(
    list: Array<string>,
    listener: UpDeviceChangeListener,
    method: string,
  ) {
    let action = list[0];
    switch (action) {
      case "AttributeChanged":
        let attrCode = Number(list[1]);
        let attrDeviceId = list[2];
        if (attrCode == NapiCode.Success) {
          let json = list[3];
          let attrs = plainToInstance(UpDeviceAttribute, JSON.parse(json) as object[]);
          listener.onAttributeChanged(attrDeviceId, attrs);
        } else if (attrCode == NapiCode.SerdeJsonError) {
          let errInfo = list[3];
          console.error("%s: onAttributeChanged: ", method, errInfo);
        } else {
          console.error("%s: onAttributeChanged: ", method, list.join("{@}"));
        }
        break;
      case "CautionChanged":
        let cauCode = Number(list[1]);
        let cauDeviceId = list[2];
        if (cauCode == NapiCode.Success) {
          let json = list[3];
          let cautions = plainToInstance(UpDeviceCaution, JSON.parse(json) as object[]);
          listener.onCautionChanged(cauDeviceId, cautions);
        } else if (cauCode == NapiCode.SerdeJsonError) {
          let errInfo = list[3];
          console.error("%s: onCautionChanged: ", method, errInfo);
        } else {
          console.error("%s: onCautionChanged: ", method, list.join("{@}"));
        }
        break;
      case "ConnectStateChanged":
        let connDeviceId = list[1];
        let connState = list[2];
        let connStateEnum = stringToUpDeviceConnectState(connState);
        if (connStateEnum != undefined) {
          listener.onConnectStateChanged(connDeviceId, connStateEnum);
        } else {
          console.error("%s: onConnectStateChanged: ", method, connState);
        }
        break;
      case "OnlineStateChanged":
        let onlineDeviceId = list[1];
        let onlineState = list[2];
        let onlineStateEnum = stringToUpDeviceOnlineState(onlineState);
        if (onlineStateEnum != undefined) {
          listener.onOnlineStateChanged(onlineDeviceId, onlineStateEnum);
        } else {
          console.error("%s: onOnlineStateChanged: ", method, onlineState);
        }
        break;
      case "BaseInfoChanged":
        UpDeviceManager.handleBaseInfoChanged(list, listener, method);
        break;
      case "SleepStateChanged":
        UpDeviceManager.handleSleepStateChanged(list, listener, method);
        break;
      case "OfflineCauseChanged":
        UpDeviceManager.handleOfflineCauseChanged(list, listener, method);
        break;
      case "OfflineDaysChanged":
        UpDeviceManager.handleOfflineDaysChanged(list, listener, method);
        break;
    }
  }

  static handleBaseInfoChanged(list: string[], listener: UpDeviceChangeListener, method: string) {
    const deviceId = UpDeviceManager.verifyDataFormat(method, "onBaseInfoChanged", list);
    if (!deviceId) {
      return;
    }
    if (list.length < 4) {
      console.error("%s[deviceId=%s]: onBaseInfoChanged failure: Invalid data format.", method, deviceId);
      return;
    }
    const deviceInfo = plainToInstance(UpDeviceInfo, JSON.parse(list[3]) as object);
    listener.onBaseInfoChanged(deviceId, deviceInfo);
  }

  static handleOfflineDaysChanged(list: string[], listener: UpDeviceChangeListener, method: string) {
    const deviceId = UpDeviceManager.verifyDataFormat(method, "onOfflineDaysChanged", list);
    if (!deviceId) {
      return;
    }
    if (list.length < 4) {
      console.error("%s[deviceId=%s]: onOfflineDaysChanged failure: Invalid data format.", method, deviceId);
      return;
    }
    const days = Number(list[3]);
    if (isNaN(days)) {
      console.error("%s[deviceId=%s]: onOfflineDaysChanged failure: days is Invalid number.", method, deviceId);
      return;
    }
    listener.onOfflineDaysChanged(deviceId, days);
  }

  static handleOfflineCauseChanged(list: string[], listener: UpDeviceChangeListener, method: string) {
    const deviceId = UpDeviceManager.verifyDataFormat(method, "onOfflineCauseChanged", list);
    if (!deviceId) {
      return;
    }
    if (list.length < 4) {
      console.error("%s[deviceId=%s]: onOfflineCauseChanged failure: Invalid data format.", method, deviceId);
      return;
    }
    const offlineCause = list[3] as UpDeviceOfflineCause;
    if (!offlineCause || !Object.values(UpDeviceOfflineCause).includes(offlineCause)) {
      console.error("%s[deviceId=%s]: onOfflineCauseChanged failure: Invalid enum value.", method, deviceId);
      return;
    }
    listener.onOfflineCauseChanged(deviceId, offlineCause);
  }

  private static handleSleepStateChanged(list: Array<string>, listener: UpDeviceChangeListener, method: string,) {
    const deviceId = UpDeviceManager.verifyDataFormat(method, "onSleepStateChanged", list);
    if (!deviceId) {
      return;
    }
    if (list.length < 4) {
      console.error("%s[deviceId=%s]: onSleepStateChanged failure: Invalid data format.", method, deviceId);
      return;
    }
    const sleepState = list[3] as UpDeviceSleepState;
    if (!sleepState || !Object.values(UpDeviceSleepState).includes(sleepState)) {
      console.error("%s[deviceId=%s]: onSleepStateChanged failure: Invalid enum value.", method, deviceId);
      return;
    }
    listener.onSleepStateChanged(deviceId, sleepState);
  }

  private static verifyDataFormat(method: string, funcName: string, list: Array<string>): string | undefined {
    if (list && list.length > 2) {
      console.error("%s: %s failure: Invalid data.", method, funcName);
      return undefined;
    }
    const code = Number(list[1]);
    if (isNaN(code)) {
      console.error("%s: %s failure: code is Invalid number.", method, funcName);
      return undefined;
    }
    const deviceId = list[2];
    if (code != NapiCode.Success) {
      console.error("%s[deviceId=%s]: %s failure: code=%d.", method, funcName, deviceId, code);
      return undefined;
    }
    return deviceId;
  }

  /**
   * 取消设备变化订阅
   * @param subscriptionId 订阅ID
   * @returns 成功取消订阅时返回 Success，其他情况下返回具体的错误信息
   */
  static unsubscribeDeviceChange(subscriptionId: string): UpDeviceResult<void> {
    try {
      let args: object = new Args();
      args["subscription_id"] = subscriptionId;
      let napi = rustDevice.updeviceActionDispatch("unsubscribe_device_change", args) as NapiResult;
      if (napi.booleanValue == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.booleanValue is undefined");
      } else {
        return UpDeviceResult.Success();
      }
    } catch (e) {
      console.error("unsubscribeDeviceChange: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 使用家庭ID订阅设备变化
   * @param familyId 家庭ID
   * @param listener 设备变化监听
   * @returns 成功订阅时返回 Success 和订阅ID，其他情况下返回具体的错误信息
   */
  static subscribeDeviceChangeByFamilyId(familyId: string, listener: UpDeviceChangeListener): UpDeviceResult<string> {
    try {
      let args: object = new Args();
      args["family_id"] = familyId;
      let napi =
        rustDevice.updeviceActionDispatchWithCallback("subscribe_device_change_by_family_id", args,
          (list: Array<string>) => {
            UpDeviceManager.handleUpDeviceChangeListenerData(list, listener, "subscribeDeviceChangeByFamilyId");
          }) as NapiResult;
      let subscribeId = napi.stringValue;
      if (subscribeId == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.stringValue is undefined");
      } else {
        return UpDeviceResult.Success(subscribeId);
      }
    } catch (e) {
      console.error("subscribeDeviceChangeByFamilyId: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 取消家庭下的设备变化订阅
   * @param subscriptionId 订阅ID
   * @returns 成功取消订阅时返回 Success，其他情况下返回具体的错误信息
   */
  static unsubscribeDeviceChangeByFamilyId(subscriptionId: string): UpDeviceResult<void> {
    try {
      let args: object = new Args();
      args["subscription_id"] = subscriptionId;
      let napi = rustDevice.updeviceActionDispatch("unsubscribe_device_change_by_family_id", args) as NapiResult;
      if (napi.booleanValue == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.booleanValue is undefined");
      } else {
        return UpDeviceResult.Success();
      }
    } catch (e) {
      console.error("unsubscribeDeviceChangeByFamilyId: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 使用多个设备ID订阅设备变化
   * @param deviceIds 设备ID列表
   * @param listener 设备变化监听
   * @returns 成功订阅时返回 Success 和订阅ID，其他情况下返回具体的错误信息
   */
  static subscribeDeviceChangeByDeviceIds(
    deviceIds: Array<string>,
    listener: UpDeviceChangeListener
  ): UpDeviceResult<string> {
    try {
      let args: object = new Args();
      args["device_ids"] = deviceIds;
      let napi =
        rustDevice.updeviceActionDispatchWithCallback("subscribe_device_change_by_device_ids", args,
          (list: Array<string>) => {
            UpDeviceManager.handleUpDeviceChangeListenerData(list, listener, "subscribeDeviceChangeByDeviceIds");
          }) as NapiResult;
      let subscribeId = napi.stringValue;
      if (subscribeId == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.stringValue is undefined");
      } else {
        return UpDeviceResult.Success(subscribeId);
      }
    } catch (e) {
      console.error("subscribeDeviceChangeByDeviceIds: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 取消多个设备的设备变化订阅
   * @param subscriptionId 订阅ID
   * @returns 成功取消订阅时返回 Success，其他情况下返回具体的错误信息
   */
  static unsubscribeDeviceChangeByDeviceIds(subscriptionId: string): UpDeviceResult<void> {
    try {
      let args: object = new Args();
      args["subscription_id"] = subscriptionId;
      let napi = rustDevice.updeviceActionDispatch("unsubscribe_device_change_by_device_ids", args) as NapiResult;
      if (napi.booleanValue == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.booleanValue is undefined");
      } else {
        return UpDeviceResult.Success();
      }
    } catch (e) {
      console.error("unsubscribeDeviceChangeByDeviceIds: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }

  /**
   * 更新设备列表
   * @param isUseCache true 表示使用本地缓存，false 表示查询 Server 接口
   * @param familyId 家庭ID
   * @param withEngineAttributes 是否包含逻辑引擎属性
   * @returns 成功时返回 Success 和设备列表，其他情况下返回具体的错误信息
   */
  static updateDeviceList(isUseCache: boolean, familyId: string | undefined = undefined, withEngineAttributes: boolean = false): Promise<UpDeviceResult<Array<UpDevice>>> {
    return new Promise((resolve, reject) => {
      try {
        let args: object = new Args();
        args['is_use_cache'] = isUseCache;
        args['family_id'] = familyId;
        args['with_engine_attributes'] = withEngineAttributes;
        rustDevice.updeviceActionDispatchWithCallback("update_device_list", args, (list: Array<string>) => {
          let code = Number(list[0]);
          let data = list[1];
          if (code == NapiCode.Success) {
            let device_arr = plainToInstance(UpDevice, JSON.parse(data) as object[]);
            resolve(UpDeviceResult.Success(device_arr))
          } else {
            reject(UpDeviceResult.Error(code, data, null));
          }
        })
      } catch (e) {
        reject(e);
      }
    });
  }
  // 创建组设备
  static async createDeviceGroup(deviceId: string) : Promise<UpDeviceResult<UpDeviceInfo>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = deviceId;
        rustDevice.updeviceActionDispatchWithCallback('create_device_group', args, (list: Array<string>) => {
          if (list.length < 2) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] createDeviceGroup failure: Invalid data format.", null));
            return;
          }
          const code = Number(list[0]);
          if (isNaN(code)) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] createDeviceGroup failure: code is Invalid number.", null));
            return;
          }
          if (code == NapiCode.Success) {
            let jsonData = list[1];
            let deviceInfo = plainToInstance(UpDeviceInfo, JSON.parse(jsonData) as object);
            resolve(UpDeviceResult.Success(deviceInfo));
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }
  // 获取可与当前设备分到同一组的设备列表
  static async findGroupableDevices(deviceId: string) : Promise<UpDeviceResult<UpDevice[]>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = deviceId;
        rustDevice.updeviceActionDispatchWithCallback('find_groupable_devices', args, (list: Array<string>) => {
          if (list.length < 2) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters,
              "[updevice_harmony] findGroupableDevices failure: Invalid data format.", null));
            return;
          }
          const code = Number(list[0]);
          if (isNaN(code)) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters,
              "[updevice_harmony] findGroupableDevices failure: code is Invalid number.", null));
            return;
          }
          if (code == NapiCode.Success) {
            let jsonData = list[1];
            const deviceIds = JSON.parse(jsonData) as string[];
            if (Array.isArray(deviceIds)) {
              let devices: UpDevice[] = new Array();
              for (const deviceId of deviceIds) {
                let device = UpDeviceManager.getDeviceById(deviceId);
                if (device != null) {
                  devices.push(device);
                }
              }
              resolve(UpDeviceResult.Success(devices));
            } else {
              reject(UpDeviceResult.Error(NapiCode.IllegalParameters,
                "[updevice_harmony] findGroupableDevices failure: value is not array.", null));
              return;
            }
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }
  // 是否为组设备
  static isGroup(deviceId: string) : UpDeviceResult<boolean | undefined> {
    try {
      const args: object = new Args();
      args['device_id'] = deviceId;
      let result = rustDevice.updeviceActionDispatch('is_group', args) as NapiResult;
      if (result.booleanValue == undefined) {
        return UpDeviceResult.Error(NapiCode.NapiCallError, "isGroup call result error", undefined, undefined);
      }
      return UpDeviceResult.Success(result.booleanValue);
    } catch (e) {
      console.error("isGroup: ", e);
      return UpDeviceResult.Error(NapiCode.ExecuteError, "isGroup throw error", undefined, undefined);
    }
  }
  // 删除组设备
  static async deleteDeviceGroup(deviceId: string) : Promise<UpDeviceResult<string>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = deviceId;
        rustDevice.updeviceActionDispatchWithCallback('delete_device_group', args, (list: Array<string>) => {
          if (list.length < 2) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] deleteDeviceGroup failure: Invalid data format.", null));
            return;
          }
          const code = Number(list[0]);
          if (isNaN(code)) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] deleteDeviceGroup failure: code is Invalid number.", null));
            return;
          }
          if (code == NapiCode.Success) {
            let jsonData = list[1];
            resolve(UpDeviceResult.Success(jsonData));
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }
  // 用户侧组设备批量添加
  static async addDevicesToGroup(deviceId: string, devices:ArrayList<string>) : Promise<UpDeviceResult<String>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = deviceId;
        args['devices'] = devices;
        rustDevice.updeviceActionDispatchWithCallback('add_devices_to_group', args, (list: Array<string>) => {
          if (list.length < 2) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] addDevicesToGroup failure: Invalid data format.", null));
            return;
          }

          const code = Number(list[0]);
          if (isNaN(code)) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] addDevicesToGroup failure: code is Invalid number.", null));
            return;
          }

          if (code == NapiCode.Success) {
            let jsonData = list[1];
            resolve(UpDeviceResult.Success(jsonData));
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }
  // 用户侧组设备批量删除
  static async removeDevicesFromGroup(deviceId: string, devices:ArrayList<string>) : Promise<UpDeviceResult<String>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = deviceId;
        args['devices'] = devices;
        rustDevice.updeviceActionDispatchWithCallback('remove_devices_from_group', args, (list: Array<string>) => {
          if (list.length < 2) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] removeDevicesFromGroup failure: Invalid data format.", null));
            return;
          }

          const code = Number(list[0]);
          if (isNaN(code)) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] removeDevicesFromGroup failure: code is Invalid number.", null));
            return;
          }

          if (code == NapiCode.Success) {
            let jsonData = list[1];
            resolve(UpDeviceResult.Success(jsonData));
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }
  static async getGroupMemberList(deviceId: string) : Promise<UpDeviceResult<UpDevice[]>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = deviceId;
        rustDevice.updeviceActionDispatchWithCallback('get_group_member_list', args, (list: Array<string>) => {
          if (list.length < 2) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] getGroupMemberList failure: Invalid data format.", null));
            return;
          }
          const code = Number(list[0]);
          if (isNaN(code)) {
            reject(UpDeviceResult.Error(NapiCode.IllegalParameters, "[updevice_harmony] getGroupMemberList failure: code is Invalid number.", null));
            return;
          }
          if (code == NapiCode.Success) {
            let jsonData = list[1];
            let devices = plainToInstance(UpDevice, JSON.parse(jsonData) as object[]);
            resolve(UpDeviceResult.Success(devices));
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }
  static priorityPrepareDevice(deviceId: string): UpDeviceResult<void> {
    try {
      let args: object = new Args();
      args["device_id"] = deviceId;
      let napi = rustDevice.updeviceActionDispatch("priority_prepare_device", args) as NapiResult;
      if (napi.booleanValue == undefined) {
        return UpDeviceResult.Error(NapiCode.ExecuteError, "napi.booleanValue is undefined");
      } else {
        return UpDeviceResult.Success();
      }
    } catch (e) {
      console.error("priorityPrepareDevice: ", e);
      return UpDeviceResult.Error(NapiCode.NapiCallError, "call rust error");
    }
  }
}

function handleDeviceListChange(list: string[], listener: (devices: Array<UpDevice>) => void,) {
  if (list.length < 2) {
    console.error("onDeviceListChange failure: Invalid data format.");
    return;
  }
  const code = Number(list[0]);
  if (isNaN(code)) {
    console.error("onDeviceListChange failure: code is Invalid number.");
    return;
  }
  if (code != NapiCode.Success) {
    console.error("onDeviceListChange failure: code=%d.", code);
    return;
  }
  const devices = plainToInstance(UpDevice, JSON.parse(list[1]) as object[]);
  listener(devices);
}
