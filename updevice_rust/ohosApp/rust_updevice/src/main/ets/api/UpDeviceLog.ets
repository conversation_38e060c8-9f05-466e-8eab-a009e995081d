import { hilog } from '@kit.PerformanceAnalysisKit';
import { FileLogger } from '@uplus/uplog';
import { UpDeviceManager } from './UpDeviceManager';

const DOMAIN = 0xFFFA;

export class UpDeviceLog {
  static debug(tag: string, message: string): void {
    hilog.debug(DOMAIN, tag, message);
    UpDeviceLog.fileLogger()?.logToFile(tag, message);
  }

  static info(tag: string, message: string): void {
    hilog.info(DOMAIN, tag, message);
    UpDeviceLog.fileLogger()?.logToFile(tag, message);
  }

  static warn(tag: string, message: string): void {
    hilog.warn(DOMAIN, tag, message);
    UpDeviceLog.fileLogger()?.logToFile(tag, message);
  }

  static error(tag: string, message: string): void {
    hilog.error(DOMAIN, tag, message);
    UpDeviceLog.fileLogger()?.logToFile(tag, message);
  }

  private static fileLogger(): FileLogger | undefined {
    return UpDeviceManager.fileLogger;
  }
}