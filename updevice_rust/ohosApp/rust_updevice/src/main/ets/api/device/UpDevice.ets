import { UpDeviceInfo } from '../models/UpDeviceInfo';
import { UpDeviceAttribute } from '../models/UpDeviceAttribute';
import { UpDeviceCaution } from '../models/UpDeviceCaution';
import { UpDeviceConnectState } from '../models/UpDeviceConnectState';
import { UpDeviceOnlineState, UpDeviceOnlineStateV2 } from '../models/UpDeviceOnlineState';
import { NapiCode, UpDeviceResult } from '../UpDeviceResult';
import { UpDeviceCommand } from '../models/UpDeviceCommand';
import { UpDeviceConfigState } from '../models/UpDeviceConfigState';
import { UpDeviceSleepState } from '../models/UpDeviceSleepState';
import { UpDeviceOfflineCause } from '../models/UpDeviceOfflineCause';
import "reflect-metadata";
import { Type } from 'class-transformer';
import { Command } from '../models/Command';
import { Attribute } from '../models/Attribute';
import { ExtendApi } from './ExtendApi';
import rustDevice from 'librust_uplus.so';
import { Args } from '../models/Args';
import { Caution } from '../models/Caution';
import { DeviceUtils } from '../DeviceUtils';

/**
 * UpDevice 设备
 */
export class UpDevice {
  protocol: string;
  deviceId: string;
  @Type(() => UpDeviceInfo)
  deviceInfo: UpDeviceInfo;
  @Type(() => UpDeviceAttribute)
  attributeList: Array<UpDeviceAttribute>;
  @Type(() => UpDeviceCaution)
  cautionList: Array<UpDeviceCaution>;
  @Type(() => Attribute)
  engineAttributeList: Array<Attribute>;
  @Type(() => Caution)
  engineCautionList: Array<Caution>;
  @Type(() => String)
  configState: UpDeviceConfigState;
  @Type(() => String)
  connectState: UpDeviceConnectState;
  @Type(() => String)
  bleState: UpDeviceConnectState;
  @Type(() => String)
  onlineState: UpDeviceOnlineState;
  @Type(() => String)
  sleepState: UpDeviceSleepState;
  faultStateCode: number;
  @Type(() => String)
  offlineCause: UpDeviceOfflineCause;
  offlineDays: number;

  static fromObject(obj: object): UpDevice {
    let values: Array<Object> = Object.values(obj);
    return new UpDevice(
      values[0] as string,
      values[1] as string,
      UpDeviceInfo.fromObject(values[2] as Object),
      DeviceUtils.toDeviceAttributes(values[3] as Array<Object>),
      DeviceUtils.toDeviceCautions(values[4] as Array<Object>),
      DeviceUtils.toAttributes(values[5] as Array<Object>),
      DeviceUtils.toCautions(values[6] as Array<Object>),
      DeviceUtils.toConfigState(values[7] as string),
      DeviceUtils.toConnectState(values[8] as string),
      DeviceUtils.toConnectState(values[9] as string),
      DeviceUtils.toOnlineState(values[10] as string),
      DeviceUtils.toSleepState(values[11] as string),
      values[12] as number,
      DeviceUtils.toOfflineCause(values[13] as string),
      values[14] as number
    );
  }

  constructor(
    protocol: string,
    deviceId: string,
    deviceInfo: UpDeviceInfo,
    attributeList: Array<UpDeviceAttribute>,
    cautionList: Array<UpDeviceCaution>,
    engineAttributeList: Array<Attribute>,
    engineCautionList: Array<Caution>,
    configState: UpDeviceConfigState,
    connectState: UpDeviceConnectState,
    bleState: UpDeviceConnectState,
    onlineState: UpDeviceOnlineState,
    sleepState: UpDeviceSleepState,
    faultStateCode: number,
    offlineCause: UpDeviceOfflineCause,
    offlineDays: number,
  ) {
    this.protocol = protocol;
    this.deviceId = deviceId;
    this.deviceInfo = deviceInfo;
    this.attributeList = attributeList;
    this.cautionList = cautionList;
    this.engineAttributeList = engineAttributeList;
    this.engineCautionList = engineCautionList;
    this.configState = configState;
    this.connectState = connectState;
    this.bleState = bleState;
    this.onlineState = onlineState;
    this.sleepState = sleepState;
    this.faultStateCode = faultStateCode;
    this.offlineCause = offlineCause;
    this.offlineDays = offlineDays;
  }


  getProtocol(): string {
    return this.protocol;
  }

  getDeviceId(): string {
    return this.deviceId;
  }

  /**
   * 获取设备信息
   * @returns
   */
  getDeviceInfo(): UpDeviceInfo {
    return this.deviceInfo;
  }

  /**
   * 获取设备属性列表
   * @returns
   */
  getAttributeList(): Array<UpDeviceAttribute> {
    return this.attributeList;
  }

  /**
   * 获取设备告警列表
   * @returns
   */
  getCautionList(): Array<UpDeviceCaution> {
    return this.cautionList;
  }

  /**
   * 获取逻辑引擎属性列表
   * @returns
   */
  getEngineAttributeList(): Array<Attribute> {
    return this.engineAttributeList;
  }

  /**
   * 获取逻辑引擎告警列表
   * @returns
   */
  getEngineCautionList(): Array<Caution> {
    return this.engineCautionList;
  }

  /**
   * 获取设备配置状态
   * @returns
   */
  getConfigState(): UpDeviceConfigState {
    return this.configState;
  }

  /**
   * 获取设备连接状态
   * @deprecated 使用`getConnectState`替代
   * @returns
   */
  getConnection(): UpDeviceConnectState {
    return this.connectState;
  }

  /**
   * 获取设备连接状态
   * @returns
   */
  getConnectState(): UpDeviceConnectState {
    return this.connectState;
  }

  /**
   * 获取设备蓝牙连接状态
   * @returns
   */
  getBleState(): UpDeviceConnectState {
    return this.bleState;
  }

  /**
   * 获取设备在线状态
   * @returns
   */
  getOnlineState(): UpDeviceOnlineState {
    return this.onlineState;
  }

  /**
   * 获取设备在线状态
   * @returns
   */
  getOnlineStateV2(): UpDeviceOnlineStateV2 {
    if (this.onlineState === UpDeviceOnlineState.Offline) {
      return UpDeviceOnlineStateV2.Offline;
    }
    if (this.connectState === UpDeviceConnectState.Ready) {
      return UpDeviceOnlineStateV2.OnlineReady;
    } else {
      return UpDeviceOnlineStateV2.OnlineNotReady;
    }
  }

  /**
   * 获取设备休眠状态
   * @returns
   */
  getSleepState(): UpDeviceSleepState {
    return this.sleepState;
  }

  /**
   * 获取设备故障状态码
   * @returns
   */
  getFaultStateCode(): number {
    if (this.faultStateCode === 1000) {
      return 0;
    }
    return this.faultStateCode;
  }


  /**
   * 获取设备离线原因
   * @returns
   */
  getOfflineCause(): UpDeviceOfflineCause {
    return this.offlineCause;
  }

  /**
   * 获取离线天数
   * @returns
   */
  getOfflineDays(): number {
    return this.offlineDays;
  }

  /**
   * 下发命令
   * @param command 命令
   * @returns 成功时返回 Success，其他情况下返回具体的错误信息
   */
  async executeCommand(command: UpDeviceCommand): Promise<UpDeviceResult<String>> {
    return new Promise((resolve, reject) => {
      try {
        const args: object = new Args();
        args['device_id'] = this.deviceId;
        const groupName = command?.groupName;
        if (groupName) {
          args['group_name'] = groupName;
        }
        let jsonObj: Record<string, Object> = {};
        command.attributes.forEach((value, key) => {
          if (key && value !== undefined) {
            jsonObj[key] = value
          }
        });
        args['pair_map'] = JSON.stringify(jsonObj);
        rustDevice.updeviceActionDispatchWithCallback('execute_command', args, (list: Array<string>) => {
          let code = Number(list[0]);
          if (code == NapiCode.Success) {
            resolve(UpDeviceResult.Success(""));
          } else {
            let rust_error_info = list[1];
            reject(UpDeviceResult.Error(code, rust_error_info, null));
          }
        });
      } catch (e) {
        reject(UpDeviceResult.Error(NapiCode.IllegalParameters, JSON.stringify(e), null));
      }
    });
  }

  /**
   * 刷新设备全属性列表
   * @returns
   */
  refreshAttributes(): Promise<UpDeviceResult<Array<Attribute>>> {
    return new Promise((resolve, _) => resolve(UpDeviceResult.Success()));
  }

  /**
   * 下发命令
   * @param commands 命令列表
   * @returns
   */
  operate(commands: Array<Command>): Promise<UpDeviceResult<void>> {
    return new Promise((resolve, _) => resolve(UpDeviceResult.Success()));
  }

  /**
   * 队列方式下发命令
   * @param commands 命令列表
   * @returns
   */
  operateCommandQueue(commands: Array<Command>): Promise<UpDeviceResult<void>> {
    return new Promise((resolve, _) => resolve(UpDeviceResult.Success()));
  }

  /**
   * 清除队列中未下发的命令
   * @returns
   */
  clearOperateCommandQueue(): UpDeviceResult<void> {
    return UpDeviceResult.Success();
  }

  getExtendApi(): ExtendApi {
    return new ExtendApi(this.deviceId);
  }
}


