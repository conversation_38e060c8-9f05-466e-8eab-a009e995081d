{
  "app": {
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "useNormalizedOHMUrl": true, // rust ffi 依赖导致的修改
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "release"
      }
    ],
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_ohosApp_1nZqAf7v00Ij4VCQaT4U_uV1B3gmRkwqyV7hBPMCN3A=.cer",
          "storePassword": "0000001AA865173AF74737EFDD3AACDCE1E892EED39709BE6649ECCCF398BEF1DA4BEDECC5EB276EB3DB",
          "keyAlias": "debugKey",
          "keyPassword": "0000001A32648CB0F6163B7D1E759D59D8262A204F917DDCF5CC5152D066DD11EAB9774A3EF2C785CC87",
          "profile": "/Users/<USER>/.ohos/config/default_ohosApp_1nZqAf7v00Ij4VCQaT4U_uV1B3gmRkwqyV7hBPMCN3A=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_ohosApp_1nZqAf7v00Ij4VCQaT4U_uV1B3gmRkwqyV7hBPMCN3A=.p12"
        }
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "rust_updevice",
      "srcPath": "./rust_updevice",
    }
  ]
}