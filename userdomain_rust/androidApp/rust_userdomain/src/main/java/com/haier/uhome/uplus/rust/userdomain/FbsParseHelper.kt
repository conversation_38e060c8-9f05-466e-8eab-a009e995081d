@file:Suppress("UNCHECKED_CAST")

package com.haier.uhome.uplus.rust.userdomain

import com.haier.uhome.uplus.rust.userdomain.UserDomainResult.Companion.JNI_RETURN_NULL
import com.haier.uhome.uplus.rust.userdomain.fbs.BoolWrapper
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSAddress
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSAddressInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSAddressInfoList
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSAuthData
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDevice
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDeviceInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDeviceList
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDeviceMap
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDeviceOperationResult
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDeviceOwnerInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSDevicePermission
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSFamily
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSFamilyInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSFamilyList
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSFamilyMap
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSFamilyMemberInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSFloorInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSLocation
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSMemberInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSShareDeviceCardInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.FBSUserInfo
import com.haier.uhome.uplus.rust.userdomain.fbs.Int32Wrapper
import com.haier.uhome.uplus.rust.userdomain.fbs.StrWrapper
import com.haier.uhome.uplus.rust.userdomain.fbs.UserDomainFlat
import com.haier.uhome.uplus.rust.userdomain.fbs.UserdomainContainer
import com.haier.uhome.uplus.rust.userdomain.model.Address
import com.haier.uhome.uplus.rust.userdomain.model.AddressInfo
import com.haier.uhome.uplus.rust.userdomain.model.AuthData
import com.haier.uhome.uplus.rust.userdomain.model.Device
import com.haier.uhome.uplus.rust.userdomain.model.DeviceAuth
import com.haier.uhome.uplus.rust.userdomain.model.DeviceInfo
import com.haier.uhome.uplus.rust.userdomain.model.DeviceOwnerInfo
import com.haier.uhome.uplus.rust.userdomain.model.DevicePermission
import com.haier.uhome.uplus.rust.userdomain.model.Family
import com.haier.uhome.uplus.rust.userdomain.model.FamilyInfo
import com.haier.uhome.uplus.rust.userdomain.model.FamilyMemberInfo
import com.haier.uhome.uplus.rust.userdomain.model.FloorInfo
import com.haier.uhome.uplus.rust.userdomain.model.Location
import com.haier.uhome.uplus.rust.userdomain.model.MemberInfo
import com.haier.uhome.uplus.rust.userdomain.model.Room
import com.haier.uhome.uplus.rust.userdomain.model.ShareDeviceCardInfo
import com.haier.uhome.uplus.rust.userdomain.model.UserInfo
import java.nio.ByteBuffer

fun <T> parseUserDomainResult(bysBuffer: ByteBuffer?): UserDomainResult<T>? {
    if (bysBuffer == null) {
        return null
    }
    val result = UserDomainFlat.getRootAsUserDomainFlat(bysBuffer)
    return when (result.containerType) {
        UserdomainContainer.NoneWrapper -> {
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
            )
        }

        UserdomainContainer.BoolWrapper -> {
            val value = (result.container(BoolWrapper()) as BoolWrapper?)?.value
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = value as T
            )
        }

        UserdomainContainer.Int32Wrapper -> {
            val value = (result.container(Int32Wrapper()) as Int32Wrapper?)?.value
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = value as T
            )
        }


        UserdomainContainer.StrWrapper -> {
            val value = (result.container(StrWrapper()) as StrWrapper?)?.value
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = value as T
            )
        }

        UserdomainContainer.FBSUserInfo -> {
            val value = result.container(FBSUserInfo()) as FBSUserInfo?
            val userInfo = value?.createUserInfo()
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = userInfo as T
            )
        }

        UserdomainContainer.FBSAuthData -> {
            val value = result.container(FBSAuthData()) as FBSAuthData?
            val authData = value?.createAuthData()
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = authData as T
            )
        }

        UserdomainContainer.FBSFamilyInfo -> {
            val value = result.container(FBSFamilyInfo()) as FBSFamilyInfo?
            val familyInfo = value?.createFamilyInfo()
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = familyInfo as T
            )
        }

        UserdomainContainer.FBSFamily -> {
            val value = result.container(FBSFamily()) as FBSFamily?
            val family = value?.familyInfo?.run {
                Family(
                    familyInfo = this.createFamilyInfo()
                )
            }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = family as T
            )
        }

        UserdomainContainer.FBSFamilyList -> {
            val vecFamily = result.container(FBSFamilyList()) as FBSFamilyList?
            val families = vecFamily?.let { createFamilies(it) }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = families as T
            )
        }

        UserdomainContainer.FBSFamilyMap -> {
            val vecFamily = result.container(FBSFamilyMap()) as FBSFamilyMap?
            val familyMap = vecFamily?.let { createFamilyMap(it) }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = familyMap as T
            )
        }

        UserdomainContainer.FBSDevice -> {
            val value = result.container(FBSDevice()) as FBSDevice?
            val deviceInfo = value?.deviceInfo?.createDeviceInfo()
            val device = deviceInfo?.let {
                Device(
                    deviceInfo = it
                )
            }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = device as T
            )
        }

        UserdomainContainer.FBSDeviceList -> {
            val vecDevice = result.container(FBSDeviceList()) as FBSDeviceList?
            val devices = vecDevice?.let { createDevices(it) }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = devices as T
            )
        }

        UserdomainContainer.FBSDeviceMap -> {
            val vecDevice = result.container(FBSDeviceMap()) as FBSDeviceMap?
            val deviceMap = vecDevice?.let { createDeviceMap(it) }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = deviceMap as T
            )
        }

        UserdomainContainer.FBSAddressInfo -> {
            val value = result.container(FBSAddressInfo()) as FBSAddressInfo?
            val address = value?.createAddressInfo()
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = address as T
            )
        }

        UserdomainContainer.FBSAddressInfoList -> {
            val vecAddress = result.container(FBSAddressInfoList()) as FBSAddressInfoList?
            val addresses = vecAddress?.let { createAddressInfoList(it) }
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = addresses as T
            )
        }

        UserdomainContainer.FBSDeviceOperationResult -> {
            val value = result.container(FBSDeviceOperationResult()) as FBSDeviceOperationResult?
            val deviceOperationResult = value?.createDeviceOperationResult()
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = deviceOperationResult as T
            )
        }

        UserdomainContainer.FBSFamilyMemberInfo -> {
            val value = result.container(FBSFamilyMemberInfo()) as FBSFamilyMemberInfo?
            val familyMemberInfo = value?.createFamilyMemberInfo()
            UserDomainResult(
                code = result.code ?: JNI_RETURN_NULL,
                error = result.error ?: "",
                value = familyMemberInfo as T
            )
        }

        else -> null
    }
}

private fun FBSFamilyMemberInfo.createFamilyMemberInfo() = memberInfo?.createMemberInfo()?.let {
    FamilyMemberInfo(
        familyId = familyId ?: "",
        joinTime = joinTime ?: "",
        memberName = memberName ?: "",
        memberRole = memberRole,
        shareDeviceCount = shareDeviceCount.toInt(),
        memberInfo = it,
        memberType = memberType.toLong()
    )
}

private fun FBSDeviceOperationResult.createDeviceOperationResult(): DeviceOperationResult {
    return DeviceOperationResult(
        successDevices = createSuccessDevices(),
        failedDevices = createFailedDevices(),
    )
}

private fun FBSDeviceOperationResult.createFailedDevices(): List<DeviceResult> {
    val failedDevices = mutableListOf<DeviceResult>()
    for (i in 0 until failureDevicesLength) {
        failureDevices(i)?.run {
            failedDevices.add(
                DeviceResult(
                    deviceId = deviceId ?: "",
                    deviceName = deviceName ?: "",
                    reasonCode = reasonCode ?: "",
                )
            )
        }
    }
    return failedDevices
}

private fun FBSDeviceOperationResult.createSuccessDevices(): List<DeviceResult> {
    val successDevices = mutableListOf<DeviceResult>()
    for (i in 0 until successDevicesLength) {
        successDevices(i)?.run {
            successDevices.add(
                DeviceResult(
                    deviceId = deviceId ?: "",
                    deviceName = deviceName ?: "",
                    reasonCode = reasonCode ?: "",
                )
            )
        }
    }
    return successDevices
}

private fun FBSUserInfo.createUserInfo() = UserInfo(
    userId = userId ?: "",
    username = username ?: "",
    mobile = mobile,
    email = email,
    givenName = givenName,
    nickname = nickname,
    familyNum = familyNum,
    gender = gender,
    marriage = marriage,
    birthday = birthday,
    education = education,
    avatarUrl = avatarUrl,
    extraPhone = extraPhone,
    income = income,
    height = height,
    weight = weight,
    countryCode = countryCode,
    privacyCountryCode = privacyCountryCode,
    signature = signature,
    regClientId = regClientId,
)

private fun FBSAuthData.createAuthData(): AuthData {
    return AuthData(
        accessToken = accessToken ?: "",
        expiresIn = expiresIn,
        refreshToken = refreshToken ?: "",
        scope = scope ?: "",
        tokenType = tokenType ?: "",
        uHomeAccessToken = uhomeAccessToken ?: "",
        uHomeUserId = uhomeUserId ?: "",
        createTime = createTime,
        ucUserId = ucUserId ?: ""
    )
}

fun createAddressInfoList(addressInfoList: FBSAddressInfoList): List<AddressInfo> {
    val infoList = mutableListOf<AddressInfo>()
    for (i in 0 until addressInfoList.addressesLength) {
        addressInfoList.addresses(i)?.run {
            val addressInfo = createAddressInfo()
            infoList.add(addressInfo)
        }
    }
    return infoList
}

private fun FBSAddressInfo.createAddressInfo() = AddressInfo(
    address = address.createAddress(),
    addressId = addressId ?: "",
    email = email,
    isDefault = isDefault,
    isService = isService,
    receiverMobile = receiverMobile ?: "",
    receiverName = receiverName ?: "",
    source = source ?: "",
    tag = tag,
    userId = userId ?: "",
)

private fun FBSAddress?.createAddress(): Address {
    return if (this == null)
        Address(
            city = "",
            cityId = "",
            countryCode = "",
            district = "",
            districtId = "",
            line1 = "",
            line2 = "",
            postcode = "",
            province = "",
            provinceId = "",
            town = "",
            townId = ""
        )
    else
        Address(
            city = city ?: "",
            cityId = cityId ?: "",
            countryCode = countryCode,
            district = district ?: "",
            districtId = districtId ?: "",
            line1 = line1 ?: "",
            line2 = line2,
            postcode = postcode,
            province = province ?: "",
            provinceId = provinceId ?: "",
            town = town,
            townId = townId,
        )
}


private fun FBSDeviceInfo.createDeviceInfo() = DeviceInfo(
    deviceId = deviceId ?: "",
    deviceName = deviceName ?: "",
    devName = devName ?: "",
    deviceType = deviceType,
    familyId = familyId ?: "",
    ownerId = ownerId ?: "",
    permission = permission.createDevicePermission(),
    wifiType = wifiType,
    deviceNetType = deviceNetType,
    bindTime = bindTime ?: "",
    isOnline = isOnline,
    ownerInfo = ownerInfo.createDeviceOwnerInfo(),
    subDeviceIds = createSubDeviceIds(this),
    parentId = parentId,
    deviceRole = deviceRole,
    deviceRoleType = deviceRoleType,
    appTypeName = apptypeName ?: "",
    appTypeCode = apptypeCode ?: "",
    categoryGrouping = categoryGrouping ?: "",
    barcode = barcode ?: "",
    bindType = bindType,
    brand = brand ?: "",
    imageAddr1 = imageAddr1 ?: "",
    cardPageImg = cardPageImg ?: "",
    cardSort = cardSort.toLong(),
    cardStatus = cardStatus.toLong(),
    aggregationParentId = aggregationParentId ?: "",
    supportAggregationFlag = supportAggregationFlag.toLong(),
    deviceAggregateType = deviceAggregateType ?: "",
    model = model ?: "",
    prodNo = prodNo ?: "",
    roomName = roomName ?: "",
    roomId = roomId ?: "",
    accessType = accessType,
    configType = configType ?: "",
    communicationMode = communicationMode,
    deviceFloorId = deviceFloorId ?: "",
    deviceFloorOrderId = deviceFloorOrderId ?: "",
    deviceFloorName = deviceFloorName ?: "",
    apptypeIcon = apptypeIcon ?: "",
    deviceGroupId = deviceGroupId,
    deviceGroupType = deviceGroupType,
    noKeepAlive = noKeepAlive,
    twoGropingName = twoGropingName ?: "",
    supportFlag = supportFlag.toLong(),
    sharedDeviceFlag = sharedDeviceFlag.toLong(),
    shareDeviceCardInfo = createShareDeviceCardInfoList(this),
    attachmentSortCode = attachmentSortCode.toLong(),
    deviceShareSupportFlag = deviceShareSupportFlag,
    rebind = rebind.toLong(),
)

private fun createDevices(deviceList: FBSDeviceList): List<Device> {
    val devices = mutableListOf<Device>()
    for (i in 0 until deviceList.devicesLength) {
        deviceList.devices(i)?.deviceInfo?.run {
            devices.add(
                Device(
                    deviceInfo = createDeviceInfo()
                )
            )
        }
    }
    return devices
}

private fun createDeviceMap(fbsDeviceMap: FBSDeviceMap): Map<String, Device> {
    val deviceMap = mutableMapOf<String, Device>()
    fbsDeviceMap.run {
        for (i in 0 until entriesLength) {
            val deviceEntry = entries(i)
            deviceEntry?.run {
                key?.let { deviceId ->
                    value?.deviceInfo?.let { deviceInfo ->
                        deviceMap[deviceId] = Device(
                            deviceInfo = deviceInfo.createDeviceInfo()
                        )
                    }
                }
            }
        }
    }
    return deviceMap
}

private fun createSubDeviceIds(fbsDeviceInfo: FBSDeviceInfo): List<String> {
    val infoList = mutableListOf<String>()
    for (i in 0 until fbsDeviceInfo.subDeviceIdsLength) {
        fbsDeviceInfo.subDeviceIds(i)?.run {
            infoList.add(this)
        }
    }
    return infoList
}

private fun createShareDeviceCardInfoList(fbsDeviceInfo: FBSDeviceInfo): List<ShareDeviceCardInfo> {
    val infoList = mutableListOf<ShareDeviceCardInfo>()
    for (i in 0 until fbsDeviceInfo.shareDeviceCardInfoLength) {
        fbsDeviceInfo.shareDeviceCardInfo(i)?.run {
            val cardInfo = ShareDeviceCardInfo(
                familyId = familyId ?: "",
                cardSort = cardSort.toLong(),
                cardStatus = cardStatus.toLong()
            )
            infoList.add(cardInfo)
        }
    }
    return infoList
}


private fun FBSFamilyInfo.createFamilyInfo() = FamilyInfo(
    familyId = familyId ?: "",
    familyName = familyName ?: "",
    familyPosition = familyPosition,
    createTime = createTime ?: "",
    appId = appId ?: "",
    members = createMembers(this),
    firstMember = firstMember.createMemberInfoOrDefault(),
    owner = owner?.createMemberInfo(),
    ownerId = ownerId ?: "",
    floorInfo = createFloorInfoList(this),
    isDefaultFamily = isDefaultFamily,
    locationChangeFlag = locationChangeFlag,
    totalDeviceCount = totalDeviceCount ?: "0",
    familyLocation = familyLocation.createLocation(),
)

private fun createFamilies(vecFamily: FBSFamilyList): List<Family> {
    val families = mutableListOf<Family>()
    for (i in 0 until vecFamily.familysLength) {
        vecFamily.familys(i)?.familyInfo?.run {
            families.add(
                Family(
                    familyInfo = createFamilyInfo()
                )
            )
        }
    }
    return families
}

private fun createFamilyMap(fbsFamilyMap: FBSFamilyMap): Map<String, Family> {
    val familyMap = mutableMapOf<String, Family>()
    fbsFamilyMap.run {
        for (i in 0 until entriesLength) {
            val familyEntry = entries(i)
            familyEntry?.run {
                key?.let { familyId ->
                    value?.familyInfo?.let { familyInfo ->
                        familyMap[familyId] = Family(
                            familyInfo = familyInfo.createFamilyInfo()
                        )
                    }
                }
            }
        }
    }
    return familyMap
}

private fun FBSDeviceOwnerInfo?.createDeviceOwnerInfo(): DeviceOwnerInfo {
    return if (this == null)
        DeviceOwnerInfo(
            userId = "",
            mobile = "",
            userNickName = "",
            ucUserId = ""
        )
    else
        DeviceOwnerInfo(
            userId = userId ?: "",
            mobile = mobile ?: "",
            userNickName = userNickName ?: "",
            ucUserId = ucUserId ?: ""
        )
}

private fun FBSDevicePermission?.createDevicePermission(): DevicePermission {
    return if (this == null)
        DevicePermission(
            auth = DeviceAuth(
                control = false,
                set = false,
                view = false
            ),
            authType = ""
        )
    else
        DevicePermission(
            auth = DeviceAuth(
                control = auth?.control ?: false,
                set = auth?.set ?: false,
                view = auth?.view ?: false,
            ),
            authType = authType ?: ""
        )

}


private fun createFloorInfoList(fbsFamilyInfo: FBSFamilyInfo): List<FloorInfo> {
    val infoList = mutableListOf<FloorInfo>()
    for (i in 0 until fbsFamilyInfo.floorInfosLength) {
        fbsFamilyInfo.floorInfos(i)?.run {
            val floorInfo = FloorInfo(
                floorName = floorName ?: "",
                floorId = floorId ?: "",
                floorClass = floorClass ?: "",
                floorLabel = floorLabel,
                floorLogo = floorLogo,
                floorPicture = floorPicture,
                floorOrderId = floorOrderId ?: "",
                floorRooms = createRooms(this),
                floorCreateTime = floorCreateTime
            )
            infoList.add(floorInfo)
        }
    }
    return infoList
}

private fun createMembers(fbsFamilyInfo: FBSFamilyInfo): List<FamilyMemberInfo> {
    val infoList = mutableListOf<FamilyMemberInfo>()
    for (i in 0 until fbsFamilyInfo.membersLength) {
        fbsFamilyInfo.members(i)?.run {
            val memberInfo = FamilyMemberInfo(
                familyId = familyId ?: "",
                joinTime = joinTime ?: "",
                memberName = memberName ?: "",
                memberRole = memberRole,
                shareDeviceCount = shareDeviceCount.toInt(),
                memberInfo = createMemberInfo(),
                memberType = memberType.toLong()
            )
            infoList.add(memberInfo)
        }
    }
    return infoList
}

private fun createRooms(fbsFloorInfo: FBSFloorInfo): List<Room> {
    val rooms = mutableListOf<Room>()
    for (i in 0 until fbsFloorInfo.floorRoomsLength) {
        fbsFloorInfo.floorRooms(i)?.run {
            val room = Room(
                roomName = roomName ?: "",
                roomId = roomId ?: "",
                roomClass = roomClass,
                roomLabel = roomLabel,
                roomLogo = roomLogo,
                roomPicture = roomPicture,
                sortCode = sortCode
            )
            rooms.add(room)
        }
    }
    return rooms
}

private fun FBSFamilyMemberInfo.createMemberInfo() =
    MemberInfo(
        iotUserId = memberInfo?.iotUserId ?: "",
        name = memberInfo?.name,
        userAvatar = memberInfo?.userAvatar,
        userMobile = memberInfo?.userMobile,
        ucUserId = memberInfo?.ucUserId,
        hostUserId = memberInfo?.hostUserId,
        userBirthday = memberInfo?.userBirthday,
        isVirtualMember = memberInfo?.isVirtualMember ?: false,
    )

private fun FBSMemberInfo?.createMemberInfoOrDefault() =
    if (this == null)
        MemberInfo(
            iotUserId = "",
            name = null,
            userAvatar = null,
            userMobile = null,
            ucUserId = null,
            hostUserId = null,
            userBirthday = null,
            isVirtualMember = false,
        )
    else
        MemberInfo(
            iotUserId = iotUserId ?: "",
            name = name,
            userAvatar = userAvatar,
            userMobile = userMobile,
            ucUserId = ucUserId,
            hostUserId = hostUserId,
            userBirthday = userBirthday,
            isVirtualMember = isVirtualMember,
        )

private fun FBSMemberInfo.createMemberInfo() =
    MemberInfo(
        iotUserId = iotUserId ?: "",
        name = name,
        userAvatar = userAvatar,
        userMobile = userMobile,
        ucUserId = ucUserId,
        hostUserId = hostUserId,
        userBirthday = userBirthday,
        isVirtualMember = isVirtualMember,
    )

private fun FBSLocation?.createLocation() =
    if (this == null)
        Location(
            longitude = null,
            latitude = null,
            cityCode = null,
        )
    else
        Location(
            longitude = longitude,
            latitude = latitude,
            cityCode = cityCode,
        )

