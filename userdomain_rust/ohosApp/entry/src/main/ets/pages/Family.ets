import { UserDomain } from '@uplus/rust_userdomain/src/main/ets/api/UserDomain';
import { RoomArgs } from '@uplus/rust_userdomain/src/main/ets/api/models/RoomArgs';
import { FamilyArgs, FloorArgs, RoomOrderArgs } from '@uplus/rust_userdomain';
import { Room } from '@uplus/rust_userdomain/src/main/ets/api/models/Room';
import { instanceToPlain } from 'class-transformer';

const TAG = "FamilyPage"

@Entry
@Component
struct FamilyPage {
  build() {
    Scroll() {
      Column({
        space: 20
      }) {
        Button("admin_invite_member").onClick(() => {
          let family = UserDomain.getInstance().getUser().getFamilyById("191340573414000000");
          family.adminInviteMember("2016283552", "PDD", "妹妹", 2).then(data => {
            console.log("adminInviteMember: ", JSON.stringify(instanceToPlain(data)))
          })
        })
        Button("getGroupDeviceList").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .getGroupDeviceList()
            .then((data) => {
              console.log("getGroupDeviceList ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("queryFamilyInfo").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .queryFamilyInfo()
            .then((data) => {
              console.log("queryFamilyInfo ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("queryRoomlist").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .queryRoomList("100000000000000104")
            .then((data) => {
              console.log("queryRoomlist ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("createFloor").onClick(() => {
          let args = new FloorArgs();
          args.floor_id = "123";
          args.floor_name = "测试房间创建";
          args.floor_class = "阳台2";
          args.floor_label = "2";
          args.floor_logo = "no";
          args.floor_picture = "no";
          args.floor_order_id = "3";
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .createFloor(args)
            .then((data) => {
              console.log("createFloor ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("deleteFloor").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .deleteFloor("100000000000000106")
            .then((data) => {
              console.log("deleteFloor ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("modifyFamilyInfo").onClick(() => {
          let familyArgs = new FamilyArgs();
          familyArgs.family_name = "hello"
          familyArgs.position = "北京市 海淀区"
          familyArgs.longitude = "116.222584"
          familyArgs.latitude = "39.905745"
          familyArgs.city_code = "110107"
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("757340735922000000")
            .modifyFamilyInfo(familyArgs)
            .then((data) => {
              console.log("modifyFamilyInfo ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("modifyVirtualMember").onClick(() => {
          let family = UserDomain.getInstance().getUser().getFamilyById("648140648485000000");
          family.modifyVirtualMember("644485", "此乃你所",
            "http://resource.haier.net:80/resource/enabling/fridgegene/bobby/20191206170024220400022.png", true, "")
            .then((data) => {
              console.log("modifyVirtualMember ==", data);
            })
        })
        Button("addRoom").onClick(() => {
          let args = new RoomArgs();
          args.room_name = "ee";
          args.room_class = "卫生间";
          args.floor_order_id = "1";
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .addRoom(args)
            .then(data => {
              console.log("modifyFamilyInfo ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("modifyMemberRole").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .modifyMemberRole("2016283552", "女儿")
            .then((data) => {
              console.log("modifyVirtualMemberRole ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("modifyVirtualMemberRole").onClick(() => {
          let family = UserDomain.getInstance().getUser().getFamilyById("744094271007000000");
          family.modifyVirtualMemberRole("648955", "女儿")
            .then((data) => {
              console.log("modifyVirtualMemberRole ==", data);
            })
        })
        Button("saveRoomOrder").onClick(() => {
          const arg1 = new RoomOrderArgs();
          arg1.floorId = "100000000000000104";
          arg1.rooms = ["卧室", "厨房", "卫生间", "阳台", "洗漱间", "餐厅", "客厅"];
          let roomOrderArgs: RoomOrderArgs[] = [arg1];
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .saveRoomsOrder(roomOrderArgs)
            .then((data) => {
              console.log("saveRoomsOrder ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("removeRoom").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .removeRoom("441340654989000000")
            .then(data => {
              console.log("removeRoom ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("deleteFamilyMember").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .deleteFamilyMember("2016283552")
            .then((data) => {
              console.log("deleteFamilyMember ==", JSON.stringify(instanceToPlain(data)));
            });
        })
        Button("deleteFamilyAsAdmin").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .deleteFamilyAsAdmin()
            .then(data => {
              console.log("deleteFamilyMember ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("getFamilyById").onClick(() => {
          let family = UserDomain.getInstance().getUser().getFamilyById("191340573414000000")
          console.log(TAG, "getFamilyById: ", JSON.stringify(instanceToPlain(family)))
        })
        Button("updateRoomName").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("362340567693000000")
            .updateRoomName("杨柳青-Rust", "100000000000000001")
            .then((data) => {
              console.log(TAG, "updateRoomName ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("getDeviceListByFamilyId").onClick(() => {
          let deviceList = UserDomain.getInstance()
            .getUser()
            .getFamilyById("362340567693000000")
            .getDeviceList();
          console.log(TAG, "getDeviceListByFamilyId: ", JSON.stringify(instanceToPlain(deviceList)))
        })
        Button("unbindDevices").onClick(() => {
          const family = UserDomain.getInstance().getUser().getFamilyById("362340567693000000")
          let deviceList = family.getDeviceList();
          console.log(TAG, "currentDeviceList: ", JSON.stringify(instanceToPlain(deviceList)))
          family.unbindDevices([deviceList[0].deviceInfo]).then((data) => {
            console.log(TAG, "unbindDevices ==", JSON.stringify(data));
          })
        })
        Button("change_family_admin").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("362340567693000000")
            .reassignFamilyAdministrator("2016283552")// 修改创建者
            .then((data) => {
              console.log(TAG, "change_family_admin ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("exitFamilyAsAdmin").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("362340567693000000")
            .exitFamilyAsAdmin("31696856")
            .then((data) => {
              console.log(TAG, "exitFamilyAsAdmin ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("exitFamilyAsMember").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("362340567693000000")
            .exitFamilyAsMember()
            .then((data) => {
              console.log(TAG, "exitFamilyAsMember ==", data);
            })
        })
        Button("queryFirstMember").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .queryFirstMember()
            .then((data) => {
              console.log(TAG, "queryFirstMember ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("addVirtualMember").onClick(() => {
          UserDomain.getInstance()
            .getUser()
            .getFamilyById("191340573414000000")
            .addVirtualMember("cooper", "644650")
            .then((data) => {
              console.log(TAG, "addVirtualMember ==", JSON.stringify(instanceToPlain(data)));
            })
        })
        Button("moveDevicesToOtherRoom").onClick(() => {
          let family = UserDomain.getInstance().getUser().getFamilyById("191340573414000000");
          let room = new Room();
          room.roomId = "100000000000000001"
          let deviceList = family.getDeviceList();
          family.moveDevicesToOtherRoom(room, [deviceList[0].getDeviceInfo()]).then(data => {
            console.log("moveDevicesToOtherRoom ==", JSON.stringify(instanceToPlain(data)));
          })
        })
        Button("moveDevicesToOtherFamily").onClick(() => {
          let family = UserDomain.getInstance().getUser().getFamilyById("191340573414000000");
          let deviceList = family.getDeviceList();
          family.moveDevicesToOtherFamily([deviceList[0].deviceInfo], "605340657573000000").then(data => {
            console.log("moveDevicesToOtherFamily ==", JSON.stringify(instanceToPlain(data)));
          })
        })
      }
    }
    .height('100%')
    .width('100%')
    .scrollable(ScrollDirection.Vertical)
  }
}
