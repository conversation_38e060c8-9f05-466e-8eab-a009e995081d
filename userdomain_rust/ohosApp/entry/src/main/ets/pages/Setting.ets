import { UserDomain } from '@uplus/rust_userdomain/src/main/ets/api/UserDomain';

@Entry
@Component
struct Setting {
  build() {
    Row() {
      Column({ space: 10 }) {
        Button("getHttpRequestRetryDelay").onClick(() => {
          const requestDelay = UserDomain.getInstance().userDomainSetting.getHttpRequestRetryDelay()
          console.log("getHttpRequestRetryDelay: ", requestDelay)
        })
        Button("setHttpRequestRetryDelay").onClick(() => {
          UserDomain.getInstance().userDomainSetting.setHttpRequestRetryDelay(1)
        })
        Button("getUserDomainPlatform").onClick(() => {
          const userDomainPlatform = UserDomain.getInstance().userDomainSetting.getUserDomainPlatform()
          console.log("getUserDomainPlatform: ", userDomainPlatform)
        })
        Button("isRefreshFamilyListEnable").onClick(() => {
          const refreshFamilyListEnable = UserDomain.getInstance().userDomainSetting.isRefreshFamilyListEnable()
          console.log("isRefreshFamilyListEnable: ", refreshFamilyListEnable)
        })
        Button("isRefreshDeviceListEnable").onClick(() => {
          const refreshDeviceListEnable = UserDomain.getInstance().userDomainSetting.isRefreshDeviceListEnable()
          console.log("isRefreshDeviceListEnable: ", refreshDeviceListEnable)
        })
        Button("planRefreshTokenTime").onClick(() => {
          const planRefreshTokenTime = UserDomain.getInstance().userDomainSetting.planRefreshTokenTime()
          console.log("planRefreshTokenTime: ", planRefreshTokenTime)
        })
      }
      .width('100%')
    }
    .height('100%')
  }
}