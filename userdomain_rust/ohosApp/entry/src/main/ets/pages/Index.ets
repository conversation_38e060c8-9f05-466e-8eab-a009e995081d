import {
  AuthData,
  Device,
  Family,
  UserDomainEventData,
  UserDomainPlatform,
  UserInfo
} from '@uplus/rust_userdomain/Index';
import { AppNetworkEnv, UserDomain, UserDomainEvent } from '@uplus/rust_userdomain/src/main/ets/api/UserDomain';
import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import { RustStorage } from '@uplus/rust_storage';
import { instanceToPlain } from 'class-transformer';

const TAG = "IndexPage"

export enum MIMETypes {
  IMAGE_TYPE = 'image/*',
  VIDEO_TYPE = 'video/*'
}

@Entry
@Component
struct Index {
  @State observer_id: string = ""

  build() {
    RelativeContainer() {
      Scroll() {
        Column({ space: 10 }) {
          Row({ space: 10 }) {
            Button("User")
              .backgroundColor(Color.Black)
              .fontColor(Color.Yellow)
              .onClick(() => {
                router.pushUrl({
                  url: "pages/User",
                  params: {}
                }, router.RouterMode.Single)
              })
            Button("Family")
              .backgroundColor(Color.Black)
              .fontColor(Color.Yellow)
              .onClick(() => {
                router.pushUrl({
                  url: "pages/Family",
                  params: {}
                }, router.RouterMode.Single)
              })
            Button("Device")
              .backgroundColor(Color.Black)
              .fontColor(Color.Yellow)
              .onClick(() => {
                router.pushUrl({
                  url: "pages/Device",
                  params: {}
                }, router.RouterMode.Single)
              })
            Button("Setting")
              .backgroundColor(Color.Black)
              .fontColor(Color.Yellow)
              .onClick(() => {
                router.pushUrl({
                  url: "pages/Setting",
                  params: {}
                }, router.RouterMode.Single)
              })
          }

          Button("updateOauthData").onClick(() => {
            let isUpdate = UserDomain.getInstance().updateOauthData(
              "c54ea4d1e75043e48ad2d1f1633cb263",
              "7094b6e5f4374665bef0fd2d53617626",
              "c54ea4d1e75043e48ad2d1f1633cb263",
              863999,
              "",
              "bearer",
              "1448784735",
              "1448784735");
            console.log(TAG, "updateOauthData: ", isUpdate);
          })
          Button("getOauthData").onClick(() => {
            let oauthData = UserDomain.getInstance().getOauthData();
            console.log(TAG, "getAuthData: ", JSON.stringify(instanceToPlain(oauthData)));
          })
          Button("state").onClick(() => {
            let state = UserDomain.getInstance().state();
            console.log(TAG, "state: ", state);
          })
          Button("cancelLogin").onClick(() => {
            UserDomain.getInstance().cancelLogin();
          })
          Button("isRefreshCompleted").onClick(() => {
            let result = UserDomain.getInstance().isRefreshCompleted();
            console.log(TAG, "isRefreshCompleted ==", result);
          })
          Button("isRefreshDeviceListCompleted").onClick(() => {
            let result = UserDomain.getInstance().isRefreshDeviceListCompleted();
            console.log(TAG, "isRefreshDeviceListCompleted ==", result);
            promptAction.showToast({
              message: '刷新设备列表' + result ? "success" : "failed",
              duration: 1600,
              bottom: 150
            })
          })
          Button("isRefreshFamilyListCompleted").onClick(() => {
            let result = UserDomain.getInstance().isRefreshFamilyListCompleted();
            console.log(TAG, "isRefreshFamilyListCompleted ==", result);
            promptAction.showToast({
              message: '刷新家庭列表' + result ? "success" : "failed",
              duration: 1600,
              bottom: 150
            })
          })
          Button("logout").onClick(() => {
            console.log("logout");
            UserDomain.getInstance().logout().then((data) => {
              console.log(TAG, "logout: ", JSON.stringify(instanceToPlain(data)))
            })
          })
          Button("removeObserver").onClick(() => {
            UserDomain.getInstance().removeObserver(this.observer_id)
            console.log("removeObserver")
          })
        }
      }
      .height('100%')
      .width('100%')
      .scrollable(ScrollDirection.Vertical)
    }
  }

  aboutToAppear(): void {
    RustStorage.initRustStorage(getContext(this));
    UserDomain.getInstance().init(
      "MB-ISPHEZJHMB-0000",
      "a540098f99a2a6b16428cbfbcc9372a5",
      "9.7.0",
      "693bec2ff97a8e864cfa75cfcc357254",
      AppNetworkEnv.Production,
      false,
      UserDomainPlatform.Harmony,
      true
    );
    UserDomain.getInstance().autoRefreshToken();

    this.observer_id = UserDomain.getInstance().addObserver((eventType, eventData) => {
      this.handleEventData(eventType, eventData)
    })
    // const observer2 = UserDomain.getInstance().addObserver(this.userObserver)
    // const observer3 = UserDomain.getInstance().addObserver(this.userObserver)
    // console.log(TAG, "addObserver: ", this.observer_id, observer2, observer3)
  }

  // private userObserver = (eventType: UserDomainEvent, eventData: UserDomainEventData) => {
  //   console.log("UserDomainEvent2", eventType, eventData)
  // }

  private handleEventData(eventType: UserDomainEvent, eventData: UserDomainEventData) {
    switch (eventType) {
      case UserDomainEvent.MessageRefreshTokenSuccess:
        const authData = eventData as AuthData
        console.log(TAG, eventType, JSON.stringify(instanceToPlain(authData)));
        break;
      case UserDomainEvent.MessageUserInfoRefreshSuccess:
        const userInfo = eventData as UserInfo
        console.log(TAG, eventType, JSON.stringify(instanceToPlain(userInfo)));
        break;
      case UserDomainEvent.MessageDeviceListRefreshSuccess:
        const devices = eventData as Array<Device>
        console.log(TAG, eventType, JSON.stringify(instanceToPlain(devices)));
        break;
      case UserDomainEvent.MessageFamilyListRefreshSuccess:
        const families = eventData as Array<Family>
        console.log(TAG, eventType, JSON.stringify(instanceToPlain(families)));
        break;
      case UserDomainEvent.MessageCurrentFamilyChanged:
        const family = eventData as Family
        console.log(TAG, eventType, JSON.stringify(instanceToPlain(family)));
        break;
      case UserDomainEvent.MessageRefreshTokenFailed:
      case UserDomainEvent.MessageTokenInvalid:
      case UserDomainEvent.MessageTokenMismatchDevice:
      case UserDomainEvent.MessageLogout:
      case UserDomainEvent.MessageLoginElseWhere:
      case UserDomainEvent.MessageRefreshTokenFailed:
      case UserDomainEvent.MessageRefreshFailure:
      case UserDomainEvent.MessageUserInfoRefreshFailed:
      case UserDomainEvent.MessageDeviceListRefreshFailed:
      case UserDomainEvent.MessageFamilyListRefreshFailed:
        console.log(TAG, eventType);
        break;
      default:
        break;
    }
  }
}
