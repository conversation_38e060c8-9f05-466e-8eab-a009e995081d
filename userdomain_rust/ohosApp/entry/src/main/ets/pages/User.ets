import { Address<PERSON>rgs, CreateF<PERSON><PERSON><PERSON><PERSON><PERSON>rgs, FamilyArgs, UserDomain } from '@uplus/rust_userdomain'
import { UserArgs } from '@uplus/rust_userdomain/src/main/ets/api/models/UserArgs'
import { instanceToPlain } from 'class-transformer'
import { photoAccessHelper } from '@kit.MediaLibraryKit'
import { picker } from '@kit.CoreFileKit'

const TAG = "UserPage"

@Entry
@Component
struct UserPage {
  @State imgDataArr: string[] = [];

  build() {
    RelativeContainer() {
      Scroll() {
        Column({ space: 10 }) {
          Button("refreshUserInfo").onClick(() => {
            UserDomain.getInstance().getUser().refreshUserInfo().then((data) => {
              console.log(TAG, "refreshUserInfo: ", JSON.stringify(instanceToPlain(data)));
            })
          })
          But<PERSON>("refreshUser").onClick(() => {
            UserDomain.getInstance().getUser().refreshUser().then((result) => {
              console.log(TAG, "refreshUser: ", JSON.stringify(instanceToPlain(result)));
            })
          })
          Button("modifyUserInfo").onClick(() => {
            let user = new UserArgs();
            user.userId = '**********';
            user.givenName = 'John Doe';
            user.nickname = '123312';
            user.familyNum = '';
            user.gender = '';
            user.marriage = '';
            user.birthday = '1991-01-01';
            user.education = '';
            user.avatarUrl =
              'https://accountstatic.haier.com/************/8f4c41784b3cd9932e6cadee6e8d41ed/avatar/**********-1376865478333210624.jpeg';
            user.extraPhone = '**********';
            user.income = '';
            user.height = '183';
            user.weight = '75';
            user.privacyCountryCode = '';
            UserDomain.getInstance().getUser().modifyUserInfo(user).then((data) => {
              console.log("modifyUserInfo", data)
            })
          })
          Button("refreshDeviceList").onClick(() => {
            UserDomain.getInstance().getUser().refreshDeviceList().then((data) => {
              console.log(TAG, "refreshDeviceList ==", JSON.stringify(instanceToPlain(data)));
            })
          })
          Button("getDevicesMap").onClick(() => {
            let map = UserDomain.getInstance().getUser().getDevicesMap();
            console.log(TAG, "getDevicesMap", JSON.stringify(instanceToPlain(map)))
          })
          Button("getDeviceById").onClick(() => {
            let device = UserDomain.getInstance().getUser().getDeviceById("VMHT00001")
            console.log(TAG, "getDeviceById", JSON.stringify(instanceToPlain(device)))
          })
          Button("refreshFamilyList").onClick(() => {
            UserDomain.getInstance().getUser().refreshFamilyList().then((data) => {
              console.log(TAG, "refreshFamilyList ==", JSON.stringify(instanceToPlain(data)));
            })
          })
          Button("refreshAddressList").onClick(() => {
            UserDomain.getInstance().getUser().refreshAddressList().then((data) => {
              console.log(TAG, "RefreshFamilyList ==", JSON.stringify(instanceToPlain(data)));
            })
          })
          Button("reply_family_invite").onClick(() => {
            UserDomain.getInstance()
              .getUser()
              .replyFamilyInvite("859583", "640247950689000000", "好兄弟", true)
              .then((data) => {
                console.log(TAG, "replyFamilyInvite: ", JSON.stringify(instanceToPlain(data)))
              })
          })
          Button("setCurrentFamily").onClick(() => {
            UserDomain.getInstance()
              .getUser()
              .setCurrentFamily("191340573414000000")
              .then((result) => {
                console.log(TAG, "setCurrentFamily: ", JSON.stringify(instanceToPlain(result)))
              })
          })
          Button("getCurrentFamily").onClick(() => {
            let family = UserDomain.getInstance().getUser().getCurrentFamily()
            console.log(TAG, "getCurrentFamily", JSON.stringify(instanceToPlain(family)))
          })
          Button("getUserInfo").onClick(() => {
            let userinfo = UserDomain.getInstance().getUser().getUserInfo()
            console.log(TAG, "getUserInfo: ", JSON.stringify(instanceToPlain(userinfo)))
          })
          Button("modifyUserAvatar").onClick(() => {
            this.selectFile().then((data) => {
              UserDomain.getInstance()
                .getUser()
                .modifyUserAvatar(data)
                .then((data) => {
                  console.log(TAG, "modifyUserAvatar ==", JSON.stringify(instanceToPlain(data)));
                });
            })
          })
          Button("createFamily").onClick(() => {
            let args = new FamilyArgs();
            args.city_code = "370212"
            args.latitude = "36.130337"
            args.longitude = "120.420222"
            args.family_name = "bb4"
            args.position = "青岛市 崂山区"
            args.room_names = new Array()
            let roomArgs = new CreateFamilyRoomArgs()
            roomArgs.room_class = "卫生间"
            roomArgs.room_name = "egg1"
            args.room_names.push(roomArgs)
            roomArgs = new CreateFamilyRoomArgs()
            roomArgs.room_class = "卫生间"
            roomArgs.room_name = "egg2"
            args.room_names.push(roomArgs)
            roomArgs = new CreateFamilyRoomArgs()
            roomArgs.room_class = "卫生间"
            roomArgs.room_name = "egg3"
            args.room_names.push(roomArgs)
            UserDomain.getInstance()
              .getUser()
              .createFamily(args)
              .then(data => {
                console.log(TAG, "createAddress: ", JSON.stringify(instanceToPlain(data)))
              })
          })
          Button("createAddress").onClick(() => {
            let args = new AddressArgs();
            args.city = "青岛";
            args.cityId = "370200";
            args.countryCode = "CN";
            args.district = "市南区";
            args.districtId = "370212";
            args.line1 = "海尔路1号";
            args.line2 = "软件园区";
            args.postcode = "266100";
            args.province = "山东省";
            args.provinceId = "370000";
            args.town = "崂山区";
            args.townId = "370200";
            args.email = "<EMAIL>";
            args.addressId = "ADDR001";
            args.isDefault = false;
            args.isService = false;
            args.reviceMobile = "13800138000";
            args.reviceName = "张三";
            args.source = "rust";
            args.tag = "家";
            args.userId = "USER123";
            UserDomain.getInstance()
              .getUser()
              .createAddress(args)
              .then(data => {
                console.log(TAG, "createAddress: ", JSON.stringify(instanceToPlain(data)))
              })
          })
          Button("editAddress").onClick(() => {
            let args = new AddressArgs();
            args.city = "宁波";
            args.cityId = "370200";
            args.countryCode = "CN";
            args.district = "大泽山";
            args.districtId = "370212";
            args.line1 = "海尔路100号";
            args.line2 = "软件园区";
            args.postcode = "266100";
            args.province = "山东省";
            args.provinceId = "370000";
            args.town = "崂山区";
            args.townId = "370200";
            args.email = "<EMAIL>";
            args.addressId = "130888063";
            args.isDefault = false;
            args.isService = false;
            args.reviceMobile = "13800138000";
            args.reviceName = "李四";
            args.source = "rust";
            args.tag = "家";
            args.userId = "USER123";
            UserDomain.getInstance()
              .getUser()
              .editAddress(args)
              .then(data => {
                console.log(TAG, "deleteAddress: ", JSON.stringify(instanceToPlain(data)))
              })
          })
          Button("deleteAddress").onClick(() => {
            UserDomain.getInstance()
              .getUser()
              .deleteAddress("130866863")
              .then(data => {
                console.log(TAG, "deleteAddress: ", JSON.stringify(instanceToPlain(data)))
              })
          })
          Button("getDefaultAddress").onClick(() => {
            let addressInfo = UserDomain.getInstance().getUser().getDefaultAddress()
            console.log(TAG, "getDefaultAddress: ", JSON.stringify(instanceToPlain(addressInfo)))
          })
          Button("getAddressList").onClick(() => {
            let addresses = UserDomain.getInstance().getUser().getAddressList()
            console.log(TAG, "getAddressList: ", JSON.stringify(instanceToPlain(addresses)))
          })

          Button("getFamilysMap").onClick(() => {
            let map = UserDomain.getInstance().getUser().getFamilysMap();
            console.log(TAG, "getFamilysMap: ", JSON.stringify(instanceToPlain(map)));
          })
        }
      }
      .height('100%')
      .width('100%')
      .scrollable(ScrollDirection.Vertical)
    }
  }

  async selectFile(): Promise<string> {
    let photoSelectOptions = new photoAccessHelper.PhotoSelectOptions();
    let photoPicker = new photoAccessHelper.PhotoViewPicker();
    // 过滤选择媒体文件类型为IMAGE
    photoSelectOptions.MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE;
    // 设置最大选择数量
    photoSelectOptions.maxSelectNumber = 1;
    let chooseFile: picker.PhotoSelectResult = await photoPicker.select(photoSelectOptions);
    // 获取选择的文件列表
    this.imgDataArr = chooseFile.photoUris;
    return chooseFile.photoUris[0];
  }
}