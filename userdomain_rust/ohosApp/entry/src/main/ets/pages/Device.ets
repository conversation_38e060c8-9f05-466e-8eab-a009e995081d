import { Device, DeviceInfo, UserDomain } from '@uplus/rust_userdomain'
import { instanceToPlain } from 'class-transformer'

@Entry
@Component
struct DevicePage {
  build() {
    RelativeContainer() {
      Scroll() {
        Column({ space: 10 }) {
          Button("updateDeviceName").onClick(() => {
            UserDomain.getInstance()
              .getUser()
              .getFamilyById("605340657573000000")
              .getDeviceList()[0]
              .updateDeviceName("智能体脂秤-Test")
              .then((data) => {
                console.log("updateDeviceName: ", JSON.stringify(instanceToPlain(data)))
              })
          })
          Button("updateDeviceNameAndCheck").onClick(() => {
            UserDomain.getInstance()
              .getUser()
              .getFamilyById("605340657573000000")
              .getDeviceList()[0]
              .updateDeviceNameAndCheck("智能体脂秤-Test2", "1", true)
              .then((data) => {
                console.log("updateDeviceNameAndCheck: ", JSON.stringify(instanceToPlain(data)))
              })
          })
        }
      }
      .height('100%')
      .width('100%')
      .scrollable(ScrollDirection.Vertical)
    }
  }
}