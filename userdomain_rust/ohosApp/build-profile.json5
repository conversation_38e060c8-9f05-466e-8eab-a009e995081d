{"app": {"products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.2(14)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}], "signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default_ohosApp_YUOdFDwGRz1IcnYJSqdF2fdItDqp2Kh-0xJ-0xgRq9I=.cer", "keyAlias": "debugKey", "keyPassword": "0000001B5D1042E3CF2042DDC68A04218B451B1D0E3920F5DB2453E357DF04A575F4D1EABCD3CEF1AC48B4", "profile": "/Users/<USER>/.ohos/config/default_ohosApp_YUOdFDwGRz1IcnYJSqdF2fdItDqp2Kh-0xJ-0xgRq9I=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default_ohosApp_YUOdFDwGRz1IcnYJSqdF2fdItDqp2Kh-0xJ-0xgRq9I=.p12", "storePassword": "0000001B0671F7235C7A14111CD8B1265950510E3262AB6BD33EFA3A30CE6511BB24581D626735C0480459"}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "rust_userdomain", "srcPath": "./rust_userdomain", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}