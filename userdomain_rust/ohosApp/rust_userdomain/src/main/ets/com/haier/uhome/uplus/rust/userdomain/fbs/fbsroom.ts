// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

export class FBSRoom {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):FBSRoom {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsFBSRoom(bb:flatbuffers.ByteBuffer, obj?:FBSRoom):FBSRoom {
  return (obj || new FBSRoom()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsFBSRoom(bb:flatbuffers.ByteBuffer, obj?:FBSRoom):FBSRoom {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new FBSRoom()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

roomName():string|null
roomName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomId():string|null
roomId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomClass():string|null
roomClass(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomClass(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomLabel():string|null
roomLabel(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomLabel(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomLogo():string|null
roomLogo(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomLogo(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomPicture():string|null
roomPicture(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomPicture(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 14);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

sortCode():string|null
sortCode(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
sortCode(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 16);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

floorOrderId():string|null
floorOrderId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
floorOrderId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 18);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

floorId():string|null
floorId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
floorId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 20);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

static startFBSRoom(builder:flatbuffers.Builder) {
  builder.startObject(9);
}

static addRoomName(builder:flatbuffers.Builder, roomNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(0, roomNameOffset, 0);
}

static addRoomId(builder:flatbuffers.Builder, roomIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, roomIdOffset, 0);
}

static addRoomClass(builder:flatbuffers.Builder, roomClassOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, roomClassOffset, 0);
}

static addRoomLabel(builder:flatbuffers.Builder, roomLabelOffset:flatbuffers.Offset) {
  builder.addFieldOffset(3, roomLabelOffset, 0);
}

static addRoomLogo(builder:flatbuffers.Builder, roomLogoOffset:flatbuffers.Offset) {
  builder.addFieldOffset(4, roomLogoOffset, 0);
}

static addRoomPicture(builder:flatbuffers.Builder, roomPictureOffset:flatbuffers.Offset) {
  builder.addFieldOffset(5, roomPictureOffset, 0);
}

static addSortCode(builder:flatbuffers.Builder, sortCodeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(6, sortCodeOffset, 0);
}

static addFloorOrderId(builder:flatbuffers.Builder, floorOrderIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(7, floorOrderIdOffset, 0);
}

static addFloorId(builder:flatbuffers.Builder, floorIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(8, floorIdOffset, 0);
}

static endFBSRoom(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createFBSRoom(builder:flatbuffers.Builder, roomNameOffset:flatbuffers.Offset, roomIdOffset:flatbuffers.Offset, roomClassOffset:flatbuffers.Offset, roomLabelOffset:flatbuffers.Offset, roomLogoOffset:flatbuffers.Offset, roomPictureOffset:flatbuffers.Offset, sortCodeOffset:flatbuffers.Offset, floorOrderIdOffset:flatbuffers.Offset, floorIdOffset:flatbuffers.Offset):flatbuffers.Offset {
  FBSRoom.startFBSRoom(builder);
  FBSRoom.addRoomName(builder, roomNameOffset);
  FBSRoom.addRoomId(builder, roomIdOffset);
  FBSRoom.addRoomClass(builder, roomClassOffset);
  FBSRoom.addRoomLabel(builder, roomLabelOffset);
  FBSRoom.addRoomLogo(builder, roomLogoOffset);
  FBSRoom.addRoomPicture(builder, roomPictureOffset);
  FBSRoom.addSortCode(builder, sortCodeOffset);
  FBSRoom.addFloorOrderId(builder, floorOrderIdOffset);
  FBSRoom.addFloorId(builder, floorIdOffset);
  return FBSRoom.endFBSRoom(builder);
}
}
