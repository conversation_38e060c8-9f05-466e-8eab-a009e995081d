// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

import { FBSDeviceOwnerInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsdevice-owner-info.js';
import { FBSDevicePermission } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsdevice-permission.js';
import { FBSShareDeviceCardInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsshare-device-card-info.js';


export class FBSDeviceInfo {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):FBSDeviceInfo {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsFBSDeviceInfo(bb:flatbuffers.ByteBuffer, obj?:FBSDeviceInfo):FBSDeviceInfo {
  return (obj || new FBSDeviceInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsFBSDeviceInfo(bb:flatbuffers.ByteBuffer, obj?:FBSDeviceInfo):FBSDeviceInfo {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new FBSDeviceInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

deviceId():string|null
deviceId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceName():string|null
deviceName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

devName():string|null
devName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
devName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceType():string|null
deviceType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

familyId():string|null
familyId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
familyId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

ownerId():string|null
ownerId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
ownerId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 14);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

permission(obj?:FBSDevicePermission):FBSDevicePermission|null {
  const offset = this.bb!.__offset(this.bb_pos, 16);
  return offset ? (obj || new FBSDevicePermission()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
}

wifiType():string|null
wifiType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
wifiType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 18);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceNetType():string|null
deviceNetType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceNetType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 20);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

bindTime():string|null
bindTime(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
bindTime(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 22);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

isOnline():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 24);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

ownerInfo(obj?:FBSDeviceOwnerInfo):FBSDeviceOwnerInfo|null {
  const offset = this.bb!.__offset(this.bb_pos, 26);
  return offset ? (obj || new FBSDeviceOwnerInfo()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
}

subDeviceIds(index: number):string
subDeviceIds(index: number,optionalEncoding:flatbuffers.Encoding):string|Uint8Array
subDeviceIds(index: number,optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 28);
  return offset ? this.bb!.__string(this.bb!.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
}

subDeviceIdsLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 28);
  return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
}

parentId():string|null
parentId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
parentId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 30);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceRole():string|null
deviceRole(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceRole(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 32);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceRoleType():string|null
deviceRoleType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceRoleType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 34);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

apptypeName():string|null
apptypeName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
apptypeName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 36);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

apptypeCode():string|null
apptypeCode(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
apptypeCode(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 38);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

categoryGrouping():string|null
categoryGrouping(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
categoryGrouping(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 40);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

barcode():string|null
barcode(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
barcode(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 42);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

bindType():string|null
bindType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
bindType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 44);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

brand():string|null
brand(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
brand(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 46);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

imageAddr1():string|null
imageAddr1(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
imageAddr1(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 48);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

cardPageImg():string|null
cardPageImg(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
cardPageImg(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 50);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

cardSort():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 52);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

cardStatus():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 54);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

aggregationParentId():string|null
aggregationParentId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
aggregationParentId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 56);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

supportAggregationFlag():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 58);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

deviceAggregateType():string|null
deviceAggregateType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceAggregateType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 60);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

model():string|null
model(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
model(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 62);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

prodNo():string|null
prodNo(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
prodNo(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 64);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomName():string|null
roomName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 66);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

roomId():string|null
roomId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
roomId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 68);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

accessType():string|null
accessType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
accessType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 70);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

configType():string|null
configType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
configType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 72);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

communicationMode():string|null
communicationMode(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
communicationMode(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 74);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceFloorId():string|null
deviceFloorId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceFloorId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 76);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceFloorOrderId():string|null
deviceFloorOrderId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceFloorOrderId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 78);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceFloorName():string|null
deviceFloorName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceFloorName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 80);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

apptypeIcon():string|null
apptypeIcon(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
apptypeIcon(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 82);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceGroupId():string|null
deviceGroupId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceGroupId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 84);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceGroupType():string|null
deviceGroupType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceGroupType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 86);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

noKeepAlive():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 88);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

twoGropingName():string|null
twoGropingName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
twoGropingName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 90);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

supportFlag():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 92);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

sharedDeviceFlag():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 94);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

shareDeviceCardInfo(index: number, obj?:FBSShareDeviceCardInfo):FBSShareDeviceCardInfo|null {
  const offset = this.bb!.__offset(this.bb_pos, 96);
  return offset ? (obj || new FBSShareDeviceCardInfo()).__init(this.bb!.__indirect(this.bb!.__vector(this.bb_pos + offset) + index * 4), this.bb!) : null;
}

shareDeviceCardInfoLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 96);
  return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
}

attachmentSortCode():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 98);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

deviceShareSupportFlag():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 100);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

rebind():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 102);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

static startFBSDeviceInfo(builder:flatbuffers.Builder) {
  builder.startObject(50);
}

static addDeviceId(builder:flatbuffers.Builder, deviceIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(0, deviceIdOffset, 0);
}

static addDeviceName(builder:flatbuffers.Builder, deviceNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, deviceNameOffset, 0);
}

static addDevName(builder:flatbuffers.Builder, devNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, devNameOffset, 0);
}

static addDeviceType(builder:flatbuffers.Builder, deviceTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(3, deviceTypeOffset, 0);
}

static addFamilyId(builder:flatbuffers.Builder, familyIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(4, familyIdOffset, 0);
}

static addOwnerId(builder:flatbuffers.Builder, ownerIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(5, ownerIdOffset, 0);
}

static addPermission(builder:flatbuffers.Builder, permissionOffset:flatbuffers.Offset) {
  builder.addFieldOffset(6, permissionOffset, 0);
}

static addWifiType(builder:flatbuffers.Builder, wifiTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(7, wifiTypeOffset, 0);
}

static addDeviceNetType(builder:flatbuffers.Builder, deviceNetTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(8, deviceNetTypeOffset, 0);
}

static addBindTime(builder:flatbuffers.Builder, bindTimeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(9, bindTimeOffset, 0);
}

static addIsOnline(builder:flatbuffers.Builder, isOnline:boolean) {
  builder.addFieldInt8(10, +isOnline, +false);
}

static addOwnerInfo(builder:flatbuffers.Builder, ownerInfoOffset:flatbuffers.Offset) {
  builder.addFieldOffset(11, ownerInfoOffset, 0);
}

static addSubDeviceIds(builder:flatbuffers.Builder, subDeviceIdsOffset:flatbuffers.Offset) {
  builder.addFieldOffset(12, subDeviceIdsOffset, 0);
}

static createSubDeviceIdsVector(builder:flatbuffers.Builder, data:flatbuffers.Offset[]):flatbuffers.Offset {
  builder.startVector(4, data.length, 4);
  for (let i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]!);
  }
  return builder.endVector();
}

static startSubDeviceIdsVector(builder:flatbuffers.Builder, numElems:number) {
  builder.startVector(4, numElems, 4);
}

static addParentId(builder:flatbuffers.Builder, parentIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(13, parentIdOffset, 0);
}

static addDeviceRole(builder:flatbuffers.Builder, deviceRoleOffset:flatbuffers.Offset) {
  builder.addFieldOffset(14, deviceRoleOffset, 0);
}

static addDeviceRoleType(builder:flatbuffers.Builder, deviceRoleTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(15, deviceRoleTypeOffset, 0);
}

static addApptypeName(builder:flatbuffers.Builder, apptypeNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(16, apptypeNameOffset, 0);
}

static addApptypeCode(builder:flatbuffers.Builder, apptypeCodeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(17, apptypeCodeOffset, 0);
}

static addCategoryGrouping(builder:flatbuffers.Builder, categoryGroupingOffset:flatbuffers.Offset) {
  builder.addFieldOffset(18, categoryGroupingOffset, 0);
}

static addBarcode(builder:flatbuffers.Builder, barcodeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(19, barcodeOffset, 0);
}

static addBindType(builder:flatbuffers.Builder, bindTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(20, bindTypeOffset, 0);
}

static addBrand(builder:flatbuffers.Builder, brandOffset:flatbuffers.Offset) {
  builder.addFieldOffset(21, brandOffset, 0);
}

static addImageAddr1(builder:flatbuffers.Builder, imageAddr1Offset:flatbuffers.Offset) {
  builder.addFieldOffset(22, imageAddr1Offset, 0);
}

static addCardPageImg(builder:flatbuffers.Builder, cardPageImgOffset:flatbuffers.Offset) {
  builder.addFieldOffset(23, cardPageImgOffset, 0);
}

static addCardSort(builder:flatbuffers.Builder, cardSort:bigint) {
  builder.addFieldInt64(24, cardSort, BigInt('0'));
}

static addCardStatus(builder:flatbuffers.Builder, cardStatus:bigint) {
  builder.addFieldInt64(25, cardStatus, BigInt('0'));
}

static addAggregationParentId(builder:flatbuffers.Builder, aggregationParentIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(26, aggregationParentIdOffset, 0);
}

static addSupportAggregationFlag(builder:flatbuffers.Builder, supportAggregationFlag:bigint) {
  builder.addFieldInt64(27, supportAggregationFlag, BigInt('0'));
}

static addDeviceAggregateType(builder:flatbuffers.Builder, deviceAggregateTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(28, deviceAggregateTypeOffset, 0);
}

static addModel(builder:flatbuffers.Builder, modelOffset:flatbuffers.Offset) {
  builder.addFieldOffset(29, modelOffset, 0);
}

static addProdNo(builder:flatbuffers.Builder, prodNoOffset:flatbuffers.Offset) {
  builder.addFieldOffset(30, prodNoOffset, 0);
}

static addRoomName(builder:flatbuffers.Builder, roomNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(31, roomNameOffset, 0);
}

static addRoomId(builder:flatbuffers.Builder, roomIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(32, roomIdOffset, 0);
}

static addAccessType(builder:flatbuffers.Builder, accessTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(33, accessTypeOffset, 0);
}

static addConfigType(builder:flatbuffers.Builder, configTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(34, configTypeOffset, 0);
}

static addCommunicationMode(builder:flatbuffers.Builder, communicationModeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(35, communicationModeOffset, 0);
}

static addDeviceFloorId(builder:flatbuffers.Builder, deviceFloorIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(36, deviceFloorIdOffset, 0);
}

static addDeviceFloorOrderId(builder:flatbuffers.Builder, deviceFloorOrderIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(37, deviceFloorOrderIdOffset, 0);
}

static addDeviceFloorName(builder:flatbuffers.Builder, deviceFloorNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(38, deviceFloorNameOffset, 0);
}

static addApptypeIcon(builder:flatbuffers.Builder, apptypeIconOffset:flatbuffers.Offset) {
  builder.addFieldOffset(39, apptypeIconOffset, 0);
}

static addDeviceGroupId(builder:flatbuffers.Builder, deviceGroupIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(40, deviceGroupIdOffset, 0);
}

static addDeviceGroupType(builder:flatbuffers.Builder, deviceGroupTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(41, deviceGroupTypeOffset, 0);
}

static addNoKeepAlive(builder:flatbuffers.Builder, noKeepAlive:boolean) {
  builder.addFieldInt8(42, +noKeepAlive, +false);
}

static addTwoGropingName(builder:flatbuffers.Builder, twoGropingNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(43, twoGropingNameOffset, 0);
}

static addSupportFlag(builder:flatbuffers.Builder, supportFlag:bigint) {
  builder.addFieldInt64(44, supportFlag, BigInt('0'));
}

static addSharedDeviceFlag(builder:flatbuffers.Builder, sharedDeviceFlag:bigint) {
  builder.addFieldInt64(45, sharedDeviceFlag, BigInt('0'));
}

static addShareDeviceCardInfo(builder:flatbuffers.Builder, shareDeviceCardInfoOffset:flatbuffers.Offset) {
  builder.addFieldOffset(46, shareDeviceCardInfoOffset, 0);
}

static createShareDeviceCardInfoVector(builder:flatbuffers.Builder, data:flatbuffers.Offset[]):flatbuffers.Offset {
  builder.startVector(4, data.length, 4);
  for (let i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]!);
  }
  return builder.endVector();
}

static startShareDeviceCardInfoVector(builder:flatbuffers.Builder, numElems:number) {
  builder.startVector(4, numElems, 4);
}

static addAttachmentSortCode(builder:flatbuffers.Builder, attachmentSortCode:bigint) {
  builder.addFieldInt64(47, attachmentSortCode, BigInt('0'));
}

static addDeviceShareSupportFlag(builder:flatbuffers.Builder, deviceShareSupportFlag:boolean) {
  builder.addFieldInt8(48, +deviceShareSupportFlag, +false);
}

static addRebind(builder:flatbuffers.Builder, rebind:bigint) {
  builder.addFieldInt64(49, rebind, BigInt('0'));
}

static endFBSDeviceInfo(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

}
