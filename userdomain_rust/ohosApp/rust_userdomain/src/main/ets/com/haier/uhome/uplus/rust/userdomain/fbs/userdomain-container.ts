// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import { <PERSON><PERSON>Wrapper } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/bool-wrapper.js';
import { FBSAddressInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsaddress-info.js';
import { FBSAddressInfoList } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsaddress-info-list.js';
import { FBSAuthData } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsauth-data.js';
import { FBSDevice } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsdevice.js';
import { FBSDeviceList } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsdevice-list.js';
import { FBSDeviceMap } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsdevice-map.js';
import { FBSDeviceOperationResult } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsdevice-operation-result.js';
import { FBSFamily } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsfamily.js';
import { FBSFamilyInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsfamily-info.js';
import { FBSFamilyList } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsfamily-list.js';
import { FBSFamilyMap } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsfamily-map.js';
import { FBSFamilyMemberInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsfamily-member-info.js';
import { FBSRoom } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsroom.js';
import { FBSRoomList } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsroom-list.js';
import { FBSUserInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsuser-info.js';
import { Int32Wrapper } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/int32-wrapper.js';
import { NoneWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/none-wrapper.js';
import { StrWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/str-wrapper.js';


export enum UserdomainContainer {
  NONE = 0,
  Int32Wrapper = 1,
  StrWrapper = 2,
  BoolWrapper = 3,
  NoneWrapper = 4,
  FBSDevice = 5,
  FBSDeviceMap = 6,
  FBSDeviceList = 7,
  FBSDeviceOperationResult = 8,
  FBSFamilyInfo = 9,
  FBSRoom = 10,
  FBSRoomList = 11,
  FBSFamilyMemberInfo = 12,
  FBSFamily = 13,
  FBSFamilyMap = 14,
  FBSFamilyList = 15,
  FBSUserInfo = 16,
  FBSAddressInfo = 17,
  FBSAddressInfoList = 18,
  FBSAuthData = 19
}

export function unionToUserdomainContainer(
  type: UserdomainContainer,
  accessor: (obj:BoolWrapper|FBSAddressInfo|FBSAddressInfoList|FBSAuthData|FBSDevice|FBSDeviceList|FBSDeviceMap|FBSDeviceOperationResult|FBSFamily|FBSFamilyInfo|FBSFamilyList|FBSFamilyMap|FBSFamilyMemberInfo|FBSRoom|FBSRoomList|FBSUserInfo|Int32Wrapper|NoneWrapper|StrWrapper) => BoolWrapper|FBSAddressInfo|FBSAddressInfoList|FBSAuthData|FBSDevice|FBSDeviceList|FBSDeviceMap|FBSDeviceOperationResult|FBSFamily|FBSFamilyInfo|FBSFamilyList|FBSFamilyMap|FBSFamilyMemberInfo|FBSRoom|FBSRoomList|FBSUserInfo|Int32Wrapper|NoneWrapper|StrWrapper|null
): BoolWrapper|FBSAddressInfo|FBSAddressInfoList|FBSAuthData|FBSDevice|FBSDeviceList|FBSDeviceMap|FBSDeviceOperationResult|FBSFamily|FBSFamilyInfo|FBSFamilyList|FBSFamilyMap|FBSFamilyMemberInfo|FBSRoom|FBSRoomList|FBSUserInfo|Int32Wrapper|NoneWrapper|StrWrapper|null {
  switch(UserdomainContainer[type]) {
    case 'NONE': return null; 
    case 'Int32Wrapper': return accessor(new Int32Wrapper())! as Int32Wrapper;
    case 'StrWrapper': return accessor(new StrWrapper())! as StrWrapper;
    case 'BoolWrapper': return accessor(new BoolWrapper())! as BoolWrapper;
    case 'NoneWrapper': return accessor(new NoneWrapper())! as NoneWrapper;
    case 'FBSDevice': return accessor(new FBSDevice())! as FBSDevice;
    case 'FBSDeviceMap': return accessor(new FBSDeviceMap())! as FBSDeviceMap;
    case 'FBSDeviceList': return accessor(new FBSDeviceList())! as FBSDeviceList;
    case 'FBSDeviceOperationResult': return accessor(new FBSDeviceOperationResult())! as FBSDeviceOperationResult;
    case 'FBSFamilyInfo': return accessor(new FBSFamilyInfo())! as FBSFamilyInfo;
    case 'FBSRoom': return accessor(new FBSRoom())! as FBSRoom;
    case 'FBSRoomList': return accessor(new FBSRoomList())! as FBSRoomList;
    case 'FBSFamilyMemberInfo': return accessor(new FBSFamilyMemberInfo())! as FBSFamilyMemberInfo;
    case 'FBSFamily': return accessor(new FBSFamily())! as FBSFamily;
    case 'FBSFamilyMap': return accessor(new FBSFamilyMap())! as FBSFamilyMap;
    case 'FBSFamilyList': return accessor(new FBSFamilyList())! as FBSFamilyList;
    case 'FBSUserInfo': return accessor(new FBSUserInfo())! as FBSUserInfo;
    case 'FBSAddressInfo': return accessor(new FBSAddressInfo())! as FBSAddressInfo;
    case 'FBSAddressInfoList': return accessor(new FBSAddressInfoList())! as FBSAddressInfoList;
    case 'FBSAuthData': return accessor(new FBSAuthData())! as FBSAuthData;
    default: return null;
  }
}

export function unionListToUserdomainContainer(
  type: UserdomainContainer, 
  accessor: (index: number, obj:BoolWrapper|FBSAddressInfo|FBSAddressInfoList|FBSAuthData|FBSDevice|FBSDeviceList|FBSDeviceMap|FBSDeviceOperationResult|FBSFamily|FBSFamilyInfo|FBSFamilyList|FBSFamilyMap|FBSFamilyMemberInfo|FBSRoom|FBSRoomList|FBSUserInfo|Int32Wrapper|NoneWrapper|StrWrapper) => BoolWrapper|FBSAddressInfo|FBSAddressInfoList|FBSAuthData|FBSDevice|FBSDeviceList|FBSDeviceMap|FBSDeviceOperationResult|FBSFamily|FBSFamilyInfo|FBSFamilyList|FBSFamilyMap|FBSFamilyMemberInfo|FBSRoom|FBSRoomList|FBSUserInfo|Int32Wrapper|NoneWrapper|StrWrapper|null, 
  index: number
): BoolWrapper|FBSAddressInfo|FBSAddressInfoList|FBSAuthData|FBSDevice|FBSDeviceList|FBSDeviceMap|FBSDeviceOperationResult|FBSFamily|FBSFamilyInfo|FBSFamilyList|FBSFamilyMap|FBSFamilyMemberInfo|FBSRoom|FBSRoomList|FBSUserInfo|Int32Wrapper|NoneWrapper|StrWrapper|null {
  switch(UserdomainContainer[type]) {
    case 'NONE': return null; 
    case 'Int32Wrapper': return accessor(index, new Int32Wrapper())! as Int32Wrapper;
    case 'StrWrapper': return accessor(index, new StrWrapper())! as StrWrapper;
    case 'BoolWrapper': return accessor(index, new BoolWrapper())! as BoolWrapper;
    case 'NoneWrapper': return accessor(index, new NoneWrapper())! as NoneWrapper;
    case 'FBSDevice': return accessor(index, new FBSDevice())! as FBSDevice;
    case 'FBSDeviceMap': return accessor(index, new FBSDeviceMap())! as FBSDeviceMap;
    case 'FBSDeviceList': return accessor(index, new FBSDeviceList())! as FBSDeviceList;
    case 'FBSDeviceOperationResult': return accessor(index, new FBSDeviceOperationResult())! as FBSDeviceOperationResult;
    case 'FBSFamilyInfo': return accessor(index, new FBSFamilyInfo())! as FBSFamilyInfo;
    case 'FBSRoom': return accessor(index, new FBSRoom())! as FBSRoom;
    case 'FBSRoomList': return accessor(index, new FBSRoomList())! as FBSRoomList;
    case 'FBSFamilyMemberInfo': return accessor(index, new FBSFamilyMemberInfo())! as FBSFamilyMemberInfo;
    case 'FBSFamily': return accessor(index, new FBSFamily())! as FBSFamily;
    case 'FBSFamilyMap': return accessor(index, new FBSFamilyMap())! as FBSFamilyMap;
    case 'FBSFamilyList': return accessor(index, new FBSFamilyList())! as FBSFamilyList;
    case 'FBSUserInfo': return accessor(index, new FBSUserInfo())! as FBSUserInfo;
    case 'FBSAddressInfo': return accessor(index, new FBSAddressInfo())! as FBSAddressInfo;
    case 'FBSAddressInfoList': return accessor(index, new FBSAddressInfoList())! as FBSAddressInfoList;
    case 'FBSAuthData': return accessor(index, new FBSAuthData())! as FBSAuthData;
    default: return null;
  }
}
