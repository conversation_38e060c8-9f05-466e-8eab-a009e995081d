// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

import { FBSMemberInfo } from '../../../../../../../com/haier/uhome/uplus/rust/userdomain/fbs/fbsmember-info.js';


export class FBSFamilyMemberInfo {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):FBSFamilyMemberInfo {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsFBSFamilyMemberInfo(bb:flatbuffers.ByteBuffer, obj?:FBSFamilyMemberInfo):FBSFamilyMemberInfo {
  return (obj || new FBSFamilyMemberInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsFBSFamilyMemberInfo(bb:flatbuffers.ByteBuffer, obj?:FBSFamilyMemberInfo):FBSFamilyMemberInfo {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new FBSFamilyMemberInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

familyId():string|null
familyId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
familyId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

joinTime():string|null
joinTime(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
joinTime(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

memberName():string|null
memberName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
memberName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

memberRole():string|null
memberRole(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
memberRole(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

shareDeviceCount():number {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? this.bb!.readUint32(this.bb_pos + offset) : 0;
}

memberInfo(obj?:FBSMemberInfo):FBSMemberInfo|null {
  const offset = this.bb!.__offset(this.bb_pos, 14);
  return offset ? (obj || new FBSMemberInfo()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
}

memberType():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 16);
  return offset ? this.bb!.readUint64(this.bb_pos + offset) : BigInt('0');
}

static startFBSFamilyMemberInfo(builder:flatbuffers.Builder) {
  builder.startObject(7);
}

static addFamilyId(builder:flatbuffers.Builder, familyIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(0, familyIdOffset, 0);
}

static addJoinTime(builder:flatbuffers.Builder, joinTimeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, joinTimeOffset, 0);
}

static addMemberName(builder:flatbuffers.Builder, memberNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, memberNameOffset, 0);
}

static addMemberRole(builder:flatbuffers.Builder, memberRoleOffset:flatbuffers.Offset) {
  builder.addFieldOffset(3, memberRoleOffset, 0);
}

static addShareDeviceCount(builder:flatbuffers.Builder, shareDeviceCount:number) {
  builder.addFieldInt32(4, shareDeviceCount, 0);
}

static addMemberInfo(builder:flatbuffers.Builder, memberInfoOffset:flatbuffers.Offset) {
  builder.addFieldOffset(5, memberInfoOffset, 0);
}

static addMemberType(builder:flatbuffers.Builder, memberType:bigint) {
  builder.addFieldInt64(6, memberType, BigInt('0'));
}

static endFBSFamilyMemberInfo(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

}
