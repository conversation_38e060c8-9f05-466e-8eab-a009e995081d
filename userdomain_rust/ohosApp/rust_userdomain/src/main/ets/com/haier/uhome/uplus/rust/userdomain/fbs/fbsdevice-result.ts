// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

export class FBSDeviceResult {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):FBSDeviceResult {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsFBSDeviceResult(bb:flatbuffers.ByteBuffer, obj?:FBSDeviceResult):FBSDeviceResult {
  return (obj || new FBSDeviceResult()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsFBSDeviceResult(bb:flatbuffers.ByteBuffer, obj?:FBSDeviceResult):FBSDeviceResult {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new FBSDeviceResult()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

deviceId():string|null
deviceId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceName():string|null
deviceName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

reasonCode():string|null
reasonCode(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
reasonCode(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

static startFBSDeviceResult(builder:flatbuffers.Builder) {
  builder.startObject(3);
}

static addDeviceId(builder:flatbuffers.Builder, deviceIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(0, deviceIdOffset, 0);
}

static addDeviceName(builder:flatbuffers.Builder, deviceNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, deviceNameOffset, 0);
}

static addReasonCode(builder:flatbuffers.Builder, reasonCodeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, reasonCodeOffset, 0);
}

static endFBSDeviceResult(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createFBSDeviceResult(builder:flatbuffers.Builder, deviceIdOffset:flatbuffers.Offset, deviceNameOffset:flatbuffers.Offset, reasonCodeOffset:flatbuffers.Offset):flatbuffers.Offset {
  FBSDeviceResult.startFBSDeviceResult(builder);
  FBSDeviceResult.addDeviceId(builder, deviceIdOffset);
  FBSDeviceResult.addDeviceName(builder, deviceNameOffset);
  FBSDeviceResult.addReasonCode(builder, reasonCodeOffset);
  return FBSDeviceResult.endFBSDeviceResult(builder);
}
}
