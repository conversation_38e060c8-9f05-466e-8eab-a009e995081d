import { ByteBuffer } from '@ohos/flatbuffers';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FBSAddressInfo,
  FBSAddressInfoList,
  FBSAuthData,
  FBSDevice,
  FBSDeviceList,
  FBSDeviceMap,
  FBSDeviceOperationResult,
  FBSFamily,
  FBSFamilyInfo,
  FBSFamilyList,
  FBSFamilyMap,
  FBSFamilyMemberInfo,
  FBSRoomList,
  FBSUserInfo,
  Int32Wrapper,
  StrWrapper,
  UserDomainFlat
} from '../com/haier/uhome/uplus/rust/userdomain/fbs';
import { Device } from './Device';
import { Family } from './Family';
import { AuthData } from './models/AuthData';
import { UserInfo } from './models/UserInfo';
import { UserDomainLog } from './UserDomainLog';
import {
  ErrorMessage,
  handleRustFbsWrapperBuffer,
  handleRustWrapperBuffer,
  RustConstant,
  UserDomainCode,
  UserDomainResult
} from './util';
import { AddressInfo } from './models/AddressInfo';
import { DeviceOperationResult } from './models/DeviceOperationResult';
import { FamilyInfo } from './models/FamilyInfo';
import { Room } from './models/Room';
import { FamilyMemberInfo } from './models/MemberInfo';

export function handleRustVoidBuffer(buf: ArrayBuffer | null, tag: string, method: string) {
  handleRustVoidBufferResult(buf, tag, method);
}

export function handleRustVoidBufferResult(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<void> {
  if (buf) {
    const flat = UserDomainFlat.getRootAsUserDomainFlat(new ByteBuffer(new Uint8Array(buf)));
    const code = flat.code() ?? UserDomainCode.NoCode;
    const error = flat.error() ?? ErrorMessage.NoError;
    UserDomainLog.debug(tag, `${method}: ${code}, ${flat.error()}`);
    return new UserDomainResult(code, error);
  } else {
    UserDomainLog.warn(tag, `${method}: ${RustConstant.Call_Failure}`);
    return new UserDomainResult(UserDomainCode.RustCallFailure, ErrorMessage.RustCallFailure);
  }
}

export function handleRustBoolBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<boolean | undefined> {
  const result: UserDomainResult<boolean | undefined> = handleRustWrapperBuffer(buf, BoolWrapper);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  return result;
}

export function handleRustNumberBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<number | undefined> {
  const result: UserDomainResult<number | undefined> = handleRustWrapperBuffer(buf, Int32Wrapper);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  return result;
}

export function handleRustStringBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<string | undefined> {
  const result: UserDomainResult<string | undefined> = handleRustWrapperBuffer(buf, StrWrapper);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  return result;
}

export function handleRustAuthDataBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<AuthData | undefined> {
  const result: UserDomainResult<FBSAuthData | undefined> = handleRustFbsWrapperBuffer(buf, FBSAuthData);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const authData = value ? AuthData.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, authData);
}

export function handleRustUserInfoBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<UserInfo | undefined> {
  const result: UserDomainResult<FBSUserInfo | undefined> = handleRustFbsWrapperBuffer(buf, FBSUserInfo);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const userInfo = value ? UserInfo.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, userInfo);
}

export function handleRustFamilyInfoBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<FamilyInfo | undefined> {
  const result: UserDomainResult<FBSFamilyInfo | undefined> = handleRustFbsWrapperBuffer(buf, FBSFamilyInfo);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const familyInfo = value ? FamilyInfo.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, familyInfo);
}

export function handleRustFamilyBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Family | undefined> {
  const result: UserDomainResult<FBSFamily | undefined> = handleRustFbsWrapperBuffer(buf, FBSFamily);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const family = value ? Family.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, family);
}

export function handleRustDeviceBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Device | undefined> {
  const result: UserDomainResult<FBSDevice | undefined> = handleRustFbsWrapperBuffer(buf, FBSDevice);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const device = value ? Device.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, device);
}

export function handleRustDeviceMapBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Map<string, Device> | undefined> {
  const result: UserDomainResult<FBSDeviceMap | undefined> = handleRustFbsWrapperBuffer(buf, FBSDeviceMap);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const map = value ? Device.fromFbsMap(value) : undefined;
  return new UserDomainResult(code, error, map);
}

export function handleRustDeviceListBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Array<Device> | undefined> {
  const result: UserDomainResult<FBSDeviceList | undefined> = handleRustFbsWrapperBuffer(buf, FBSDeviceList);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const array = value ? Device.fromFbsList(value) : undefined;
  return new UserDomainResult(code, error, array);
}

export function handleRustFamilyMapBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Map<string, Family> | undefined> {
  const result: UserDomainResult<FBSFamilyMap | undefined> = handleRustFbsWrapperBuffer(buf, FBSFamilyMap);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const map = value ? Family.fromFbsMap(value) : undefined;
  return new UserDomainResult(code, error, map);
}

export function handleRustFamilyListBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Array<Family> | undefined> {
  const result: UserDomainResult<FBSFamilyList | undefined> = handleRustFbsWrapperBuffer(buf, FBSFamilyList);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const array = value ? Family.fromFbsList(value) : undefined;
  return new UserDomainResult(code, error, array);
}

export function handleRustAddressInfoBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<AddressInfo | undefined> {
  const result: UserDomainResult<FBSAddressInfo | undefined> = handleRustFbsWrapperBuffer(buf, FBSAddressInfo);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const info = value ? AddressInfo.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, info);
}

export function handleRustAddressInfoListBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Array<AddressInfo> | undefined> {
  const result: UserDomainResult<FBSAddressInfoList | undefined> = handleRustFbsWrapperBuffer(buf, FBSAddressInfoList);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const list = value ? AddressInfo.fromFbsList(value) : undefined;
  return new UserDomainResult(code, error, list);
}

export function handleRustDeviceOperationResultBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<DeviceOperationResult | undefined> {
  const result: UserDomainResult<FBSDeviceOperationResult | undefined> =
    handleRustFbsWrapperBuffer(buf, FBSDeviceOperationResult);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const dor = value ? DeviceOperationResult.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, dor);
}

export function handleRustRoomListBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<Array<Room> | undefined> {
  const result: UserDomainResult<FBSRoomList | undefined> = handleRustFbsWrapperBuffer(buf, FBSRoomList);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const list = value ? Room.fromFbsList(value) : undefined;
  return new UserDomainResult(code, error, list);
}

export function handleRustFamilyMemberInfoBuffer(
  buf: ArrayBuffer | null,
  tag: string,
  method: string
): UserDomainResult<FamilyMemberInfo | undefined> {
  const result: UserDomainResult<FBSFamilyMemberInfo | undefined> =
    handleRustFbsWrapperBuffer(buf, FBSFamilyMemberInfo);
  const code = result.code;
  const error = result.error;
  const value = result.value;
  UserDomainLog.debug(tag, `${method}: ${code}, ${error}, ${value}`);
  const info = value ? FamilyMemberInfo.fromFbs(value) : undefined;
  return new UserDomainResult(code, error, info);
}