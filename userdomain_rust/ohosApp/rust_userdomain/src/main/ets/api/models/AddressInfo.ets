import { F<PERSON><PERSON><PERSON><PERSON>, FBSAddressInfo, FBSAddressInfoList } from '../../com/haier/uhome/uplus/rust/userdomain/fbs';

export class AddressInfo {
  //地址信息
  address: Address = new Address();
  // 邮箱地址
  email: string = '';
  // 地址编号
  id: string = '';
  // 是否是默认地址（0：否，1：是）
  is_default: boolean = false;
  // 是否有效（0：无效，1：有效）
  is_service: boolean = false;
  // 收件人手机号码
  receiverMobile: string = '';
  // 收货人姓名
  receiverName: string = '';
  // 来源
  source: string = '';
  // 标签
  tag: string = '';
  // 用户ID
  userId: string = '';

  static fromFbs(fbs: FBSAddressInfo): AddressInfo {
    const info = new AddressInfo();
    const address = fbs.address();
    if (address) {
      info.address = Address.fromFbs(address);
    }
    info.email = fbs.email() ?? '';
    info.id = fbs.addressId() ?? '';
    info.is_default = fbs.isDefault();
    info.is_service = fbs.isService();
    info.receiverMobile = fbs.receiverMobile() ?? '';
    info.receiverName = fbs.receiverName() ?? '';
    info.source = fbs.source() ?? '';
    info.tag = fbs.tag() ?? '';
    info.userId = fbs.userId() ?? '';
    return info;
  }

  static fromFbsList(fbs: FBSAddressInfoList): Array<AddressInfo> {
    const length = fbs.addressesLength();
    const list = new Array<AddressInfo>(length);
    for (let i = 0; i < length; i++) {
      const fbsInfo = fbs.addresses(i);
      const info = fbsInfo ? AddressInfo.fromFbs(fbsInfo) : new AddressInfo();
      list[i] = info;
    }
    return list;
  }
}

export class Address {
  // 城市名称
  city: string = '';
  // 城市编码
  cityId: string = '';
  // 国家编码
  countryCode: string = '';
  // 区域名称
  district: string = '';
  // 区域编码
  districtId: string = '';
  // 地址行1
  line1: string = '';
  // 地址行2
  line2: string = '';
  // 邮政编码
  postcode: string = '';
  // 省份名称
  province: string = '';
  // 省份编码
  provinceId: string = '';
  // 乡镇、街道名称
  town: string = '';
  // 乡镇街道编码
  townId: string = '';

  static fromFbs(fbs: FBSAddress): Address {
    const address = new Address();
    address.city = fbs.city() ?? '';
    address.cityId = fbs.cityId() ?? '';
    address.countryCode = fbs.countryCode() ?? '';
    address.district = fbs.district() ?? '';
    address.districtId = fbs.districtId() ?? '';
    address.line1 = fbs.line1() ?? '';
    address.line2 = fbs.line2() ?? '';
    address.postcode = fbs.postcode() ?? '';
    address.province = fbs.province() ?? '';
    address.provinceId = fbs.provinceId() ?? '';
    address.town = fbs.town() ?? '';
    address.townId = fbs.townId() ?? '';
    return address;
  }
}