import { Type } from 'class-transformer';
import "reflect-metadata"
import { FBSDeviceOperationResult, FBSDeviceResult } from '../../com/haier/uhome/uplus/rust/userdomain/fbs';

export class DeviceOperationResult {
  @Type(() => DeviceResult)
  successDevices: Array<DeviceResult> = new Array();
  @Type(() => DeviceResult)
  failureDevices: Array<DeviceResult> = new Array();

  static fromFbs(fbs: FBSDeviceOperationResult): DeviceOperationResult {
    const result = new DeviceOperationResult();
    let length = fbs.successDevicesLength();
    for (let i = 0; i < length; i++) {
      const sd = fbs.successDevices(i);
      result.successDevices.push(sd ? DeviceResult.fromFbs(sd) : new DeviceResult());
    }
    length = fbs.failureDevicesLength();
    for (let i = 0; i < length; i++) {
      const fd = fbs.failureDevices(i);
      result.failureDevices.push(fd ? DeviceResult.fromFbs(fd) : new DeviceResult());
    }
    return result;
  }
}

export class DeviceResult {
  deviceId: string = '';
  deviceName: string = '';
  reasonCode: string = '';

  static fromFbs(fbs: FBSDeviceResult): DeviceResult {
    const dr = new DeviceResult();
    dr.deviceId = fbs.deviceId() ?? '';
    dr.deviceName = fbs.deviceName() ?? '';
    dr.reasonCode = fbs.reasonCode() ?? '';
    return dr;
  }
}