import { Type } from 'class-transformer';
import "reflect-metadata";
import { FBSFamilyMemberInfo, FBSMemberInfo } from '../../com/haier/uhome/uplus/rust/userdomain/fbs';

export class MemberInfo {
  // 用户ID
  userId: string = '';
  // 用户名称
  name: string = '';
  // 在家庭中的名称
  memberName: string = ''; // fbs 中不存在
  // 用户头像
  avatarUrl: string = '';
  // 用户手机号
  mobile: string = '';
  // 用户中心用户id
  ucUserId: string = '';
  // 宿主用户的IOT平台userId
  hostUserId: string = '';
  // 用户生日
  birthday: string = '';
  //是否虚拟成员
  virtualUserFlag: boolean = false;

  static fromFbs(fbs: FBSMemberInfo): MemberInfo {
    const memberInfo = new MemberInfo()
    memberInfo.userId = fbs.iotUserId() ?? ''
    memberInfo.name = fbs.name() ?? ''
    memberInfo.avatarUrl = fbs.userAvatar() ?? ''
    memberInfo.mobile = fbs.userMobile() ?? ''
    memberInfo.ucUserId = fbs.ucUserId() ?? ''
    memberInfo.hostUserId = fbs.hostUserId() ?? ''
    memberInfo.birthday = fbs.userBirthday() ?? ''
    memberInfo.virtualUserFlag = fbs.isVirtualMember()
    return memberInfo
  }
}

export class FamilyMemberInfo {
  // 家庭ID
  familyId: string = '';
  //加入家庭的时间
  joinTime: string = '';
  // 在家庭中的成员名称
  memberName: string = '';
  // 成员角色
  memberRole: string = '';
  // 家庭中该成员分享的设备数量
  shareDeviceCount: number = 0;
  //成员基本信息
  @Type(() => MemberInfo)
  memberInfo: MemberInfo = new MemberInfo();
  // 成员类型，0-创建者，1-管理员，2-成员
  memberType: number = 2;

  static fromFbs(fbs: FBSFamilyMemberInfo): FamilyMemberInfo {
    const info = new FamilyMemberInfo()
    info.familyId = fbs.familyId() ?? ''
    info.joinTime = fbs.joinTime() ?? ''
    info.memberName = fbs.memberName() ?? ''
    info.memberRole = fbs.memberRole() ?? ''
    info.shareDeviceCount = Number(fbs.shareDeviceCount())
    info.memberType = Number(fbs.memberType())
    const fbsMemberInfo = fbs.memberInfo()
    if (fbsMemberInfo) {
      info.memberInfo = MemberInfo.fromFbs(fbsMemberInfo)
    }
    return info
  }
}
