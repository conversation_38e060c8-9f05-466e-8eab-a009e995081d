import { Type } from 'class-transformer';
import "reflect-metadata"
import {
  FBSDeviceAuth,
  FBSDeviceInfo,
  FBSDeviceOwnerInfo,
  FBSDevicePermission,
  FBSShareDeviceCardInfo
} from '../../com/haier/uhome/uplus/rust/userdomain/fbs';

export class DeviceInfo {
  // 设备ID
  deviceId: string = '';
  // 设备名称
  deviceName: string = '';
  // 家庭下设备名称
  devName: string = '';
  // 设备类型
  deviceType: string = '';
  // 家庭id
  familyId: string = '';
  //设备拥有者id
  ownerId: string = '';
  //权限信息
  @Type(() => DevicePermission)
  permission: DevicePermission = new DevicePermission();
  //typeid
  wifiType: string = '';
  //网器：netdevice,非网器，nonNetDevice
  deviceNetType: string = '';
  //绑定时间
  bindTime: string = '';
  //是否在线
  isOnline: boolean = false;
  //绑定用户信息
  @Type(() => DeviceOwnerInfo)
  ownerInfo: DeviceOwnerInfo = new DeviceOwnerInfo();
  //子设备id列表
  subDeviceIds: Array<string> = new Array();
  //父设备id
  parentId: string = '';
  //1 普通设备; 2 网关设备; 3 附件设备; 4 子设备;
  deviceRole: string = '';
  //主设备/子设备/无主从关系，0 无主设备，1 主设备，2 子设备
  deviceRoleType: string = '';
  //应用分类
  apptypeName: string = '';
  //应用分类编码
  apptypeCode: string = '';
  //一级应用分组
  categoryGrouping: string = '';
  //机编码
  barcode: string = '';
  //绑定方式（SmartLink SmartAP SoftAP Scancode bt DirectLink Nolink BLE&SoftAP）
  bindType: string = '';
  //品牌
  brand: string = '';
  //实物图1
  imageAddr1: string = '';
  //小卡图
  cardPageImg: string = '';
  //卡片排序
  cardSort: number = 0;
  //卡片状态
  cardStatus: number = 0;
  //聚合父设备ID
  aggregationParentId: string = '';
  //是否支持聚合标识
  supportAggregationFlag: number = 0;
  //设备聚合类型
  deviceAggregateType: string = '';
  //型号
  model: string = '';
  //产品编码
  prodNo: string = '';
  //房间名称
  roomName: string = '';
  //房间id
  roomId: string = '';
  //是否设备拥有者
  isOwner: boolean = false; // fbs 中不存在
  //联网类型 13:WIFI, 14:SDK_LINUX, 15:SDK_RTOS,31:SDK_ANDROID,90:NB-IoT, 93:Wi-Fi&BLE, 98:DEVICE_CLOUD
  accessType: string = '';
  //0 app端支持配网绑定；1 app端不支持配网绑定
  configType: string = '';
  //通讯协议
  communicationMode: string = '';
  //楼层id
  deviceFloorId: string = '';
  //在家庭中的楼层序列（-3到5，没有0）
  deviceFloorOrderId: string = '';
  //楼层名称
  deviceFloorName: string = '';
  //	应用分类名称
  apptypeIcon: string = '';
  //组设备id
  deviceGroupId: string = '';
  //组设备类型
  deviceGroupType: string = '';
  //0:保活  1：非保活
  noKeepAlive: boolean = false;
  //二级应用分组
  twoGropingName: string = '';
  //极简版支持标识
  supportFlag: number = 0;
  //设备共享标识
  sharedDeviceFlag: number = 0;
  //设备共享卡片信息
  @Type(() => ShareDeviceCardInfo)
  shareDeviceCardInfo: Array<ShareDeviceCardInfo> = new Array();
  //附件设备排序码
  attachmentSortCode: number = 0;
  //设备共享支持标识
  deviceShareSupportFlag: boolean = false;
  //设备二次绑定标识，1表示支持二次绑定，0表示不支持二次绑定
  rebind: number = 0;
  static fromFbs(fbs: FBSDeviceInfo): DeviceInfo {
    const deviceInfo = new DeviceInfo()
    deviceInfo.deviceId = fbs.deviceId() ?? ''
    deviceInfo.deviceName = fbs.deviceName() ?? ''
    deviceInfo.devName = fbs.devName() ?? ''
    deviceInfo.deviceType = fbs.deviceType() ?? ''
    deviceInfo.familyId = fbs.familyId() ?? ''
    deviceInfo.ownerId = fbs.ownerId() ?? ''
    const fbsPermission = fbs.permission()
    if (fbsPermission) {
      deviceInfo.permission = DevicePermission.fromFbs(fbsPermission)
    }
    deviceInfo.wifiType = fbs.wifiType() ?? ''
    deviceInfo.deviceNetType = fbs.deviceNetType() ?? ''
    deviceInfo.bindTime = fbs.bindTime() ?? ''
    deviceInfo.isOnline = fbs.isOnline()
    const fbsOwnerInfo = fbs.ownerInfo()
    if (fbsOwnerInfo) {
      deviceInfo.ownerInfo = DeviceOwnerInfo.fromFbs(fbsOwnerInfo)
    }
    const subDeviceIdsLength = fbs.subDeviceIdsLength()
    const subDeviceIds = new Array<string>(subDeviceIdsLength)
    for (let i = 0; i < subDeviceIdsLength; i++) {
      subDeviceIds[i] = fbs.subDeviceIds(i)
    }
    deviceInfo.subDeviceIds = subDeviceIds
    deviceInfo.parentId = fbs.parentId() ?? ''
    deviceInfo.deviceRole = fbs.deviceRole() ?? ''
    deviceInfo.deviceRoleType = fbs.deviceRoleType() ?? ''
    deviceInfo.apptypeName = fbs.apptypeName() ?? ''
    deviceInfo.apptypeCode = fbs.apptypeCode() ?? ''
    deviceInfo.categoryGrouping = fbs.categoryGrouping() ?? ''
    deviceInfo.barcode = fbs.barcode() ?? ''
    deviceInfo.bindType = fbs.bindType() ?? ''
    deviceInfo.brand = fbs.brand() ?? ''
    deviceInfo.imageAddr1 = fbs.imageAddr1() ?? ''
    deviceInfo.cardPageImg = fbs.cardPageImg() ?? ''
    deviceInfo.cardSort = Number(fbs.cardSort())
    deviceInfo.cardStatus = Number(fbs.cardStatus())
    deviceInfo.aggregationParentId = fbs.aggregationParentId() ?? ''
    deviceInfo.supportAggregationFlag = Number(fbs.supportAggregationFlag())
    deviceInfo.model = fbs.model() ?? ''
    deviceInfo.prodNo = fbs.prodNo() ?? ''
    deviceInfo.roomName = fbs.roomName() ?? ''
    deviceInfo.roomId = fbs.roomId() ?? ''
    deviceInfo.accessType = fbs.accessType() ?? ''
    deviceInfo.configType = fbs.configType() ?? ''
    deviceInfo.communicationMode = fbs.communicationMode() ?? ''
    deviceInfo.deviceFloorId = fbs.deviceFloorId() ?? ''
    deviceInfo.deviceFloorOrderId = fbs.deviceFloorOrderId() ?? ''
    deviceInfo.deviceFloorName = fbs.deviceFloorName() ?? ''
    deviceInfo.apptypeIcon = fbs.apptypeIcon() ?? ''
    deviceInfo.deviceGroupId = fbs.deviceGroupId() ?? ''
    deviceInfo.deviceGroupType = fbs.deviceGroupType() ?? ''
    deviceInfo.noKeepAlive = fbs.noKeepAlive()
    deviceInfo.twoGropingName = fbs.twoGropingName() ?? ''
    deviceInfo.supportFlag = Number(fbs.supportFlag())
    deviceInfo.sharedDeviceFlag = Number(fbs.sharedDeviceFlag())
    const shareDeviceCardInfoLength = fbs.shareDeviceCardInfoLength()
    const shareDeviceCardInfo = new Array<ShareDeviceCardInfo>()
    for (let i = 0; i < shareDeviceCardInfoLength; i++) {
      const fbsShareDeviceCardInfo = fbs.shareDeviceCardInfo(i)
      shareDeviceCardInfo[i] =
        fbsShareDeviceCardInfo ? ShareDeviceCardInfo.fromFbs(fbsShareDeviceCardInfo) : new ShareDeviceCardInfo()
    }
    deviceInfo.shareDeviceCardInfo = shareDeviceCardInfo
    deviceInfo.attachmentSortCode = Number(fbs.attachmentSortCode())
    deviceInfo.deviceShareSupportFlag = fbs.deviceShareSupportFlag()
    deviceInfo.rebind = Number(fbs.rebind())
    return deviceInfo
  }
}

export class DevicePermission {
  auth: DeviceAuth = new DeviceAuth();
  authType: string = '';

  static fromFbs(fbs: FBSDevicePermission): DevicePermission {
    const devicePermission = new DevicePermission()
    const fbsAuth = fbs.auth()
    if (fbsAuth) {
      devicePermission.auth = DeviceAuth.fromFbs(fbsAuth)
    }
    devicePermission.authType = fbs.authType() ?? ''
    return devicePermission
  }
}

export class DeviceOwnerInfo {
  userId: string = '';
  mobile: string = '';
  userNickname: string = '';
  ucUserId: string = '';

  static fromFbs(fbs: FBSDeviceOwnerInfo): DeviceOwnerInfo {
    const info = new DeviceOwnerInfo()
    info.userId = fbs.userId() ?? ''
    info.mobile = fbs.mobile() ?? ''
    info.userNickname = fbs.userNickName() ?? ''
    info.ucUserId = fbs.ucUserId() ?? ''
    return info
  }
}

export class DeviceAuth {
  control: boolean = false;
  set: boolean = false;
  view: boolean = false;

  static fromFbs(fbs: FBSDeviceAuth): DeviceAuth {
    const auth = new DeviceAuth()
    auth.control = fbs.control()
    auth.set = fbs.set()
    auth.view = fbs.view()
    return auth
  }
}

export class ShareDeviceCardInfo {
  familyId: string = '';
  cardSort: number = 0;
  cardStatus: number = 0;

  static fromFbs(fbs: FBSShareDeviceCardInfo): ShareDeviceCardInfo {
    const info = new ShareDeviceCardInfo();
    info.familyId = fbs.familyId() ?? ''
    info.cardSort = Number(fbs.cardSort())
    info.cardStatus = Number(fbs.cardStatus())
    return info
  }
}