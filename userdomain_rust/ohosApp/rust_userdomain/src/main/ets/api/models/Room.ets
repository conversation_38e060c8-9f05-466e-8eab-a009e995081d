import { FBSRoom, FBSRoomList } from '../../com/haier/uhome/uplus/rust/userdomain/fbs';

export class Room {
  // 房间名称
  roomName: string = '';
  // 房间ID
  roomId: string = '';
  // 房间类型
  roomClass?: string;
  // 房间标签
  roomLabel?: string;
  // 房间图标
  roomLogo?: string;
  // 房间图片
  roomPicture?: string;
  // 排序码，可能的值为"0"、"1"、"2"
  sortCode?: string;
  // 楼层次序 (新增字段)
  floorOrderId?: string;
  // 楼层ID (新增字段)
  floorId?: string;

  static fromFbs(fbs: FBSRoom): Room {
    const room = new Room()
    room.roomName = fbs.roomName() ?? ''
    room.roomId = fbs.roomId() ?? ''
    room.roomClass = fbs.roomClass() ?? ''
    room.roomLabel = fbs.roomLabel() ?? ''
    room.roomLogo = fbs.roomLogo() ?? ''
    room.roomPicture = fbs.roomPicture() ?? ''
    room.sortCode = fbs.sortCode() ?? ''
    room.floorOrderId = fbs.floorOrderId() ?? ''
    room.floorId = fbs.floorId() ?? ''
    return room
  }

  static fromFbsList(fbsList: FBSRoomList): Array<Room> {
    const length = fbsList.roomsLength();
    const list = new Array<Room>(length);
    for (let i = 0; i < length; i++) {
      const fbs = fbsList.rooms(i);
      list[i] = fbs ? Room.fromFbs(fbs) : new Room();
    }
    return list;
  }
}