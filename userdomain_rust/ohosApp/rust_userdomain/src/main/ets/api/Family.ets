import { Device } from './Device';
import { DeviceInfo } from './models/DeviceInfo';
import { FamilyArgs } from './models/FamilyArgs';
import { FamilyInfo } from './models/FamilyInfo';
import { FloorArgs } from './models/FloorArgs';
import { Room } from './models/Room';
import { RoomArgs, RoomOrderArgs } from './models/RoomArgs';
import {
  handleRustDeviceListBuffer,
  handleRustDeviceMapBuffer,
  handleRustDeviceOperationResultBuffer,
  handleRustFamilyInfoBuffer,
  handleRustFamilyMemberInfoBuffer,
  handleRustNumberBuffer,
  handleRustRoomListBuffer,
  handleRustStringBuffer,
  handleRustVoidBufferResult
} from './RustUserDomainWizard';
import { instanceToPlain, Type } from 'class-transformer';
import "reflect-metadata";
import { DeviceOperationResult } from './models/DeviceOperationResult';
import { FamilyMemberInfo } from './models/MemberInfo';
import { FBSFamily, F<PERSON><PERSON>amilyList, FBSFamilyMap } from '../com/haier/uhome/uplus/rust/userdomain/fbs';
import { argsToMap, RustConstant, UserDomainResult } from './util';
import { RustChannel } from '@uplus/rust_ffi';
import { TAG } from './UserDomain';
import { AggCardItemArgs } from './models/AggCardItemArgs';

export class Family {
  @Type(() => FamilyInfo)
  familyInfo: FamilyInfo = new FamilyInfo();

  constructor(familyInfo: FamilyInfo) {
    this.familyInfo = familyInfo;
  }

  static fromFbs(fbs: FBSFamily): Family {
    const fbsInfo = fbs.familyInfo()
    const info = fbsInfo ? FamilyInfo.fromFbs(fbsInfo) : new FamilyInfo()
    return new Family(info)
  }

  static fromFbsList(fbsList: FBSFamilyList): Array<Family> {
    const length = fbsList.familysLength()
    const families = new Array<Family>(length)
    for (let i = 0; i < length; i++) {
      const fbsFamily = fbsList.familys(i)
      families[i] = fbsFamily ? Family.fromFbs(fbsFamily) : new Family(new FamilyInfo())
    }
    return families
  }

  static fromFbsMap(fbsMap: FBSFamilyMap): Map<string, Family> {
    const length = fbsMap.entriesLength()
    const map = new Map<string, Family>()
    for (let i = 0; i < length; i++) {
      const entry = fbsMap.entries(i)
      const key = entry?.key() ?? ""
      const fbsFamily = entry?.value()
      const value = fbsFamily ? Family.fromFbs(fbsFamily) : new Family(new FamilyInfo())
      map.set(key, value)
    }
    return map
  }

  public get getFamilyInfo(): FamilyInfo {
    return this.familyInfo;
  }

  /**
   * 获取设备列表map
   * @returns
   */
  getDevicesMap(): Map<string, Device> {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_devices_map_by_family_id");
    params.set("family_id", this.familyInfo.familyId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustDeviceMapBuffer(buf, TAG, "getDevicesMap");
    return result.value ?? new Map();
  }

  getDeviceList(): Array<Device> {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_device_list_by_family_id");
    params.set("family_id", this.familyInfo.familyId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustDeviceListBuffer(buf, TAG, "getDeviceList");
    return result.value ?? new Array();
  }

  /**
   * 删除家庭成员
   * @param memberId 成员的userid
   * @returns
   */
  async deleteFamilyMember(memberId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "delete_family_member");
      params.set("family_id", this.familyInfo.familyId);
      params.set("member_id", memberId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "deleteFamilyMember");
        resolve(result);
      });
    });
  }

  /**
   * 管理员邀请成员加入家庭
   * @param userId 被邀请用户的用户中心ID
   * @param nickname 被邀请用户的用户昵称
   * @param memberRole 身份备注（可选）
   * @param memberType 角色类型（1: 管理员, 2: 成员）
   * @returns
   */
  async adminInviteMember(
    userId: string,
    nickname: string,
    memberRole: string = "",
    memberType: number = 2
  ): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "admin_invite_member");
      params.set("family_id", this.familyInfo.familyId);
      params.set("user_id", userId);
      params.set("nickname", nickname);
      params.set("member_role", memberRole);
      params.set("member_type", memberType.toString());
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "adminInviteMember");
        resolve(result);
      });
    });
  }

  /**
   * 更换家庭管理员
   * @param userId 用户id
   * @returns
   */
  async reassignFamilyAdministrator(userId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "change_family_admin");
      params.set("family_id", this.familyInfo.familyId);
      params.set("user_id", userId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "reassignFamilyAdministrator");
        resolve(result);
      });
    });
  }

  /**
   * 添加房间
   * @param roomArgs 房间参数
   * @param room 房间
   * @returns
   */
  async addRoom(roomArgs: RoomArgs): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = argsToMap(roomArgs);
      params.set(RustConstant.Action, "add_room");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "addRoom");
        resolve(result);
      });
    });
  }

  /**
   * 解散家庭
   * @returns
   */
  async deleteFamilyAsAdmin(): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "delete_family_as_admin");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "deleteFamilyAsAdmin");
        resolve(result);
      });
    });
  }

  /**
   * 移除房间
   * @param roomId 房间id
   * @returns
   */
  async removeRoom(roomId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "remove_room");
      params.set("family_id", this.familyInfo.familyId);
      params.set("room_id", roomId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "removeRoom");
        resolve(result);
      });
    });
  }

  /**
   * 修改家庭信息
   * @param familyInfo 家庭信息
   * @returns
   */
  async modifyFamilyInfo(familyArgs: FamilyArgs): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = argsToMap(familyArgs);
      params.set(RustConstant.Action, "modify_family_info");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyFamilyInfo");
        resolve(result);
      });
    });
  }

  /**
   * 更新房间名称
   * @param roomName 房间名称
   * @param roomId 房间id
   * @returns
   */
  async updateRoomName(roomName: string, roomId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "update_room_name");
      params.set("family_id", this.familyInfo.familyId);
      params.set("room_id", roomId);
      params.set("room_name", roomName);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "updateRoomName");
        resolve(result);
      });
    });
  }

  /**
   * 管理员退出家庭
   * @param userId 管理员id
   * @returns
   */
  async exitFamilyAsAdmin(userId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "exit_family_as_admin");
      params.set("family_id", this.familyInfo.familyId);
      params.set("user_id", userId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "exitFamilyAsAdmin");
        resolve(result);
      });
    });
  }

  /**
   * 成员退出家庭
   * @returns
   */
  async exitFamilyAsMember(): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "exit_family_as_member");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "exitFamilyAsMember");
        resolve(result);
      });
    });
  }

  /**
   * 移动设备到其他房间
   * @param room 房间对象
   * @param deviceInfos 设备数组
   * @returns
   */
  async moveDevicesToOtherRoom(
    room: Room,
    deviceInfos: Array<DeviceInfo>
  ): Promise<UserDomainResult<DeviceOperationResult | undefined>> {
    return new Promise((resolve) => {
      const deviceIds = deviceInfos.map((it) => it.deviceId)
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "move_devices_to_other_room");
      params.set("family_id", this.familyInfo.familyId);
      params.set("new_room_id", room.roomId);
      params.set("device_infos", JSON.stringify(instanceToPlain(deviceIds)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustDeviceOperationResultBuffer(buf, TAG, "moveDevicesToOtherRoom");
        resolve(result);
      });
    });
  }

  /**
   * 查询家庭信息
   * @returns
   */
  async queryFamilyInfo(): Promise<UserDomainResult<FamilyInfo | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "query_family_info");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustFamilyInfoBuffer(buf, TAG, "queryFamilyInfo");
        resolve(result);
      });
    });
  }

  /**
   * 查询家庭房间列表
   * @param floorId 楼层id
   * @returns
   */
  async queryRoomList(floorId: string): Promise<UserDomainResult<Array<Room> | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "query_room_list");
      params.set("family_id", this.familyInfo.familyId);
      params.set("floor_id", floorId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustRoomListBuffer(buf, TAG, "queryRoomList");
        resolve(result);
      });
    });
  }

  /**
   * 解绑设备
   * @param deviceInfos 设备数组
   * @returns
   */
  async unbindDevices(deviceInfos: Array<DeviceInfo>): Promise<UserDomainResult<DeviceOperationResult | undefined>> {
    return new Promise((resolve) => {
      let deviceIds = deviceInfos.map((it) => it.deviceId);
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "unbind_devices");
      params.set("family_id", this.familyInfo.familyId);
      params.set("device_infos", JSON.stringify(instanceToPlain(deviceIds)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustDeviceOperationResultBuffer(buf, TAG, "unbindDevices");
        resolve(result);
      });
    });
  }

  /**
   * 移动设备到其他家庭
   * @param deviceInfos 设备数组
   * @param targetFamilyId 家庭ID
   * @returns
   */
  async moveDevicesToOtherFamily(
    deviceInfos: Array<DeviceInfo>,
    targetFamilyId: string
  ): Promise<UserDomainResult<DeviceOperationResult | undefined>> {
    return new Promise((resolve) => {
      let deviceIds = deviceInfos.map((it) => it.deviceId);
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "move_devices_to_other_family");
      params.set("family_id", this.familyInfo.familyId);
      params.set("target_family_id", targetFamilyId);
      params.set("device_infos", JSON.stringify(instanceToPlain(deviceIds)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustDeviceOperationResultBuffer(buf, TAG, "moveDevicesToOtherFamily");
        resolve(result);
      });
    });
  }

  /**
   * 创建楼层
   * @param floorArgs 楼层参数
   * @returns
   */
  async createFloor(floorArgs: FloorArgs): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = argsToMap(floorArgs);
      params.set(RustConstant.Action, "create_floor");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "createFloor");
        resolve(result);
      });
    });
  }

  /**
   * 删除楼层
   * @param floorId 楼层ID
   * @returns
   */
  /**
   * 修改设备卡片状态
   * @param cardOrderList 卡片排序列表
   * @param bigCardList 大卡片列表
   * @param middleCardList 中卡片列表
   * @param smallCardList 小卡片列表
   * @returns 修改结果
   */
  async modifyDeviceCardStatus(
    cardOrderList?: string[],
    bigCardList?: string[],
    middleCardList?: string[],
    smallCardList?: string[]
  ): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "modify_device_card_status");
      params.set("family_id", this.familyInfo.familyId);
      if (cardOrderList) {
        params.set("card_order_list", JSON.stringify(instanceToPlain(cardOrderList)));
      }
      if (bigCardList) {
        params.set("big_card_list", JSON.stringify(instanceToPlain(bigCardList)));
      }
      if (middleCardList) {
        params.set("middle_card_list", JSON.stringify(instanceToPlain(middleCardList)));
      }
      if (smallCardList) {
        params.set("small_card_list", JSON.stringify(instanceToPlain(smallCardList)));
      }
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyDeviceCardStatus");
        resolve(result);
      });
    });
  }

  /**
   * 修改设备聚合状态
   * @param aggCards 聚合卡片列表
   * @returns 修改结果
   */
  async modifyDeviceAggregation(aggCards: AggCardItemArgs[]): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "modify_device_aggregation");
      params.set("family_id", this.familyInfo.familyId);
      params.set("agg_cards", JSON.stringify(instanceToPlain(aggCards)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyDeviceAggregation");
        resolve(result);
      });
    });
  }

  async deleteFloor(floorId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "delete_floor");
      params.set("family_id", this.familyInfo.familyId);
      params.set("floor_id", floorId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "deleteFloor");
        resolve(result);
      });
    });
  }

  /**
   * 查询第一个加入家庭的成员
   * @returns
   */
  async queryFirstMember(): Promise<UserDomainResult<FamilyMemberInfo | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "query_first_member");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustFamilyMemberInfoBuffer(buf, TAG, "queryFirstMember");
        resolve(result);
      });
    });
  }

  /**
   * 添加虚拟成员
   * @param memberName 成员名称
   * @param memberId 成员ID
   * @returns
   */
  async addVirtualMember(memberName: string, memberId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "add_virtual_member");
      params.set("family_id", this.familyInfo.familyId);
      params.set("member_name", memberName);
      params.set("member_id", memberId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "addVirtualMember");
        resolve(result);
      });
    });
  }

  /**
   * 修改虚拟成员
   * @param memberId 成员ID
   * @param memberName 成员名称
   * @param avatarUrl 头像URL
   * @param isCreator 是否创建者
   * @param birthday 生日
   * @returns
   */
  async modifyVirtualMember(
    memberId: string,
    memberName: string,
    avatarUrl: string,
    isCreator: boolean,
    birthday: string
  ): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "modify_virtual_member");
      params.set("family_id", this.familyInfo.familyId);
      params.set("member_id", memberId);
      params.set("member_name", memberName);
      params.set("avatar_url", avatarUrl);
      params.set("birthday", birthday);
      params.set("is_creator", String(isCreator));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyVirtualMember");
        resolve(result);
      });
    });
  }

  /**
   * 修改成员角色
   * @param memberId 成员ID
   * @param role 角色
   * @returns
   */
  async modifyMemberRole(memberId: string, memberRole: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "modify_member_role");
      params.set("family_id", this.familyInfo.familyId);
      params.set("member_id", memberId);
      params.set("member_role", memberRole);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyMemberRole");
        resolve(result);
      });
    });
  }

  /**
   * 修改虚拟成员角色
   * @param memberId 成员ID
   * @param role 角色
   * @returns
   */
  async modifyVirtualMemberRole(memberId: string, memberRole: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "modify_virtual_member_role");
      params.set("family_id", this.familyInfo.familyId);
      params.set("member_id", memberId);
      params.set("member_role", memberRole);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyVirtualMemberRole");
        resolve(result);
      });
    });
  }

  /**
   * 保存房间顺序
   * @param roomOrderArgs 房间排序参数数组，每个元素包含floorId和rooms
   * @returns
   */
  async saveRoomsOrder(roomOrderArgs: RoomOrderArgs[]): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "save_rooms_order");
      params.set("family_id", this.familyInfo.familyId);
      params.set("room_order_args", JSON.stringify(instanceToPlain(roomOrderArgs)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "saveRoomsOrder");
        resolve(result);
      });
    });
  }

  /**
   * 获取家庭下的组设备列表
   * @returns
   */
  async getGroupDeviceList(): Promise<UserDomainResult<Array<Device> | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "get_group_device_list");
      params.set("family_id", this.familyInfo.familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustDeviceListBuffer(buf, TAG, "getGroupDeviceList");
        resolve(result);
      });
    });
  }

  /**
   * 获取当前用户在家庭中的成员类型
   * 0-创建者，1-管理员，2-成员
   * @returns 成员类型
   */
  getMemberType(): number {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_member_type");
    params.set("family_id", this.familyInfo.familyId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustNumberBuffer(buf, TAG, "getMemberType");
    return result.value ?? 2;
  }

  /**
   * 获取当前用户加入家庭的时间
   * @returns 加入时间
   */
  getJoinTime(): string {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_join_time");
    params.set("family_id", this.familyInfo.familyId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustStringBuffer(buf, TAG, "getJoinTime");
    return result.value ?? "";
  }

  /**
   * 修改家庭成员类型
   * @param memberId 成员ID
   * @param memberType 成员类型（1：管理员，2：成员）
   * @returns 操作结果
   */
  async modifyMemberType(memberId: string, memberType: number): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "get_join_time");
      params.set("family_id", this.familyInfo.familyId);
      params.set("member_id", memberId);
      params.set("member_type", memberType.toString());
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "getGroupDeviceList");
        resolve(result);
      });
    });
  }
}