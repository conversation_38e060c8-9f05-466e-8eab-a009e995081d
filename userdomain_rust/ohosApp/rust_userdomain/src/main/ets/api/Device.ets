import { DeviceInfo } from './models/DeviceInfo';
import { handleRustBoolBuffer, handleRustVoidBufferResult } from './RustUserDomainWizard';
import { Type } from 'class-transformer';
import { FBSDevice, FBSDeviceList, FBSDeviceMap } from '../com/haier/uhome/uplus/rust/userdomain/fbs';
import { RustConstant, UserDomainResult } from './util';
import { RustChannel } from '@uplus/rust_ffi';
import { TAG } from './UserDomain';

export class Device {
  @Type(() => DeviceInfo)
  deviceInfo: DeviceInfo = new DeviceInfo();

  constructor(deviceInfo: DeviceInfo) {
    this.deviceInfo = deviceInfo;
  }

  static fromFbs(fbs: FBSDevice): Device {
    const fbsDeviceInfo = fbs.deviceInfo()
    const deviceInfo = fbsDeviceInfo ? DeviceInfo.fromFbs(fbsDeviceInfo) : new DeviceInfo()
    return new Device(deviceInfo)
  }

  static fromFbsList(fbsList: FBSDeviceList): Array<Device> {
    const length = fbsList.devicesLength()
    const devices = new Array<Device>()
    for (let i = 0; i < length; i++) {
      const fbsDevice = fbsList.devices(i)
      devices[i] = fbsDevice ? Device.fromFbs(fbsDevice) : new Device(new DeviceInfo())
    }
    return devices
  }

  static fromFbsMap(fbsMap: FBSDeviceMap): Map<string, Device> {
    const length = fbsMap.entriesLength()
    const map = new Map<string, Device>()
    for (let i = 0; i < length; i++) {
      const entry = fbsMap.entries(i)
      const key = entry?.key() ?? ""
      const fbsDevice = entry?.value()
      const value = fbsDevice ? Device.fromFbs(fbsDevice) : new Device(new DeviceInfo())
      map.set(key, value)
    }
    return map
  }

  getDeviceInfo(): DeviceInfo {
    return this.deviceInfo;
  }

  /**
   * 获取设备是否支持共享
   * @return 是否支持共享
   */
  supportShared(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_device_support_shared");
    params.set("device_id", this.deviceInfo.deviceId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustBoolBuffer(buf, TAG, "supportShared");
    return result.value ?? false;
  }

  /**
   * 更新设备名称
   * @param deviceName 设备名称
   * @returns
   */
  async updateDeviceName(deviceName: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "update_device_name");
      params.set("deviceId", this.deviceInfo.deviceId);
      params.set("deviceName", deviceName);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "updateDeviceName");
        resolve(result);
      });
    });
  }

  /**
   * 更新设备名称
   * @param deviceName 设备名称
   * @param bindType 1:设备绑定成功 2:设备编辑管理
   * @param checkLevel 是否开启二级验证 （true 开启，false 不开启）
   * @returns
   */
  async updateDeviceNameAndCheck(
    deviceName: string,
    bindType: string,
    checkLevel: boolean
  ): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "update_device_name_and_check");
      params.set("deviceId", this.deviceInfo.deviceId);
      params.set("deviceName", deviceName);
      params.set("bindType", bindType);
      params.set("checkLevel", String(checkLevel));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "updateDeviceNameAndCheck");
        resolve(result);
      });
    });
  }
}