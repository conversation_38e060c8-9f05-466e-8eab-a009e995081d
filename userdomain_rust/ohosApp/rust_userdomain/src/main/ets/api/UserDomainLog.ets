import { FileLogger } from '@uplus/uplog';
import { UserDomain } from './UserDomain';
import hilog from '@ohos.hilog';

const DOMAIN = 0xFFFB;

export class UserDomainLog {
  static debug(tag: string, message: string): void {
    hilog.debug(DOMAIN, tag, message);
    UserDomainLog.fileLogger()?.logToFile(tag, message);
  }

  static info(tag: string, message: string): void {
    hilog.info(DOMAIN, tag, message);
    UserDomainLog.fileLogger()?.logToFile(tag, message);
  }

  static warn(tag: string, message: string): void {
    hilog.warn(DOMAIN, tag, message);
    UserDomainLog.fileLogger()?.logToFile(tag, message);
  }

  static error(tag: string, message: string): void {
    hilog.error(DOMAIN, tag, message);
    UserDomainLog.fileLogger()?.logToFile(tag, message);
  }

  private static fileLogger(): FileLogger | undefined {
    return UserDomain.getInstance().fileLogger;
  }
}