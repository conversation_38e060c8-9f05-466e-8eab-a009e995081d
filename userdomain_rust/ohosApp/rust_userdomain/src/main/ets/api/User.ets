import { UserInfo } from './models/UserInfo';
import { JSON } from '@kit.ArkTS';
import { Device } from './Device';
import { Family } from './Family';
import {
  handleRustAddressInfoBuffer,
  handleRustAddressInfoListBuffer,
  handleRustDeviceBuffer,
  handleRustDeviceListBuffer,
  handleRustDeviceMapBuffer,
  handleRustFamilyBuffer,
  handleRustFamilyListBuffer,
  handleRustFamilyMapBuffer,
  handleRustStringBuffer,
  handleRustUserInfoBuffer,
  handleRustVoidBufferResult
} from './RustUserDomainWizard';
import { AddressArgs } from './models/AddressArgs';
import { FamilyArgs } from './models/FamilyArgs';
import { UserArgs } from './models/UserArgs';
import { FamilyInfo } from './models/FamilyInfo';
import { DeviceInfo } from './models/DeviceInfo';
import { AddressInfo } from './models/AddressInfo';
import { instanceToPlain } from 'class-transformer';
import fs from '@ohos.file.fs';
import { argsToMap, RustConstant, UserDomainResult } from './util';
import { RustChannel } from '@uplus/rust_ffi';
import { TAG } from './UserDomain';
import { FamilyAggItemArgs } from './models/FamilyAggItemArgs';

export class User {
  /**
   * 获取用户信息
   * @returns
   */
  getUserInfo(): UserInfo {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_user_info");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustUserInfoBuffer(buf, TAG, "getUserInfo");
    return result.value ?? new UserInfo();
  }

  /**
   * 获取iot用户ID
   * @returns
   */
  getIotUserId(): string {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_iot_user_id");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustStringBuffer(buf, TAG, "getIotUserId");
    return result.value ?? '';
  }

  /**
   * 获取用户中心的用户ID
   * @returns
   */
  getUcUserId(): string {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_uc_user_id");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustStringBuffer(buf, TAG, "getUcUserId");
    return result.value ?? '';
  }

  /**
   * 获取当前家庭
   * @returns
   */
  getCurrentFamily(): Family {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_current_family");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustFamilyBuffer(buf, TAG, "getCurrentFamily");
    return result.value ?? new Family(new FamilyInfo());
  }

  /**
   * 获取设备列表map
   * @returns
   */
  getDevicesMap(): Map<string, Device> {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_devices_map_by_user");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustDeviceMapBuffer(buf, TAG, "getDevicesMap");
    return result.value ?? new Map();
  }

  /**
   * 获取家庭列表map
   * @returns
   */
  getFamilysMap(): Map<string, Family> {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_familys_map_by_user");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustFamilyMapBuffer(buf, TAG, "getFamilysMap");
    return result.value ?? new Map();
  }

  /**
   * 刷新用户信息:用户基本信息、家庭信息、设备信息
   * @returns
   */
  async refreshUser(): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "refresh_user");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "refreshUser");
        resolve(result);
      });
    });
  };

  /**
   * 刷新用户基本信息
   * @returns
   */
  async refreshUserInfo(): Promise<UserDomainResult<UserInfo | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "refresh_user_info");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustUserInfoBuffer(buf, TAG, "refreshUserInfo");
        resolve(result);
      });
    });
  }

  /**
   * 创建地址
   * @param address
   * @returns
   */
  async createAddress(address: AddressArgs): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = argsToMap(address);
      params.set(RustConstant.Action, "create_address");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "createAddress");
        resolve(result);
      });
    });
  }

  /**
   * 创建家庭
   * @param family
   * @returns familyId
   */
  async createFamily(family: FamilyArgs): Promise<UserDomainResult<string | undefined>> {
    return new Promise((resolve) => {
      const params = argsToMap(family);
      params.set(RustConstant.Action, "create_family");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustStringBuffer(buf, TAG, "createFamily");
        resolve(result);
      })
    });
  }

  /**
   * 删除地址
   * @param addressId
   * @returns
   */
  async deleteAddress(addressId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "delete_address");
      params.set("addressId", addressId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "deleteAddress");
        resolve(result);
      });
    });
  }

  /**
   * 编辑地址
   * @param address
   * @returns
   */
  async editAddress(address: AddressArgs): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = argsToMap(address);
      params.set(RustConstant.Action, "edit_address");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "editAddress");
        resolve(result);
      });
    });
  }

  /**
   * 获取设备信息
   * @param deviceId
   * @returns
   */
  getDeviceById(deviceId: string): Device {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_device_by_id");
    params.set("device_id", deviceId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustDeviceBuffer(buf, TAG, "getDeviceById");
    const device = result.value;
    return device ?? new Device(new DeviceInfo());
  }

  /**
   * 获取家庭信息
   * @param familyId
   * @returns
   */
  getFamilyById(familyId: string): Family {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_family_by_id");
    params.set("family_id", familyId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustFamilyBuffer(buf, TAG, "getFamilyById");
    const family = result.value;
    return family ?? new Family(new FamilyInfo());
  }

  /**
   * 修改用户信息
   * @param userAgrs
   * @returns
   */
  async modifyUserInfo(userAgrs: UserArgs): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = argsToMap(userAgrs);
      params.set(RustConstant.Action, "modify_user_info");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "modifyUserInfo");
        resolve(result);
      });
    });
  };

  /**
   * 刷新地址列表
   * @returns
   */
  async refreshAddressList(): Promise<UserDomainResult<Array<AddressInfo> | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "refresh_address_list");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustAddressInfoListBuffer(buf, TAG, "refreshAddressList");
        resolve(result);
      });
    });
  };

  /**
   * 刷新家庭列表
   * @returns
   */
  async refreshFamilyList(): Promise<UserDomainResult<Array<Family> | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "refresh_family_list");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustFamilyListBuffer(buf, TAG, "refreshFamilyList");
        resolve(result);
      });
    });
  };

  /**
   * 刷新设备列表
   * @returns
   */
  async refreshDeviceList(): Promise<UserDomainResult<Array<Device> | undefined>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "refresh_device_list");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustDeviceListBuffer(buf, TAG, "refreshDeviceList");
        resolve(result);
      });
    });
  };

  /**
   * 回复家庭邀请
   * @param inviteCode 邀请码
   * @param familyId 家庭id
   * @param agree 是否同意
   * @returns
   */
  async replyFamilyInvite(
    inviteCode: string,
    familyId: string,
    memberName: string,
    agree: boolean
  ): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "reply_family_invite");
      params.set("family_id", familyId);
      params.set("invite_code", inviteCode);
      params.set("member_name", memberName);
      params.set("agree", String(agree));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "replyFamilyInvite");
        resolve(result);
      });
    });
  };

  /**
   * @param familyId
   */
  setCurrentFamily(familyId: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "set_current_family");
      params.set("family_id", familyId);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "setCurrentFamily");
        resolve(result);
      });
    });
  }

  /**
   * 修改用户头像
   * @param userAvatar 用户头像
   * @returns
   */
  async modifyUserAvatar(userAvatar: string): Promise<UserDomainResult<string | undefined>> {
    return new Promise((resolve, reject) => {
      try {
        let imagePath = this.copyImgToCache(userAvatar);
        const params = new Map<string, string>();
        params.set(RustConstant.Action, "modify_user_avatar");
        params.set("image_path", imagePath);
        RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
          try {
            this.deleteFileFromCache(imagePath);
          } catch (e) {
            reject(e);
          }
          const result = handleRustStringBuffer(buf, TAG, "modifyUserAvatar");
          resolve(result);
        });
      } catch (e) {
        reject(e);
      }
    });
  };

  /**
   * 确认设备共享关系
   * @param shareUuid 共享ID
   * @param callback 回调
   */
  confirmDeviceSharingRelation(shareUuid: string): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "confirm_device_sharing_relation");
      params.set("share_uuid", shareUuid);
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "confirmDeviceSharingRelation");
        resolve(result);
      })
    });
  }

  /**
   * 取消设备共享关系
   * @param shareUuids 共享ID列表
   * @param callback 回调
   */
  cancelDeviceSharingRelation(shareUuids: string[]): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "cancel_device_sharing_relation");
      params.set("share_uuid", JSON.stringify(instanceToPlain(shareUuids)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "cancelDeviceSharingRelation");
        resolve(result);
      })
    });
  }

  /**
   * 修改聚合开关状态
   * @param source 来源
   * @param familyAgg 家庭聚合信息列表
   * @returns 修改结果
   */
  async modifyAggregationSwitch(source: string, familyAgg: FamilyAggItemArgs[]): Promise<UserDomainResult<void>> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "modify_aggregation_switch");
      params.set("source", source);
      params.set("family_agg", JSON.stringify(instanceToPlain(familyAgg)));
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "cancelDeviceSharingRelation");
        resolve(result);
      });
    });
  }

  /**
   * 获取用户默认地址
   * @returns 用户默认地址
   */
  getDefaultAddress(): AddressInfo {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_default_address");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustAddressInfoBuffer(buf, TAG, "getDefaultAddress");
    const info = result.value;
    return info ?? new AddressInfo();
  }

  /**
   * 获取用户地址列表
   * @returns 用户地址列表
   */
  getAddressList(): Array<AddressInfo> {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_address_list");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustAddressInfoListBuffer(buf, TAG, "getAddressList");
    const list = result.value;
    return list ?? new Array();
  }

  copyImgToCache(photoImgPath: string): string {
    const file = fs.openSync(photoImgPath, fs.OpenMode.READ_ONLY)
    let fileFD = file.fd
    let destPath = getContext().cacheDir
    let fileName = Date.now().toString()
    const ext = 'jpg'
    let fullPath = destPath + '/' + fileName + '.' + ext
    fs.copyFileSync(fileFD, fullPath)
    fs.closeSync(fileFD)
    return fullPath
  }

  deleteFileFromCache(filePath: string): boolean {
    try {
      fs.unlinkSync(filePath);
      return true;
    } catch (err) {
      console.error(`Error deleting file ${filePath}:`, err);
      return false;
    }
  }
}