import { ByteBuffer } from '@ohos/flatbuffers';
import { instanceToPlain } from 'class-transformer';
import { UserDomainFlat } from '../com/haier/uhome/uplus/rust/userdomain/fbs';

export function isValidEnumValue<T extends object>(value: number, enumObject: T): boolean {
  if (value) {
    const values = Object.values(enumObject);
    return values.includes(value);
  } else {
    return false;
  }
}

export enum RustConstant {
  Action = "action",
  UniqueId = "listener_id",
  LibName = "lib_userdomain",
  Call_Failure = "rust method call failure",
}

export enum UserDomainCode {
  Success = "000000",
  IllegalParameters = "900003",
  UserStateUnLogin = "110001",
  RustCallFailure = "999999",
  NoCode = "999998"
}

export enum ErrorMessage {
  RustCallFailure = "buf is null",
  NoCode = "no flat code",
  NoError = "no flat error",
}

export class UserDomainResult<T> {
  value?: T;
  code: string;
  error: string;

  constructor(code: string, error: string, value?: T) {
    this.code = code;
    this.error = error;
    this.value = value;
  }

  isSuccess(): boolean {
    return this.code === UserDomainCode.Success;
  }
}

export function handleRustFbsWrapperBuffer<T>(
  buf: ArrayBuffer | null,
  containerConstructor: new () => T,
): UserDomainResult<T> {
  if (buf) {
    const flat = UserDomainFlat.getRootAsUserDomainFlat(new ByteBuffer(new Uint8Array(buf)));
    const code = flat.code() ?? UserDomainCode.NoCode;
    const error = flat.error() ?? ErrorMessage.NoError;
    if (UserDomainCode.Success == code) {
      const container = flat.container(new containerConstructor()) as T | null;
      return new UserDomainResult(code, error, container);
    } else {
      return new UserDomainResult(code, error);
    }
  } else {
    return new UserDomainResult(UserDomainCode.RustCallFailure, ErrorMessage.RustCallFailure);
  }
}

export function handleRustWrapperBuffer<W extends { value(): T }, T>(
  buf: ArrayBuffer | null,
  wrapperConstructor: new () => W,
): UserDomainResult<T> {
  if (buf) {
    const flat = UserDomainFlat.getRootAsUserDomainFlat(new ByteBuffer(new Uint8Array(buf)));
    const code = flat.code() ?? UserDomainCode.NoCode;
    const error = flat.error() ?? ErrorMessage.NoError;
    if (UserDomainCode.Success == code) {
      const container = flat.container(new wrapperConstructor()) as W | null;
      return new UserDomainResult(code, error, container?.value());
    } else {
      return new UserDomainResult(code, error);
    }
  } else {
    return new UserDomainResult(UserDomainCode.RustCallFailure, ErrorMessage.RustCallFailure);
  }
}

/**
 * NAPI 很多参数使用的是 class 对象，这里把对象转为 map
 */
export function argsToMap(obj: object): Map<string, string> {
  const map = new Map<string, string>();
  for (const [key, value] of Object.entries(obj)) {
    // 排除字段值为 undefined 的情况
    if (value === undefined) {
      continue;
    }
    const t = typeof value;
    if (t === 'string' || t === 'number' || t === 'boolean') {
      map.set(key, String(value));
    }
    if (t === 'object') {
      map.set(key, JSON.stringify(instanceToPlain(value)));
    }
  }
  return map;
}
