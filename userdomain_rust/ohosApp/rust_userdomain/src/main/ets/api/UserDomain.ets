import { AuthData } from './models/AuthData';
import { User } from './User';
import {
  handleRustAuthDataBuffer,
  handleRustBoolBuffer,
  handleRustNumberBuffer,
  handleRustStringBuffer,
  handleRustVoidBuffer,
  handleRustVoidBufferResult,
} from './RustUserDomainWizard';
import { RustChannel } from '@uplus/rust_ffi';
import { ByteBuffer } from '@ohos/flatbuffers';
import { FileLogger } from '@uplus/uplog';
import { UserDomainLog } from './UserDomainLog';
import {
  FBSAuthData,
  FBSDeviceList,
  FBSFamily,
  FBSFamilyList,
  FBSUserdomainMessage,
  FBSUserInfo
} from '../com/haier/uhome/uplus/rust/userdomain/fbs';
import { isValidEnumValue, RustConstant, UserDomainCode } from './util';
import { Device, Family, UserInfo } from '../../../../Index';

export const TAG = "RustUserDomain";

export enum UserDomainState {
  UnLogin = -1, //未登录
  Logining = 0, //登录中
  Login = 1, //已登录
  LogingOut = 2, //登出中
}

export enum UserDomainPlatform {
  Homeland = 0, //智家
  SouthEastAsia = 1, //海外
  HomelandSYN = 2, //三翼鸟
  HomelandShop = 3, //商城
  Harmony = 4, //鸿蒙
  HomelandLite = 5, //智家极简版
  Unknow = 6, //未知
}

export enum AppNetworkEnv {
  Development = 0, //开发
  Acceptance = 1, //验收
  Production = 2, //生产
}

export enum UserDomainEvent {
  MessageTokenMismatchDevice = 1,
  MessageTokenInvalid = 2,
  MessageLogout = 3,
  MessageCancelLogin = 4,
  MessageLoginElseWhere = 5,
  MessageRefreshTokenFailed = 6,
  MessageRefreshComplete = 7,
  MessageRefreshFailure = 8,
  MessageRefreshTokenSuccess = 9,
  MessageUserInfoRefreshSuccess = 10,
  MessageUserInfoRefreshFailed = 11,
  MessageDeviceListRefreshSuccess = 12,
  MessageDeviceListRefreshFailed = 13,
  MessageFamilyListRefreshSuccess = 14,
  MessageFamilyListRefreshFailed = 15,
  MessageCurrentFamilyChanged = 16,
}

export type UserDomainEventData = AuthData | UserInfo | Array<Device> | Array<Family> | Family | null;

export class UserDomain {
  private static userDomain: UserDomain
  public user: User;
  public userDomainSetting: UserDomainSetting;
  fileLogger?: FileLogger;

  public static getInstance() {
    if (!UserDomain.userDomain) {
      UserDomain.userDomain = new UserDomain();
    }
    return UserDomain.userDomain;
  }

  private constructor() {
    this.user = new User();
    this.userDomainSetting = new UserDomainSetting();
  }

  getUser(): User {
    return this.user;
  }

  init(
    app_id: string,
    app_key: string,
    app_version: string,
    client_id: string,
    network_env: AppNetworkEnv,
    gray_mode: boolean,
    platform: UserDomainPlatform,
    debug: boolean,
  ) {
    this.fileLogger = new FileLogger("rust-userdomain", debug);
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "init");
    params.set("app_id", app_id);
    params.set("app_key", app_key);
    params.set("app_version", app_version);
    params.set("client_id", client_id);
    params.set("network_env", network_env.toString());
    params.set("gray_mode", String(gray_mode));
    params.set("platform", platform.toString());
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "init");
  }

  updateOauthData(
    accessToken: string,
    refreshToken: string,
    uhomeAccessToken: string,
    expiresIn: number,
    scope: string,
    tokenType: string,
    uhomeUserId: string,
    ucUserId: string,
  ): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "update_oauth_data");
    params.set("access_token", accessToken);
    params.set("refresh_token", refreshToken);
    params.set("uhome_access_token", uhomeAccessToken);
    params.set("expires_in", expiresIn.toString());
    params.set("scope", scope);
    params.set("token_type", tokenType);
    params.set("uhome_user_id", uhomeUserId);
    params.set("uc_user_id", ucUserId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustVoidBufferResult(buf, TAG, "updateOauthData");
    return result.isSuccess();
  }

  getOauthData(): AuthData {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_oauth_data");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const result = handleRustAuthDataBuffer(buf, TAG, "getOauthData");
    return result.value ?? new AuthData();
  }

  cancelLogin() {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "cancel_login");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "cancelLogin")
  }

  isRefreshCompleted(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "is_refresh_completed");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const ret = handleRustBoolBuffer(buf, TAG, "isRefreshCompleted");
    return ret.value ?? false;
  }

  isRefreshDeviceListCompleted(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "is_refresh_device_list_completed");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const ret = handleRustBoolBuffer(buf, TAG, "isRefreshDeviceListCompleted");
    return ret.value ?? false;
  }

  isRefreshFamilyListCompleted(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "is_refresh_family_list_completed");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const ret = handleRustBoolBuffer(buf, TAG, "isRefreshFamilyListCompleted");
    return ret.value ?? false;
  }

  isRefreshUserCompleted(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "is_refresh_user_completed");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const ret = handleRustBoolBuffer(buf, TAG, "isRefreshUserCompleted");
    return ret.value ?? false;
  }

  state(): UserDomainState {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_state");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const state = handleRustNumberBuffer(buf, TAG, "state");
    if (isValidEnumValue(state.value, UserDomainState)) {
      return state.value as UserDomainState;
    } else {
      return UserDomainState.UnLogin;
    }
  }

  autoRefreshToken() {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "auto_refresh_token");
    RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
      handleRustVoidBuffer(buf, TAG, "autoRefreshToken");
    });
  }

  addObserver(listener: (eventType: UserDomainEvent, eventData: UserDomainEventData) => void): string {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "add_observer");
    params.set(RustConstant.UniqueId, "0");
    const buf = RustChannel.getInstance().manageRustBufferListenerWithData(RustConstant.LibName, params, (eventBuf) => {
      const eventFlat = FBSUserdomainMessage.getRootAsFBSUserdomainMessage(new ByteBuffer(new Uint8Array(eventBuf)));
      const code = eventFlat.code();
      UserDomainLog.debug(TAG, `addObserver: callback: ${code}`);
      if (!isValidEnumValue(code, UserDomainEvent)) {
        return;
      }
      const userDomainEventEnum = code as UserDomainEvent;
      let userDomainEventData: UserDomainEventData;
      switch (userDomainEventEnum) {
        case UserDomainEvent.MessageRefreshTokenSuccess:
          const fbsAuthData = eventFlat.container(new FBSAuthData()) as FBSAuthData;
          userDomainEventData = AuthData.fromFbs(fbsAuthData);
          break;
        case UserDomainEvent.MessageUserInfoRefreshSuccess:
          const fbsUserInfo = eventFlat.container(new FBSUserInfo()) as FBSUserInfo
          userDomainEventData = UserInfo.fromFbs(fbsUserInfo)
          break;
        case UserDomainEvent.MessageDeviceListRefreshSuccess:
          const fbsDeviceList = eventFlat.container(new FBSDeviceList()) as FBSDeviceList
          userDomainEventData = Device.fromFbsList(fbsDeviceList)
          break;
        case UserDomainEvent.MessageFamilyListRefreshSuccess:
          const fbsFamilyList = eventFlat.container(new FBSFamilyList()) as FBSFamilyList
          userDomainEventData = Family.fromFbsList(fbsFamilyList)
          break;
        case UserDomainEvent.MessageCurrentFamilyChanged:
          const fbsFamily = eventFlat.container(new FBSFamily()) as FBSFamily
          userDomainEventData = Family.fromFbs(fbsFamily)
          break;
        case UserDomainEvent.MessageRefreshTokenFailed:
        case UserDomainEvent.MessageTokenInvalid:
        case UserDomainEvent.MessageTokenMismatchDevice:
        case UserDomainEvent.MessageLogout:
        case UserDomainEvent.MessageLoginElseWhere:
        case UserDomainEvent.MessageRefreshTokenFailed:
        case UserDomainEvent.MessageRefreshFailure:
        case UserDomainEvent.MessageUserInfoRefreshFailed:
        case UserDomainEvent.MessageDeviceListRefreshFailed:
        case UserDomainEvent.MessageFamilyListRefreshFailed:
          userDomainEventData = null
          break;
        default:
          userDomainEventData = null
          break;
      }
      listener(userDomainEventEnum, userDomainEventData);
    });
    const listenerId = handleRustStringBuffer(buf, TAG, "addObserver");
    return listenerId.value ?? '';
  }

  removeObserver(observerId: string) {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "remove_observer");
    params.set("observer_id", observerId);
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "removeObserver");
  }

  async logout(): Promise<boolean> {
    return new Promise((resolve) => {
      const params = new Map<string, string>();
      params.set(RustConstant.Action, "logout");
      RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
        const result = handleRustVoidBufferResult(buf, TAG, "logout");
        resolve(result.isSuccess());
      });
    });
  }

  scheduleRefreshToken() {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "schedule_refresh_token");
    RustChannel.getInstance().getRustBufferAsync(RustConstant.LibName, params, (buf) => {
      handleRustVoidBuffer(buf, TAG, "scheduleRefreshToken");
    });
  }
}

export class UserDomainSetting {
  //获取http请求重试间隔
  getHttpRequestRetryDelay(): number {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_http_request_retry_delay");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const time = handleRustNumberBuffer(buf, TAG, "getHttpRequestRetryDelay");
    return time.value ?? -1;
  }

  //设置http请求重试间隔
  setHttpRequestRetryDelay(delay: number) {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "set_http_request_retry_delay");
    params.set("http_request_retry_delay", delay.toString());
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "setHttpRequestRetryDelay");
  }

  //设置平台
  setUserDomainPlatform(platform: UserDomainPlatform) {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "set_user_domain_platform");
    params.set("user_domain_platform", platform.toString());
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "setUserDomainPlatform");
  }

  //获取平台
  getUserDomainPlatform(): UserDomainPlatform {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "get_user_domain_platform");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const platform = handleRustNumberBuffer(buf, TAG, "getUserDomainPlatform");
    if (isValidEnumValue(platform.value, UserDomainPlatform)) {
      return platform.value as UserDomainPlatform;
    } else {
      return UserDomainPlatform.Unknow;
    }
  }

  //设置是否刷新家庭列表
  setRefreshFamilyListEnable(enabled: boolean) {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "set_refresh_family_list_enable");
    params.set("refresh_family_list_enable", String(enabled));
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "setRefreshFamilyListEnable");
  }

  //获取是否刷新家庭列表
  isRefreshFamilyListEnable(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "is_refresh_family_list_enable");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const ret = handleRustBoolBuffer(buf, TAG, "isRefreshFamilyListEnable");
    return ret.value ?? false;
  }

  //设置是否刷新设备列表
  setRefreshDeviceListEnable(enabled: boolean) {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "set_refresh_device_list_enable");
    params.set("refresh_device_list_enable", String(enabled));
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    handleRustVoidBuffer(buf, TAG, "setRefreshDeviceListEnable");
  }

  //获取是否刷新设备列表
  isRefreshDeviceListEnable(): boolean {
    const params = new Map<string, string>();
    params.set(RustConstant.Action, "is_refresh_device_list_enable");
    const buf = RustChannel.getInstance().getRustBuffer(RustConstant.LibName, params);
    const ret = handleRustBoolBuffer(buf, TAG, "isRefreshDeviceListEnable");
    return ret.value ?? false;
  }

  //获取计划刷新token时间
  planRefreshTokenTime(): number {
    // let result =
    //   rustUserDomain.userdomainActionDispatch("get_plan_refresh_token_time_millis", {}) as UserDomainNapiResult
    // if (result.numberValue == undefined) {
    //   return -1
    // }
    // return result.numberValue;
    return 0;
  }

  //设置计划刷新token时间
  setPlanRefreshTokenTime(time: number) {
    // let args: object = new Args()
    // args["plan_refresh_token_time_millis"] = time
    // rustUserDomain.userdomainActionDispatch("set_plan_refresh_token_time_millis", args)
  }
}