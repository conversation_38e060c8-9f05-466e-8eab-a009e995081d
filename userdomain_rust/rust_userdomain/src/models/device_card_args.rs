use serde::{Deserialize, Serialize};

/// 设备卡片状态参数
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DeviceCardStatusArgs {
    /// 家庭ID
    pub family_id: String,
    /// 设备排序列表
    pub card_order_list: Option<Vec<String>>,
    /// 大卡片设备列表
    pub big_card_list: Option<Vec<String>>,
    /// 中卡片设备列表
    pub middle_card_list: Option<Vec<String>>,
    /// 小卡片设备列表
    pub small_card_list: Option<Vec<String>>,
}

impl DeviceCardStatusArgs {
    /// 创建一个新的 `DeviceCardStatusArgs` 实例
    pub fn new(
        family_id: String,
        card_order_list: Option<Vec<String>>,
        big_card_list: Option<Vec<String>>,
        middle_card_list: Option<Vec<String>>,
        small_card_list: Option<Vec<String>>,
    ) -> Self {
        Self {
            family_id,
            card_order_list,
            big_card_list,
            middle_card_list,
            small_card_list,
        }
    }
}

/// 设备聚合卡片项参数
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AggCardItemArgs {
    /// 聚合卡片内部聚合设备及排序
    pub sort_list: Vec<String>,
    /// 大卡列表
    pub big_card_list: Vec<String>,
    /// 小卡列表
    pub small_card_list: Vec<String>,
    /// 聚合类型（0-灯光 1-窗帘 2-环境 3-非网器 4-长期离线 5-摄像头）
    pub agg_type: String,
}

impl AggCardItemArgs {
    /// 创建一个新的 `AggCardItemArgs` 实例
    pub fn new(
        sort_list: Vec<String>,
        big_card_list: Vec<String>,
        small_card_list: Vec<String>,
        agg_type: String,
    ) -> Self {
        Self {
            sort_list,
            big_card_list,
            small_card_list,
            agg_type,
        }
    }
}

/// 设备聚合状态参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceAggregationArgs {
    /// 家庭ID
    pub family_id: String,
    /// 聚合卡片信息列表
    pub agg_card: Vec<AggCardItemArgs>,
}

impl DeviceAggregationArgs {
    /// 创建一个新的 `DeviceAggregationArgs` 实例
    pub fn new(family_id: String, agg_card: Vec<AggCardItemArgs>) -> Self {
        Self {
            family_id,
            agg_card,
        }
    }
}

/// 聚合开关状态参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregationSwitchArgs {
    /// 来源
    pub source: String,
    /// 家庭聚合信息列表
    pub family_agg: Vec<FamilyAggItemArgs>,
}

impl AggregationSwitchArgs {
    /// 创建一个新的 `AggregationSwitchArgs` 实例
    pub fn new(source: String, family_agg: Vec<FamilyAggItemArgs>) -> Self {
        Self { source, family_agg }
    }
}

/// 家庭聚合项参数
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct FamilyAggItemArgs {
    /// 家庭ID
    pub family_id: String,
    /// 灯光聚合开关状态（0-关闭 1-开启）
    pub light_agg: Option<String>,
    /// 窗帘聚合开关状态（0-关闭 1-开启）
    pub curtain_agg: Option<String>,
    /// 环境聚合开关状态（0-关闭 1-开启）
    pub env_agg: Option<String>,
    /// 长期离线聚合开关状态（0-关闭 1-开启）
    pub offline_agg: Option<String>,
    /// 非网器聚合开关状态（0-关闭 1-开启）
    pub nonnet_agg: Option<String>,
    /// 摄像头聚合开关状态（0-关闭 1-开启）
    pub camera_agg: Option<String>,
}

impl FamilyAggItemArgs {
    /// 创建一个新的 `FamilyAggItemArgs` 实例
    pub fn new(
        family_id: String,
        light_agg: Option<String>,
        curtain_agg: Option<String>,
        env_agg: Option<String>,
        offline_agg: Option<String>,
        nonnet_agg: Option<String>,
        camera_agg: Option<String>,
    ) -> Self {
        Self {
            family_id,
            light_agg,
            curtain_agg,
            env_agg,
            offline_agg,
            nonnet_agg,
            camera_agg,
        }
    }
}
