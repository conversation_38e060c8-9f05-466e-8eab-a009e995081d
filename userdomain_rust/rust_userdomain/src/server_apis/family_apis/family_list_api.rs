use request_rust::request::client::ReqClient;
use request_rust::Method;
use serde::Deserialize;

use crate::api::error::UserDomainError;
use crate::api::user_domain::UserDomainPlatform;
use crate::api::user_domain_manager::UserDomainManager;
use crate::api::{error::Result, family::Family};
use crate::models::family_info::FamilyInfo;

#[derive(Debug, Deserialize, Default)]
pub struct FamilyList {
    #[serde(rename = "createfamilies", default)]
    pub create_families: Vec<FamilyInfo>,
    #[serde(rename = "joinfamilies", default)]
    pub join_families: Vec<FamilyInfo>,
}

impl FamilyList {
    pub fn into_families(self) -> Vec<Family> {
        self.create_families
            .into_iter()
            .chain(self.join_families)
            .map(|family_info| Family { family_info })
            .collect::<Vec<Family>>()
    }
}

pub async fn query_family_list(client: &ReqClient) -> Result<Vec<Family>> {
    let platform = UserDomainManager::get_instance()
        .get_setting()
        .get_user_domain_platform();
    let url = match platform {
        UserDomainPlatform::HomelandLite => "/api-gw/wisdomfamily/family/simple/v1/family/list",
        UserDomainPlatform::SouthEastAsia => "/wisdomfamily/family/v3/family/list",
        _ => "/api-gw/wisdomfamily/family/refactor/v2/family/list",
    };
    let result = client
        .execute_request::<(), FamilyList>(url, Method::POST, Some(&()))
        .await;
    if let Ok(family_list) = result {
        Ok(family_list.into_families())
    } else {
        Err(UserDomainError::HttpRequstFaild(result.unwrap_err()))
    }
}
