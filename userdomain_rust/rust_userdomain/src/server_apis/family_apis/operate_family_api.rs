use request_rust::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>erValue, Method};
use serde::{Deserialize, Serialize};

use crate::api::error::{Result, UserDomainError};
use crate::api::user_domain::UserDomainPlatform;
use crate::api::user_domain_manager::UserDomainManager;
use crate::models::family_args::FamilyArgs;
use crate::models::location::Location;
use request_rust::request::client::ReqClient;

pub async fn exit_family_as_admin(
    server_client: &ReqClient,
    family_id: String,
    user_id: String,
) -> Result<()> {
    let result = server_client
        .execute_request::<OperateFamilyReq, ()>(
            "/api-gw/wisdomfamily/family/v1/familyAdmin/exit",
            Method::POST,
            Some(&OperateFamilyReq::new(family_id, user_id)),
        )
        .await;
    if let Ok(()) = result {
        Ok(())
    } else {
        Err(UserDomainError::HttpRequstFaild(result.unwrap_err()))
    }
}

/// 家庭成员退出家庭
pub async fn exit_family_as_member(server_client: &ReqClient, family_id: String) -> Result<()> {
    let result = server_client
        .execute_request(
            "/api-gw/wisdomfamily/family/refactor/v1/member/exit",
            Method::POST,
            Some(&OperateFamilyReq::new_with_family_id(family_id)),
        )
        .await;
    if let Ok(()) = result {
        Ok(())
    } else {
        Err(UserDomainError::HttpRequstFaild(result.unwrap_err()))
    }
}

pub async fn delete_family_as_admin(server_client: &ReqClient, family_id: &str) -> Result<()> {
    let platform = UserDomainManager::get_instance()
        .get_setting()
        .get_user_domain_platform();
    let url = if platform == UserDomainPlatform::SouthEastAsia {
        "/wisdomfamily/family/v1/family/destroy"
    } else {
        "/api-gw/wisdomfamily/family/v1/family/destroy"
    };
    let result = server_client
        .execute_request::<OperateFamilyReq, ()>(
            url,
            Method::POST,
            Some(&OperateFamilyReq::new_with_family_id(family_id.to_string())),
        )
        .await;
    if let Ok(()) = result {
        Ok(())
    } else {
        Err(UserDomainError::HttpRequstFaild(result.unwrap_err()))
    }
}

pub async fn reassign_family_administrator(
    server_client: ReqClient,
    family_id: &str,
    user_id: &str,
) -> Result<()> {
    let mut header = HeaderMap::new();
    header.insert(
        "supportAccountUserId",
        HeaderValue::from_str("true").unwrap(),
    );
    let result = server_client
        .extend_header(header)
        .execute_request(
            "/api-gw/wisdomfamily/family/v1/family/manager/change",
            Method::POST,
            Some(&OperateFamilyReq::new(
                family_id.to_string(),
                user_id.to_string(),
            )),
        )
        .await;
    if let Ok(()) = result {
        Ok(())
    } else {
        Err(UserDomainError::HttpRequstFaild(result.unwrap_err()))
    }
}

pub async fn modify_family_info(
    server_client: &ReqClient,
    family_args: FamilyArgs,
    family_id: String,
) -> Result<()> {
    let platform = UserDomainManager::get_instance()
        .get_setting()
        .get_user_domain_platform();
    let url = if platform == UserDomainPlatform::SouthEastAsia {
        "/wisdomfamily/family/v1/family/edit"
    } else {
        "/api-gw/wisdomfamily/family/v1/family/edit"
    };
    let _ = server_client
        .execute_request::<EditFamilyInfoReq, ()>(
            url,
            Method::POST,
            Some(&EditFamilyInfoReq::new(family_args, family_id)),
        )
        .await;
    Ok(())
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OperateFamilyReq {
    family_id: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    user_id: String,
}
impl OperateFamilyReq {
    pub fn new(family_id: String, user_id: String) -> Self {
        Self { family_id, user_id }
    }

    pub fn new_with_family_id(family_id: String) -> Self {
        Self {
            family_id,
            user_id: String::default(),
        }
    }
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct EditFamilyInfoReq {
    family_id: String,
    family_name: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    family_position: String,
    #[serde(skip_serializing_if = "Location::is_empty")]
    family_location: Location,
}

impl EditFamilyInfoReq {
    pub fn new(family_args: FamilyArgs, family_id: String) -> Self {
        Self {
            family_id,
            family_name: family_args.name,
            family_position: family_args.position,
            family_location: Location {
                longitude: Some(family_args.longitude.parse::<f64>().unwrap_or(0.0)),
                latitude: Some(family_args.latitude.parse::<f64>().unwrap_or(0.0)),
                city_code: Some(family_args.city_code),
            },
        }
    }
}
