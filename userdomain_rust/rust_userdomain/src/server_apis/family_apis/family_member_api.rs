use request_rust::request::client::ReqClient;
use request_rust::{HeaderMap, HeaderValue, Method};
use serde::{Deserialize, Serialize};

use crate::api::error::Result;
use crate::api::user_domain_manager::UserDomainManager;
use crate::models::empty_data_result::EmptyDataResult;
use crate::models::virtual_member_args::VirtualMemberArgs;

/// 删除家庭成员
///
/// # Params
/// * `family_id` 需要查询的家庭id
/// * `member_id` 删除的家庭成员的userid
/// # Returns
/// * 查询成功时，返回值无参数
/// * 查询失败时，会返回UserDomainError
pub async fn delete_family_member_as_admin_connect(
    server_client: ReqClient,
    family_id: &str,
    member_id: &str,
) -> Result<()> {
    let mut header = HeaderMap::new();
    header.insert(
        "supportAccountUserId",
        HeaderValue::from_str("true").unwrap(),
    );
    let body = FamilyMemberBody::new_with_family_id_and_member_id(
        family_id.to_string(),
        member_id.to_string(),
    );
    server_client
        .extend_header(header)
        .execute_request::<FamilyMemberBody, ()>(
            "/api-gw/wisdomfamily/family/refactor/v1/member/destroy",
            Method::POST,
            Some(&body),
        )
        .await?;
    Ok(())
}

/// 邀请家庭成员
///
/// # Params
/// * `user_id` 被邀请用户的用户中心ID
/// * `nickname` 被邀请用户的用户昵称
/// * `family_id` 家庭ID
/// * `member_role` 身份备注（可选）
/// * `member_type` 角色类型（1: 管理员, 2: 成员）
/// # Returns
/// * 查询成功时，返回值无参数
/// * 查询失败时，会返回UserDomainError
pub async fn admin_invite_member_connect(
    server_client: &ReqClient,
    user_id: &str,
    nickname: &str,
    family_id: &str,
    member_role: &str,
    member_type: i32,
) -> Result<()> {
    let mut header = HeaderMap::new();
    header.insert(
        "supportAccountUserId",
        HeaderValue::from_str("true").unwrap(),
    );

    let body = FamilyInvitationRequest {
        user_id: user_id.to_string(),
        nickname: nickname.to_string(),
        family_id: family_id.to_string(),
        member_role: member_role.to_string(),
        member_type,
    };

    server_client
        .clone()
        .extend_header(header)
        .execute_request::<FamilyInvitationRequest, EmptyDataResult>(
            "/api-gw/wisdomfamily/family/refactor/v1/family/invitation",
            Method::POST,
            Some(&body),
        )
        .await?;
    Ok(())
}
pub async fn query_first_member(
    server_client: &ReqClient,
    family_id: String,
) -> Result<FamilyMemberResp> {
    let result = server_client
        .execute_request(
            "/api-gw/wisdomfamily/family/v1/member/first",
            Method::POST,
            Some(&FamilyMemberBody::new_with_family_id(family_id)),
        )
        .await?;
    Ok(result)
}

pub async fn add_virtual_member(
    server_client: &ReqClient,
    family_id: String,
    member_id: String,
    member_name: String,
) -> Result<()> {
    server_client
        .execute_request::<VirtualMemberBody, VirtualMemberResp>(
            "/api-gw/wisdomfamily/family/v1/virtual/member/join",
            Method::POST,
            Some(&VirtualMemberBody::new(family_id, member_id, member_name)),
        )
        .await?;
    Ok(())
}

/// 编辑虚拟角色信息
///
/// # Params
/// * `virtual_member_args` 虚拟角色信息
pub async fn modify_virtual_member_connect(
    server_client: ReqClient,
    virtual_member_args: &VirtualMemberArgs,
) -> Result<()> {
    let access_token = UserDomainManager::get_instance()
        .get_user_domain()
        .get_oauth_data()
        .access_token;
    let mut header = HeaderMap::new();
    header.insert(
        "accountToken",
        HeaderValue::from_str(&access_token).unwrap(),
    );
    let body = ModifyVirtualMemberBody {
        id: virtual_member_args.member_id.to_string(),
        nick_name: virtual_member_args.member_name.to_string(),
        avatar_url: virtual_member_args.avatar_url.to_string(),
        is_creator: virtual_member_args.is_creator_to_i32(),
        birthday: virtual_member_args.birthday.to_string(),
    };

    server_client
        .extend_header(header)
        .execute_request::<ModifyVirtualMemberBody, EmptyDataResult>(
            "/oauthserver/virtual/v1/info/modify",
            Method::POST,
            Some(&body),
        )
        .await?;
    Ok(())
}

/// 修改家庭成员在家庭里的身份
///
/// # Params
/// * `member_id` 成员id
/// * `family_id` 家庭id
/// * `member_role` 成员角色
pub async fn modify_member_role_connect(
    server_client: ReqClient,
    family_id: &str,
    member_id: &str,
    member_role: &str,
) -> Result<()> {
    let mut header = HeaderMap::new();
    header.insert(
        "supportAccountUserId",
        HeaderValue::from_str("true").unwrap(),
    );

    let body = ModifyMemberRoleBody {
        family_id: family_id.to_string(),
        member_id: member_id.to_string(),
        member_role: member_role.to_string(),
    };

    server_client
        .extend_header(header)
        .execute_request::<ModifyMemberRoleBody, ()>(
            "/api-gw/wisdomfamily/family/v2/family/memberRole/modify",
            Method::POST,
            Some(&body),
        )
        .await?;
    Ok(())
}

/// 修改家庭虚拟成员在家庭里的身份
///
/// # Params
/// * `member_id` 成员id
/// * `family_id` 家庭id
/// * `member_role` 成员角色
pub async fn modify_virtual_member_role_connect(
    server_client: ReqClient,
    family_id: &str,
    member_id: &str,
    member_role: &str,
) -> Result<()> {
    let mut header = HeaderMap::new();
    header.insert(
        "supportAccountUserId",
        HeaderValue::from_str("false").unwrap(),
    );

    let body = ModifyMemberRoleBody {
        family_id: family_id.to_string(),
        member_id: member_id.to_string(),
        member_role: member_role.to_string(),
    };

    server_client
        .extend_header(header)
        .execute_request::<ModifyMemberRoleBody, ()>(
            "/api-gw/wisdomfamily/family/v2/family/memberRole/modify/fictitious",
            Method::POST,
            Some(&body),
        )
        .await?;
    Ok(())
}

#[derive(Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct VirtualMemberBody {
    pub family_id: String,
    #[serde(rename = "virtualUCId")]
    pub virtual_ucid: String,
    pub user_family_name: String,
}

impl VirtualMemberBody {
    pub fn new(family_id: String, virtual_ucid: String, user_family_name: String) -> Self {
        Self {
            family_id,
            virtual_ucid,
            user_family_name,
        }
    }
}

#[derive(Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct FamilyMemberBody {
    pub family_id: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub member_id: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub member_name: String,
}
impl FamilyMemberBody {
    pub fn new(family_id: String, member_id: String, member_name: String) -> Self {
        Self {
            family_id,
            member_id,
            member_name,
        }
    }

    pub fn new_with_family_id(family_id: String) -> Self {
        Self {
            family_id,
            member_id: String::default(),
            member_name: String::default(),
        }
    }

    pub fn new_with_family_id_and_member_id(family_id: String, member_id: String) -> Self {
        Self {
            family_id,
            member_id,
            member_name: String::default(),
        }
    }
}

#[derive(Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct FamilyInvitationRequest {
    /// 被邀请用户的用户中心ID
    pub user_id: String,
    /// 被邀请用户的用户昵称
    pub nickname: String,
    /// 家庭ID
    pub family_id: String,
    /// 身份备注（非必须）
    #[serde(skip_serializing_if = "String::is_empty")]
    pub member_role: String,
    /// 角色类型（1: 管理员, 2: 成员）
    pub member_type: i32,
}
#[derive(Default, Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct FamilyMemberResp {
    pub member_name: String,
    pub user_id: String,
    pub name: String,
}

#[derive(Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct ModifyVirtualMemberBody {
    pub id: String,
    pub nick_name: String,
    pub avatar_url: String,
    #[serde(rename = "isCreater")]
    pub is_creator: i32,
    pub birthday: String,
}

#[derive(Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct ModifyMemberRoleBody {
    pub family_id: String,
    pub member_id: String,
    pub member_role: String,
}

#[derive(Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase", default)]
pub struct VirtualMemberResp {
    pub user_id: String,
}
