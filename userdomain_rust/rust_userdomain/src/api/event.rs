use crate::api::device::Device;
use crate::api::family::Family;
use crate::models::auth_data::AuthData;
use crate::models::user_info::UserInfo;

pub const CHANNEL_MESSAGE: &str = "userdomain_channel_message";
pub const CHANNEL_OPERATE: &str = "userdomain_channel_operate";

#[derive(<PERSON><PERSON>, Debug)]
pub enum UserDomainEvent {
    // channel - CHANNEL_MESSAGE
    MessageTokenMismatchDevice,
    MessageTokenInvalid,
    MessageLogout,
    MessageCancelLogin,
    MessageRefreshTokenSuccess(AuthData),
    MessageRefreshTokenFailed,
    MessageRefreshComplete,
    MessageRefreshFailure,
    MessageUserInfoRefreshSuccess(UserInfo),
    MessageUserInfoRefreshFailed,
    MessageDeviceListRefreshSuccess(Vec<Device>),
    MessageDeviceListRefreshFailed,
    MessageFamilyListRefreshSuc<PERSON>(Vec<Family>),
    MessageFamilyListRefreshFailed,
    Message<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Family),
    MessageAddressListRefreshSuccess,
    MessageRefreshDeviceListCache,
    MessageCurrentFamilyRoomListChange(String), // 当前家庭房间顺序变化事件，参数为家庭ID
    // channel - CHANNEL_OPERATE
    OperateRefreshDeviceList,
    OperateRefreshFamilyList,
    OperateRefreshUser,
    OperatePlanedAuthData,
    OperateRefreshAllFamilyDetail,
    OperateRefreshAddressList,
}

impl UserDomainEvent {
    pub fn value(&self) -> &str {
        match self {
            UserDomainEvent::MessageTokenMismatchDevice => "MessageTokenMismatchDevice",
            UserDomainEvent::MessageTokenInvalid => "MessageTokenInvalid",
            UserDomainEvent::MessageLogout => "MessageLogout",
            UserDomainEvent::MessageCancelLogin => "MessageCancelLogin",
            UserDomainEvent::MessageRefreshTokenSuccess(_) => "MessageRefreshTokenSuccess",
            UserDomainEvent::MessageRefreshTokenFailed => "MessageRefreshTokenFailed",
            UserDomainEvent::MessageRefreshComplete => "MessageRefreshComplete",
            UserDomainEvent::MessageRefreshFailure => "MessageRefreshFailure",
            UserDomainEvent::MessageUserInfoRefreshSuccess(_) => "MessageUserInfoRefreshSuccess",
            UserDomainEvent::MessageUserInfoRefreshFailed => "MessageUserInfoRefreshFailed",
            UserDomainEvent::MessageDeviceListRefreshSuccess(_) => {
                "MessageDeviceListRefreshSuccess"
            }
            UserDomainEvent::MessageDeviceListRefreshFailed => "MessageDeviceListRefreshFailed",
            UserDomainEvent::MessageFamilyListRefreshSuccess(_) => {
                "MessageFamilyListRefreshSuccess"
            }
            UserDomainEvent::MessageFamilyListRefreshFailed => "MessageFamilyListRefreshFailed",
            UserDomainEvent::MessageCurrentFamilyChanged(_) => "MessageCurrentFamilyChanged",
            UserDomainEvent::MessageAddressListRefreshSuccess => "MessageAddressListRefreshSuccess",
            UserDomainEvent::MessageRefreshDeviceListCache => "MessageRefreshDeviceListCache",
            UserDomainEvent::MessageCurrentFamilyRoomListChange(_) => {
                "MessageCurrentFamilyRoomListChange"
            }
            UserDomainEvent::OperateRefreshDeviceList => "OperateRefreshDeviceList",
            UserDomainEvent::OperateRefreshFamilyList => "OperateRefreshFamilyList",
            UserDomainEvent::OperateRefreshUser => "OperateRefreshUser",
            UserDomainEvent::OperatePlanedAuthData => "OperatePlanedAuthData",
            UserDomainEvent::OperateRefreshAllFamilyDetail => "OperateRefreshAllFamilyDetail",
            UserDomainEvent::OperateRefreshAddressList => "OperateRefreshAddressList",
        }
    }
}
