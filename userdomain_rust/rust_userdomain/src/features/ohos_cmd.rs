use log::{debug, warn};
use napi_ohos::bindgen_prelude::ToNapiValue;
use napi_ohos::{Env, Error, JsObject, Result, Status};
use request_rust::setting::request_setting::AppNetworkEnv;
use request_rust::setting::request_setting::RequestConfigManager;
use serde::Serialize;

use crate::api::user_domain::UserDomainPlatform;
use crate::api::user_domain_manager::UserDomainManager;
use crate::data_source::device_data_source::DeviceDataSourceImpl;
use crate::data_source::family_data_source::FamilyDataSourceImpl;
use crate::data_source::user_data_source::UserDataSourceImpl;
use crate::features::constant::result::{
    RESULT_BOOLEAN_VALUE, RESULT_NUMBER_VALUE, RESULT_STRING_VALUE,
};
use crate::features::ohos_cert::{get_ohos_cert_paths, load_cert_chain};
use crate::models::auth_data::AuthData;

pub fn cmd_userdomain_init(env: Env, args: JsObject) -> Result<JsObject> {
    debug!("init user domain");
    let app_id: String = args
        .get("app_id")?
        .ok_or(Error::new(Status::InvalidArg, "need app_id"))?;
    let app_key: String = args
        .get("app_key")?
        .ok_or(Error::new(Status::InvalidArg, "need app_key"))?;
    let app_version: String = args
        .get("app_version")?
        .ok_or(Error::new(Status::InvalidArg, "need app_version"))?;
    let client_id: String = args
        .get("client_id")?
        .ok_or(Error::new(Status::InvalidArg, "need client_id"))?;
    let network_env_int: i32 = args
        .get("network_env")?
        .ok_or(Error::new(Status::InvalidArg, "need network_env"))?;
    let gray_mode: bool = args
        .get("gray_mode")?
        .ok_or(Error::new(Status::InvalidArg, "need gray_mode"))?;

    let network_env = int_to_app_network_env(network_env_int);
    debug!("init network_env {:?}", network_env);

    let paths = get_ohos_cert_paths();
    let cert_chain = load_cert_chain(paths).unwrap_or_else(|e| {
        warn!("ohos load_cert_chain faild, network is unavailable. {}", e);
        vec![]
    });

    RequestConfigManager::get_instance().write_settings(|setting| {
        setting.set_app_id(app_id);
        setting.set_app_key(app_key);
        setting.set_app_version(app_version);
        setting.set_client_id(client_id.clone());
        setting.set_root_certificates(cert_chain);
        setting.set_network_env(network_env);
    });

    UserDomainManager::get_instance()
        .get_setting()
        .set_gray_mode(gray_mode);
    UserDomainManager::get_instance()
        .get_setting()
        .set_user_domain_platform(UserDomainPlatform::Harmony);
    UserDomainManager::get_instance().init_user_domain(
        Box::new(UserDataSourceImpl::new()),
        Box::new(FamilyDataSourceImpl::new()),
        Box::new(DeviceDataSourceImpl::new()),
    );

    Ok(env.create_object()?)
}

pub fn cmd_update_oauth_data(env: Env, args: JsObject) -> Result<JsObject> {
    let access_token: String = args
        .get("access_token")?
        .ok_or(Error::new(Status::InvalidArg, "need access_token"))?;
    let expires_in: i64 = args
        .get("expires_in")?
        .ok_or(Error::new(Status::InvalidArg, "need expires_in"))?;
    let refresh_token: String = args
        .get("refresh_token")?
        .ok_or(Error::new(Status::InvalidArg, "need refresh_token"))?;
    let scope: String = args
        .get("scope")?
        .ok_or(Error::new(Status::InvalidArg, "need scope"))?;
    let token_type: String = args
        .get("token_type")?
        .ok_or(Error::new(Status::InvalidArg, "need token_type"))?;
    let uhome_access_token: String = args
        .get("uhome_access_token")?
        .ok_or(Error::new(Status::InvalidArg, "need uhome_access_token"))?;
    let uhome_user_id: String = args
        .get("uhome_user_id")?
        .ok_or(Error::new(Status::InvalidArg, "need uhome_user_id"))?;
    let uc_user_id: String = args
        .get("uc_user_id")?
        .ok_or(Error::new(Status::InvalidArg, "need uc_user_id"))?;
    let auth_data = AuthData::new(
        access_token,
        expires_in as i128,
        refresh_token,
        scope,
        token_type,
        uhome_access_token,
        uhome_user_id,
        0,
        uc_user_id,
    );
    let result = UserDomainManager::get_instance()
        .get_user_domain()
        .update_oauth_data(auth_data);
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, result)
}

pub fn cmd_get_oauth_data(env: Env) -> Result<JsObject> {
    let oauth_data = UserDomainManager::get_instance()
        .get_user_domain()
        .get_oauth_data();
    convert_result_to_js_object(env, &oauth_data)
}

pub fn cmd_remove_observer(env: Env, args: JsObject) -> Result<JsObject> {
    let observer_id: String = args
        .get("observer_id")?
        .ok_or(Error::new(Status::InvalidArg, "need observer_id"))?;
    UserDomainManager::get_instance()
        .get_user_domain()
        .remove_observer(&observer_id);
    Ok(env.create_object()?)
}

pub fn cmd_cancel_login(env: Env) -> Result<JsObject> {
    UserDomainManager::get_instance()
        .get_user_domain()
        .cancel_login();
    Ok(env.create_object()?)
}

pub fn cmd_is_refresh_completed(env: Env) -> Result<JsObject> {
    let completed = UserDomainManager::get_instance()
        .get_user_domain()
        .is_refresh_completed();
    debug!("cmd_is_refresh_completed: {}", completed);
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, completed)
}

pub fn cmd_is_refresh_device_list_completed(env: Env) -> Result<JsObject> {
    let completed = UserDomainManager::get_instance()
        .get_user_domain()
        .is_refresh_completed();
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, completed)
}

pub fn cmd_is_refresh_family_list_completed(env: Env) -> Result<JsObject> {
    let completed = UserDomainManager::get_instance()
        .get_user_domain()
        .is_refresh_family_list_completed();
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, completed)
}

pub fn cmd_is_refresh_user_completed(env: Env) -> Result<JsObject> {
    let completed = UserDomainManager::get_instance()
        .get_user_domain()
        .is_refresh_user_completed();
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, completed)
}

pub fn cmd_state(env: Env) -> Result<JsObject> {
    let state = UserDomainManager::get_instance()
        .get_user_domain()
        .state()
        .value();
    convert_common_single_key_result(env, RESULT_NUMBER_VALUE, state)
}

pub fn cmd_get_http_request_retry_delay(env: Env) -> Result<JsObject> {
    let delay = UserDomainManager::get_instance()
        .get_setting()
        .get_http_request_retry_delay();
    convert_common_single_key_result(env, RESULT_NUMBER_VALUE, delay)
}
pub fn cmd_set_http_request_retry_delay(env: Env, args: JsObject) -> Result<JsObject> {
    let delay: i32 = args.get("http_request_retry_delay")?.ok_or(Error::new(
        Status::InvalidArg,
        "need http_request_retry_delay",
    ))?;
    UserDomainManager::get_instance()
        .get_setting()
        .set_http_request_retry_delay(delay);
    Ok(env.create_object()?)
}

pub fn cmd_set_user_domain_platform(env: Env, args: JsObject) -> Result<JsObject> {
    let user_domain_platform: i32 = args
        .get("user_domain_platform")?
        .ok_or(Error::new(Status::InvalidArg, "need user_domain_platform"))?;
    let platform = match user_domain_platform {
        0 => UserDomainPlatform::Homeland,
        1 => UserDomainPlatform::SouthEastAsia,
        2 => UserDomainPlatform::HomelandSYN,
        3 => UserDomainPlatform::HomelandShop,
        4 => UserDomainPlatform::Harmony,
        5 => UserDomainPlatform::HomelandLite,
        _ => UserDomainPlatform::Unknow,
    };
    UserDomainManager::get_instance()
        .get_setting()
        .set_user_domain_platform(platform);
    Ok(env.create_object()?)
}

pub fn cmd_get_user_domain_platform(env: Env) -> Result<JsObject> {
    let platform = UserDomainManager::get_instance()
        .get_setting()
        .get_user_domain_platform();
    let platform_code = platform as i32;
    convert_common_single_key_result(env, RESULT_NUMBER_VALUE, platform_code)
}

pub fn cmd_set_refresh_family_list_enable(env: Env, args: JsObject) -> Result<JsObject> {
    let enable: bool = args.get("refresh_family_list_enable")?.ok_or(Error::new(
        Status::InvalidArg,
        "need refresh_family_list_enable",
    ))?;
    UserDomainManager::get_instance()
        .get_setting()
        .set_refresh_family_list_enable(enable);
    Ok(env.create_object()?)
}

pub fn cmd_is_refresh_family_list_enable(env: Env) -> Result<JsObject> {
    let enable = UserDomainManager::get_instance()
        .get_setting()
        .is_refresh_family_list_enable();
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, enable)
}

pub fn cmd_set_refresh_device_list_enable(env: Env, args: JsObject) -> Result<JsObject> {
    let enable: bool = args.get("refresh_device_list_enable")?.ok_or(Error::new(
        Status::InvalidArg,
        "need refresh_device_list_enable",
    ))?;
    UserDomainManager::get_instance()
        .get_setting()
        .set_refresh_device_list_enable(enable);
    Ok(env.create_object()?)
}

pub fn cmd_is_refresh_device_list_enable(env: Env) -> Result<JsObject> {
    let enable = UserDomainManager::get_instance()
        .get_setting()
        .is_refresh_device_list_enable();
    convert_common_single_key_result(env, RESULT_BOOLEAN_VALUE, enable)
}

pub fn cmd_get_plan_refresh_token_time_millis(env: Env) -> Result<JsObject> {
    let time = UserDomainManager::get_instance()
        .get_setting()
        .plan_refresh_token_time_millis();
    convert_common_single_key_result(env, RESULT_NUMBER_VALUE, time as u32)
}

pub fn cmd_set_plan_refresh_token_time_millis(env: Env, args: JsObject) -> Result<JsObject> {
    let time: u32 = args
        .get("plan_refresh_token_time_millis")?
        .ok_or(Error::new(
            Status::InvalidArg,
            "need plan_refresh_token_time_millis",
        ))?;
    UserDomainManager::get_instance()
        .get_setting()
        .set_plan_refresh_token_time_millis(time as u128);
    Ok(env.create_object()?)
}

pub fn cmd_get_user_info(env: Env) -> Result<JsObject> {
    let user_info = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info();
    convert_result_to_js_object(env, &user_info)
}

pub fn cmd_get_iot_user_id(env: Env) -> Result<JsObject> {
    let user_id = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_iot_user_id();
    convert_common_single_key_result(env, RESULT_STRING_VALUE, user_id)
}

pub fn cmd_get_uc_user_id(env: Env) -> Result<JsObject> {
    let user_id = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_uc_user_id();
    convert_common_single_key_result(env, RESULT_STRING_VALUE, user_id)
}

pub fn cmd_get_current_family(env: Env) -> Result<JsObject> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_current_family()
    {
        None => Err(Error::new(Status::InvalidArg, "family not found")),
        Some(family) => convert_result_to_js_object(env, &family),
    }
}

pub fn cmd_get_devices_map_by_user(env: Env) -> Result<JsObject> {
    let map = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_devices_map();
    convert_result_to_js_object(env, &map)
}

pub fn cmd_get_familys_map_by_user(env: Env) -> Result<JsObject> {
    let map = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_familys_map();
    convert_result_to_js_object(env, &map)
}

pub fn cmd_get_device_by_id(env: Env, args: JsObject) -> Result<JsObject> {
    let device_id: String = args
        .get("device_id")?
        .ok_or(Error::new(Status::InvalidArg, "need device_id"))?;
    let device = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_device_by_id(&device_id);
    match device {
        Some(device) => convert_result_to_js_object(env, &device),
        None => Err(Error::new(Status::InvalidArg, "device not found")),
    }
}

pub fn cmd_get_family_by_id(env: Env, args: JsObject) -> Result<JsObject> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    if let Some(family) = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(&family_id)
    {
        convert_result_to_js_object(env, &family)
    } else {
        Err(Error::new(Status::InvalidArg, "family not found"))
    }
}

pub fn cmd_get_devices_map_by_family_id(env: Env, args: JsObject) -> Result<JsObject> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    if let Some(family) = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(&family_id)
    {
        let devices_map = family.get_devices_map();
        convert_result_to_js_object(env, &devices_map)
    } else {
        Err(Error::new(Status::InvalidArg, "devices map not found"))
    }
}

pub fn cmd_get_device_list_by_family_id(env: Env, args: JsObject) -> Result<JsObject> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    if let Some(family) = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(&family_id)
    {
        let devices_list = family.get_device_list();
        convert_result_to_js_object(env, &devices_list)
    } else {
        Err(Error::new(Status::InvalidArg, "family not found"))
    }
}

/// 转化返回对象，生成JsObject返回arkTs端
fn convert_result_to_js_object<T>(env: Env, value: &T) -> Result<JsObject>
where
    T: ?Sized + Serialize,
{
    let json = serde_json::to_string(value)
        .map_err(|e| Error::new(Status::ObjectExpected, e.to_string()))?;
    let mut result = env.create_object()?;
    result.set(RESULT_STRING_VALUE, json)?;
    Ok(result)
}

pub fn convert_common_single_key_result<K: AsRef<str>, V: ToNapiValue>(
    env: Env,
    field: K,
    val: V,
) -> Result<JsObject> {
    let mut object = env.create_object()?;
    object.set(field, val)?;
    Ok(object)
}

pub fn cmd_get_default_address(env: Env) -> Result<JsObject> {
    let address = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info()
        .get_default_address();
    match address {
        Some(address) => convert_result_to_js_object(env, &address),
        None => convert_result_to_js_object(env, &{}),
    }
}

pub fn cmd_get_address_list(env: Env) -> Result<JsObject> {
    let address_list = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info()
        .get_address_list();
    convert_result_to_js_object(env, &address_list)
}

fn int_to_app_network_env(value: i32) -> AppNetworkEnv {
    match value {
        0 => AppNetworkEnv::Development,
        1 => AppNetworkEnv::Acceptance,
        2 => AppNetworkEnv::Production,
        _ => AppNetworkEnv::Production,
    }
}
