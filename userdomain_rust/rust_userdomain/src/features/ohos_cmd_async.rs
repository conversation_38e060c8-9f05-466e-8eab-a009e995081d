use log::{debug, error};
use napi_ohos::bindgen_prelude::Function;
use napi_ohos::threadsafe_function::ThreadsafeFunctionCallMode;
use napi_ohos::{Env, Error, JsBoolean, JsObject, JsString, Result, Status};
use task_manager::common_runtime::get_runtime;

use crate::api::error::UserDomainError;
use crate::api::event::UserDomainEvent;
use crate::api::event::UserDomainEvent::*;
use crate::api::family::Family;
use crate::api::user_domain_manager::UserDomainManager;
use crate::features::constant::result::RESULT_STRING_VALUE;
use crate::features::ohos_cmd::convert_common_single_key_result;
use crate::models::address_args::AddressArgs;
use crate::models::device_info::DeviceInfo;
use crate::models::family_args::CreateFamilyRoomArgs;
use crate::models::family_args::FamilyArgs;
use crate::models::floor_args::FloorArgs;
use crate::models::room::Room;
use crate::models::room_args::RoomArgs;
use crate::models::user_args::UserArgs;
use crate::models::virtual_member_args::VirtualMemberArgs;
use request_rust::request::error::RequestError;

const SUCCESS_CODE: &str = "000000";
const UNLOGING_CODE: &str = "110001";
const PARAMS_ERROR: &str = "900003";

pub fn cmd_add_observer(
    env: Env,
    _: JsObject,
    function: Function<(i32, String)>,
) -> Result<JsObject> {
    let tsfn = function.build_threadsafe_function().build()?;
    let observer = move |event: UserDomainEvent| {
        let result = event_to_result(&event);
        if result.0 > 0 {
            tsfn.call((result.0, result.1), ThreadsafeFunctionCallMode::Blocking);
        }
    };
    let observer_id = UserDomainManager::get_instance()
        .get_user_domain()
        .add_observer(observer);
    convert_common_single_key_result(env, RESULT_STRING_VALUE, observer_id)
}

fn event_to_result(event: &UserDomainEvent) -> (i32, String) {
    let event_num = match event {
        MessageTokenMismatchDevice => 1,
        MessageTokenInvalid => 2,
        MessageLogout => 3,
        MessageCancelLogin => 4,
        MessageRefreshTokenFailed => 6,
        MessageRefreshComplete => 7,
        MessageRefreshFailure => 8,
        MessageRefreshTokenSuccess(_) => 9,
        MessageUserInfoRefreshSuccess(_) => 10,
        MessageUserInfoRefreshFailed => 11,
        MessageDeviceListRefreshSuccess(_) => 12,
        MessageDeviceListRefreshFailed => 13,
        MessageFamilyListRefreshSuccess(_) => 14,
        MessageFamilyListRefreshFailed => 15,
        MessageCurrentFamilyChanged(_) => 16,
        _ => -1,
    };

    let result_str = match event {
        MessageRefreshTokenSuccess(auth_data) => {
            serde_json::to_string(auth_data).unwrap_or(String::new())
        }
        MessageUserInfoRefreshSuccess(user_info) => {
            serde_json::to_string(user_info).unwrap_or(String::new())
        }
        MessageDeviceListRefreshSuccess(devices) => {
            serde_json::to_string(devices).unwrap_or(String::new())
        }
        _ => String::new(),
    };
    (event_num, result_str)
}

pub fn cmd_set_current_family(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .set_current_family(family_id)
            .await
        {
            Ok(_) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_auto_refresh_token(_: JsObject, _: Function<(String, String)>) -> Result<()> {
    get_runtime().spawn(async move {
        UserDomainManager::get_instance()
            .get_user_domain()
            .auto_refresh_token()
            .await;
    });
    Ok(())
}
pub fn cmd_schedule_refresh_token(_: JsObject, _: Function<(String, String)>) -> Result<()> {
    let auth_data = UserDomainManager::get_instance()
        .get_user_domain()
        .get_oauth_data();
    get_runtime().spawn(async move {
        let _ = UserDomainManager::get_instance()
            .get_user_domain()
            .schedule_refresh_token(&auth_data)
            .await;
    });
    Ok(())
}

pub fn cmd_query_user_info(_: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .refresh_user_info()
            .await
        {
            Ok(user_info) => {
                let json_str = match serde_json::to_string(&user_info) {
                    Ok(json) => json,
                    Err(e) => {
                        error!("Error serializing user info: {:?}", e);
                        return;
                    }
                };
                tsfn.call(
                    (SUCCESS_CODE.to_string(), json_str),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_refresh_user(_: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .refresh_user()
            .await
        {
            Ok(()) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_refresh_device_list(_: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        debug!("Rust cmd_refresh_device_list");
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .refresh_device_list()
            .await
        {
            Ok(device_list) => {
                let json_str = match serde_json::to_string(&device_list) {
                    Ok(json) => json,
                    Err(e) => {
                        tsfn.call(
                            (
                                PARAMS_ERROR.to_string(),
                                "Error serializing user info".to_string(),
                            ),
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                        return;
                    }
                };
                tsfn.call(
                    (SUCCESS_CODE.to_string(), json_str),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_refresh_family_list(_: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        debug!("Rust cmd_refresh_family_list");
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .refresh_family_list()
            .await
        {
            Ok(family_list) => {
                let json_str = match serde_json::to_string(&family_list) {
                    Ok(json) => json,
                    Err(e) => {
                        tsfn.call(
                            (
                                PARAMS_ERROR.to_string(),
                                "Error serializing family list".to_string(),
                            ),
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                        return;
                    }
                };
                tsfn.call(
                    (SUCCESS_CODE.to_string(), json_str),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_refresh_address_list(_: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        debug!("Rust cmd_refresh_address_list");
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .refresh_address_list()
            .await
        {
            Ok(device_list) => {
                let json_str = match serde_json::to_string(&device_list) {
                    Ok(json) => json,
                    Err(e) => {
                        error!("Error serializing user info: {:?}", e);
                        return;
                    }
                };
                tsfn.call(
                    (SUCCESS_CODE.to_string(), json_str),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_modify_user_info(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let user_id: String = args
        .get("userId")?
        .ok_or(Error::new(Status::InvalidArg, "need userId"))?;
    let given_name: String = args
        .get("givenName")?
        .ok_or(Error::new(Status::InvalidArg, "need givenName"))?;
    let nickname: String = args
        .get("nickname")?
        .ok_or(Error::new(Status::InvalidArg, "need nickname"))?;
    let family_num: String = args
        .get("familyNum")?
        .ok_or(Error::new(Status::InvalidArg, "need familyNum"))?;
    let gender: String = args
        .get("gender")?
        .ok_or(Error::new(Status::InvalidArg, "gender"))?;
    let marriage: String = args
        .get("marriage")?
        .ok_or(Error::new(Status::InvalidArg, "need marriage"))?;
    let education: String = args
        .get("education")?
        .ok_or(Error::new(Status::InvalidArg, "need education"))?;
    let birthday: String = args
        .get("birthday")?
        .ok_or(Error::new(Status::InvalidArg, "need birthday"))?;
    let avatar_url: String = args
        .get("avatarUrl")?
        .ok_or(Error::new(Status::InvalidArg, "need avatar_url"))?;
    let income: String = args
        .get("income")?
        .ok_or(Error::new(Status::InvalidArg, "need income"))?;
    let extra_phone: String = args
        .get("extraPhone")?
        .ok_or(Error::new(Status::InvalidArg, "need extraPhone"))?;
    let height: String = args
        .get("height")?
        .ok_or(Error::new(Status::InvalidArg, "need height"))?;
    let weight: String = args
        .get("weight")?
        .ok_or(Error::new(Status::InvalidArg, "need weight"))?;

    let user_args = UserArgs {
        user_id,
        given_name,
        nickname,
        family_num,
        gender,
        marriage,
        birthday,
        education,
        avatar_url,
        income,
        extra_phone,
        height,
        weight,
        privacy_country_code: None,
    };

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let result = UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .modify_user_info(user_args)
            .await;
        match result {
            Ok(()) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_logout(_: JsObject, function: Function<(String, String)>) -> Result<()> {
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .logout()
            .await
        {
            Ok(_) => {
                debug!("Rust cmd_logout success");
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_confirm_device_sharing_relation(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let share_uuid: String = args
        .get("share_uuid")?
        .ok_or(Error::new(Status::InvalidArg, "need share_uuid"))?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .confirm_device_sharing_relation(share_uuid)
            .await
        {
            Ok(_) => {
                debug!("Rust cmd_confirm_device_sharing_relation success");
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_cancel_device_sharing_relation(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let share_uuids_str: String = args
        .get("share_uuids")?
        .ok_or(Error::new(Status::InvalidArg, "need share_uuids"))?;

    let share_uuids: Vec<String> = match serde_json::from_str(&share_uuids_str) {
        Ok(list) => list,
        Err(_) => {
            return Err(Error::new(
                Status::InvalidArg,
                "share_uuids must be a valid JSON array of strings",
            ));
        }
    };

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .cancel_device_sharing_relation(share_uuids)
            .await
        {
            Ok(_) => {
                debug!("Rust cmd_cancel_device_sharing_relation success");
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_create_address(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let city: String = args
        .get("city")?
        .ok_or(Error::new(Status::InvalidArg, "need city"))?;
    let city_id: String = args
        .get("cityId")?
        .ok_or(Error::new(Status::InvalidArg, "need cityId"))?;
    let country_code: String = args
        .get("countryCode")?
        .ok_or(Error::new(Status::InvalidArg, "need countryCode"))?;
    let district: String = args
        .get("district")?
        .ok_or(Error::new(Status::InvalidArg, "need district"))?;
    let district_id: String = args
        .get("districtId")?
        .ok_or(Error::new(Status::InvalidArg, "need districtId"))?;
    let line1: String = args
        .get("line1")?
        .ok_or(Error::new(Status::InvalidArg, "need line1"))?;
    let line2: String = args
        .get("line2")?
        .ok_or(Error::new(Status::InvalidArg, "need line2"))?;
    let postcode: String = args
        .get("postcode")?
        .ok_or(Error::new(Status::InvalidArg, "need postcode"))?;
    let province: String = args
        .get("province")?
        .ok_or(Error::new(Status::InvalidArg, "need province"))?;
    let province_id: String = args
        .get("provinceId")?
        .ok_or(Error::new(Status::InvalidArg, "need provinceId"))?;
    let town: String = args
        .get("town")?
        .ok_or(Error::new(Status::InvalidArg, "need town"))?;
    let town_id: String = args
        .get("townId")?
        .ok_or(Error::new(Status::InvalidArg, "need townId"))?;
    let email: String = args
        .get("email")?
        .ok_or(Error::new(Status::InvalidArg, "need email"))?;
    let address_id: String = args
        .get("addressId")?
        .ok_or(Error::new(Status::InvalidArg, "need addressId"))?;
    let is_default_property = args.get_named_property::<JsBoolean>("isDefault")?;
    let is_default: bool = is_default_property.get_value()?;
    let is_service_property = args.get_named_property::<JsBoolean>("isService")?;
    let is_service: bool = is_service_property.get_value()?;
    let receiver_mobile: String = args
        .get("receiverMobile")?
        .ok_or(Error::new(Status::InvalidArg, "need receiverMobile"))?;
    let receiver_name: String = args
        .get("receiverName")?
        .ok_or(Error::new(Status::InvalidArg, "need receiverName"))?;
    let source: String = args
        .get("source")?
        .ok_or(Error::new(Status::InvalidArg, "need source"))?;
    let tag: String = args
        .get("tag")?
        .ok_or(Error::new(Status::InvalidArg, "need tag"))?;
    let user_id: String = args
        .get("userId")?
        .ok_or(Error::new(Status::InvalidArg, "need userId"))?;
    let address_args = AddressArgs {
        city,
        city_id,
        country_code,
        district,
        district_id,
        line1,
        line2,
        postcode,
        province,
        province_id,
        town,
        town_id,
        email,
        address_id,
        is_default,
        is_service,
        receiver_mobile,
        receiver_name,
        source,
        tag,
        user_id,
    };
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .create_address(address_args)
            .await
        {
            Ok(_) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_edit_address(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let city: String = args
        .get("city")?
        .ok_or(Error::new(Status::InvalidArg, "need city"))?;
    let city_id: String = args
        .get("cityId")?
        .ok_or(Error::new(Status::InvalidArg, "need cityId"))?;
    let country_code: String = args
        .get("countryCode")?
        .ok_or(Error::new(Status::InvalidArg, "need countryCode"))?;
    let district: String = args
        .get("district")?
        .ok_or(Error::new(Status::InvalidArg, "need district"))?;
    let district_id: String = args
        .get("districtId")?
        .ok_or(Error::new(Status::InvalidArg, "need districtId"))?;
    let line1: String = args
        .get("line1")?
        .ok_or(Error::new(Status::InvalidArg, "need line1"))?;
    let line2: String = args
        .get("line2")?
        .ok_or(Error::new(Status::InvalidArg, "need line2"))?;
    let postcode: String = args
        .get("postcode")?
        .ok_or(Error::new(Status::InvalidArg, "need postcode"))?;
    let province: String = args
        .get("province")?
        .ok_or(Error::new(Status::InvalidArg, "need province"))?;
    let province_id: String = args
        .get("provinceId")?
        .ok_or(Error::new(Status::InvalidArg, "need provinceId"))?;
    let town: String = args
        .get("town")?
        .ok_or(Error::new(Status::InvalidArg, "need town"))?;
    let town_id: String = args
        .get("townId")?
        .ok_or(Error::new(Status::InvalidArg, "need townId"))?;
    let email: String = args
        .get("email")?
        .ok_or(Error::new(Status::InvalidArg, "need email"))?;
    let address_id: String = args
        .get("addressId")?
        .ok_or(Error::new(Status::InvalidArg, "need addressId"))?;
    let is_default_property = args.get_named_property::<JsBoolean>("isDefault")?;
    let is_default: bool = is_default_property.get_value()?;
    let is_service_property = args.get_named_property::<JsBoolean>("isService")?;
    let is_service: bool = is_service_property.get_value()?;
    let receiver_mobile: String = args
        .get("receiverMobile")?
        .ok_or(Error::new(Status::InvalidArg, "need receiverMobile"))?;
    let receiver_name: String = args
        .get("receiverName")?
        .ok_or(Error::new(Status::InvalidArg, "need receiverName"))?;
    let source: String = args
        .get("source")?
        .ok_or(Error::new(Status::InvalidArg, "need source"))?;
    let tag: String = args
        .get("tag")?
        .ok_or(Error::new(Status::InvalidArg, "need tag"))?;
    let user_id: String = args
        .get("userId")?
        .ok_or(Error::new(Status::InvalidArg, "need userId"))?;
    let address_args = AddressArgs {
        city,
        city_id,
        country_code,
        district,
        district_id,
        line1,
        line2,
        postcode,
        province,
        province_id,
        town,
        town_id,
        email,
        address_id,
        is_default,
        is_service,
        receiver_mobile,
        receiver_name,
        source,
        tag,
        user_id,
    };
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .edit_address(address_args)
            .await
        {
            Ok(_) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_delete_address(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let address_id: String = args
        .get("addressId")?
        .ok_or(Error::new(Status::InvalidArg, "need addressId"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .delete_address(address_id)
            .await
        {
            Ok(_) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_update_device_name(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let device_name: String = args
        .get("deviceName")?
        .ok_or(Error::new(Status::InvalidArg, "need deviceName"))?;
    let device_id: String = args
        .get("deviceId")?
        .ok_or(Error::new(Status::InvalidArg, "need deviceId"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_device_by_id(&device_id)
        {
            Some(device) => match device.update_device_name(device_name).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "device not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_update_device_name_and_check(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let device_name: String = args
        .get("deviceName")?
        .ok_or(Error::new(Status::InvalidArg, "need deviceName"))?;
    let device_id: String = args
        .get("deviceId")?
        .ok_or(Error::new(Status::InvalidArg, "need deviceId"))?;
    let bind_type: String = args
        .get("bindType")?
        .ok_or(Error::new(Status::InvalidArg, "need bindType"))?;
    let check_level_property = args.get_named_property::<JsBoolean>("checkLevel")?;
    let check_level: bool = check_level_property.get_value()?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_device_by_id(&device_id)
        {
            Some(device) => match device
                .update_device_name_and_check(device_name, bind_type, check_level)
                .await
            {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "device not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_get_group_device_list(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.get_group_device_list().await;
            debug!("get_group_device_list result: {:?}", result);
            match result {
                Ok(device_list) => {
                    let json_str = match serde_json::to_string(&device_list) {
                        Ok(json) => json,
                        Err(e) => {
                            tsfn.call(
                                (
                                    PARAMS_ERROR.to_string(),
                                    "Error serializing device list".to_string(),
                                ),
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                            return;
                        }
                    };
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), json_str),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        } else {
            tsfn.call(
                (PARAMS_ERROR.to_string(), "family not found".to_string()),
                ThreadsafeFunctionCallMode::Blocking,
            );
        }
    });
    Ok(())
}

pub fn cmd_query_family_info(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.query_family_info().await;
            match result {
                Ok(family_info) => {
                    let json_str = match serde_json::to_string(&family_info) {
                        Ok(json) => json,
                        Err(e) => {
                            tsfn.call(
                                (
                                    PARAMS_ERROR.to_string(),
                                    "Error serializing family info".to_string(),
                                ),
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                            return;
                        }
                    };
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), json_str),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        } else {
            tsfn.call(
                (PARAMS_ERROR.to_string(), "family not found".to_string()),
                ThreadsafeFunctionCallMode::Blocking,
            );
        }
    });
    Ok(())
}

pub fn cmd_query_room_list(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let floor_id: String = args
        .get("floor_id")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.query_room_list(floor_id).await;
            match result {
                Ok(room_list) => {
                    let json_str = match serde_json::to_string(&room_list) {
                        Ok(json) => json,
                        Err(e) => {
                            tsfn.call(
                                (
                                    PARAMS_ERROR.to_string(),
                                    "Error serializing room list".to_string(),
                                ),
                                ThreadsafeFunctionCallMode::Blocking,
                            );
                            return;
                        }
                    };
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), json_str),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        } else {
            tsfn.call(
                (PARAMS_ERROR.to_string(), "family not found".to_string()),
                ThreadsafeFunctionCallMode::Blocking,
            );
        }
    });
    Ok(())
}

pub fn cmd_create_floor(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let floor_order_id: String = args
        .get("floor_order_id")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_order_id"))?;
    let floor_label: String = args
        .get("floor_label")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_label"))?;
    let floor_logo: String = args
        .get("floor_logo")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_logo"))?;
    let floor_picture: String = args
        .get("floor_picture")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_picture"))?;
    let floor_id: String = args
        .get("floor_id")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_id"))?;
    let floor_class: String = args
        .get("floor_class")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_class"))?;
    let floor_name: String = args
        .get("floor_name")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_name"))?;

    let args = FloorArgs {
        floor_name,
        floor_order_id,
        floor_label,
        floor_logo,
        floor_picture,
        floor_id,
        floor_class,
    };
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.create_floor(args).await;
            match result {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        } else {
            tsfn.call(
                (PARAMS_ERROR.to_string(), "family not found".to_string()),
                ThreadsafeFunctionCallMode::Blocking,
            );
        }
    });
    Ok(())
}

pub fn cmd_modify_virtual_member(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let member_id: String = args
        .get("member_id")?
        .ok_or(Error::new(Status::InvalidArg, "need member_id"))?;
    let member_name: String = args
        .get("member_name")?
        .ok_or(Error::new(Status::InvalidArg, "need member_name"))?;
    let avatar_url: String = args
        .get("avatar_url")?
        .ok_or(Error::new(Status::InvalidArg, "need avatar_url"))?;
    let birthday: String = args
        .get("birthday")?
        .ok_or(Error::new(Status::InvalidArg, "need birthday"))?;
    let is_creator: bool = args
        .get("is_creator")?
        .ok_or(Error::new(Status::InvalidArg, "need is_creator"))?;

    let virtual_member_args = VirtualMemberArgs {
        member_id,
        member_name,
        avatar_url,
        is_creator,
        birthday,
    };
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.modify_virtual_member(virtual_member_args).await;
            match result {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        }
    });
    Ok(())
}

pub fn cmd_modify_member_role(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let member_id: String = args
        .get("member_id")?
        .ok_or(Error::new(Status::InvalidArg, "need member_id"))?;
    let member_role: String = args
        .get("member_role")?
        .ok_or(Error::new(Status::InvalidArg, "need member_role"))?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.modify_member_role(member_id, member_role).await;
            match result {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        }
    });
    Ok(())
}

pub fn cmd_save_rooms_order(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let room_order_args_str: String = args
        .get("room_order_args")?
        .ok_or(Error::new(Status::InvalidArg, "need room_order_args"))?;

    // 解析JSON字符串为RoomOrderArgs数组
    let room_order_args: Vec<crate::models::family_args::RoomOrderArgs> =
        serde_json::from_str(&room_order_args_str).map_err(|e| {
            Error::new(
                Status::InvalidArg,
                format!("Invalid room_order_args JSON: {}", e),
            )
        })?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family.save_rooms_order(room_order_args).await;
            match result {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        }
    });
    Ok(())
}

pub fn cmd_modify_virtual_member_role(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let member_id: String = args
        .get("member_id")?
        .ok_or(Error::new(Status::InvalidArg, "need member_id"))?;
    let member_role: String = args
        .get("member_role")?
        .ok_or(Error::new(Status::InvalidArg, "need member_role"))?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        if let Some(family) = option_family {
            let result = family
                .modify_virtual_member_role(member_id, member_role)
                .await;
            match result {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            }
        }
    });
    Ok(())
}

pub fn cmd_delete_floor(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let floor_id: String = args
        .get("floor_id")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_id"))?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.delete_floor(floor_id).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_modify_family_info(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let name: String = args
        .get("family_name")?
        .ok_or(Error::new(Status::InvalidArg, "need family_name"))?;
    let position: String = args
        .get("position")?
        .ok_or(Error::new(Status::InvalidArg, "need position"))?;
    let city_code: String = args
        .get("city_code")?
        .ok_or(Error::new(Status::InvalidArg, "need city_code"))?;
    let latitude: String = args
        .get("latitude")?
        .ok_or(Error::new(Status::InvalidArg, "need latitude"))?;
    let longitude: String = args
        .get("longitude")?
        .ok_or(Error::new(Status::InvalidArg, "need longitude"))?;

    let family_args = FamilyArgs {
        name,
        position,
        latitude,
        longitude,
        city_code,
        room_names: vec![],
    };

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => {
                let result = family.modify_family_info(family_args).await;
                match result {
                    Ok(_) => {
                        tsfn.call(
                            (SUCCESS_CODE.to_string(), "".to_string()),
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Err(err) => {
                        let (error_code, error_message) = get_error_code_and_message(&err.into());
                        tsfn.call(
                            (error_code, error_message),
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                }
            }
        }
    });
    Ok(())
}

pub fn cmd_create_family(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let city_code: String = args
        .get("cityCode")?
        .ok_or(Error::new(Status::InvalidArg, "need familyName"))?;
    let latitude: String = args
        .get("latitude")?
        .ok_or(Error::new(Status::InvalidArg, "need latitude"))?;
    let longitude: String = args
        .get("longitude")?
        .ok_or(Error::new(Status::InvalidArg, "need longitude"))?;
    let family_name: String = args
        .get("name")?
        .ok_or(Error::new(Status::InvalidArg, "need familyName"))?;
    let position: String = args
        .get("position")?
        .ok_or(Error::new(Status::InvalidArg, "need position"))?;
    let room_names_prop: JsObject = args
        .get("roomNames")?
        .ok_or(Error::new(Status::InvalidArg, "need roomNames"))?;
    let room_names_keys = room_names_prop.get_property_names()?;
    let length = room_names_keys.get_array_length()?;
    let mut room_names = vec![];
    for i in 0..length {
        let key = room_names_keys.get_element::<JsString>(i)?;
        let utf8_value = JsString::into_utf8(key)?;
        let rust_str = utf8_value.as_str()?;
        let obj = room_names_prop.get_named_property::<JsObject>(rust_str)?;
        let room_class: String = obj
            .get("roomClass")?
            .ok_or(Error::new(Status::InvalidArg, "need roomClass"))?;
        let room_name: String = obj
            .get("roomName")?
            .ok_or(Error::new(Status::InvalidArg, "need roomName"))?;
        let room = CreateFamilyRoomArgs {
            room_class,
            room_name,
        };
        room_names.push(room);
    }
    let family_args = FamilyArgs::new(
        city_code,
        latitude,
        longitude,
        family_name,
        position,
        room_names,
    );
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .create_family(family_args)
            .await
        {
            Ok(family_id) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), family_id),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_add_room(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let image: String = args
        .get("image")?
        .ok_or(Error::new(Status::InvalidArg, "need image"))?;
    let label: String = args
        .get("label")?
        .ok_or(Error::new(Status::InvalidArg, "need label"))?;
    let logo: String = args
        .get("logo")?
        .ok_or(Error::new(Status::InvalidArg, "need logo"))?;
    let name: String = args
        .get("name")?
        .ok_or(Error::new(Status::InvalidArg, "need name"))?;
    let room_type: String = args
        .get("room_type")?
        .ok_or(Error::new(Status::InvalidArg, "need room_type"))?;
    let floor_id: String = args
        .get("floor_id")?
        .ok_or(Error::new(Status::InvalidArg, "need floor_id"))?;
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let room_args = RoomArgs {
        room_name: name,
        floor_order_id: "1".to_string(),
        room_class: "".to_string(),
    };
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            Some(family) => match family.add_room(room_args).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_remove_room(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let room_id: String = args
        .get("room_id")?
        .ok_or(Error::new(Status::InvalidArg, "need room_id"))?;
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            Some(family) => match family.remove_room(room_id).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}
fn fetch_family_by_id(family_id: &str) -> Option<Family> {
    UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id)
}

pub fn cmd_update_room_name(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let room_id: String = args
        .get("room_id")?
        .ok_or(Error::new(Status::InvalidArg, "need room_id"))?;
    let room_name: String = args
        .get("room_name")?
        .ok_or(Error::new(Status::InvalidArg, "need room_name"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.update_room_name(room_id, room_name).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_exit_family_as_admin(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let user_id: String = args
        .get("user_id")?
        .ok_or(Error::new(Status::InvalidArg, "need user_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.exit_family_as_admin(user_id).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_unbind_devices(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let device_infos: String = args
        .get("device_infos")?
        .ok_or(Error::new(Status::InvalidArg, "need device_infos"))?;
    debug!("{}", device_infos);
    let device_list: Vec<DeviceInfo> = serde_json::from_str(&device_infos)
        .map_err(|e| Error::new(Status::InvalidArg, e.to_string()))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.unbind_devices(&device_list).await {
                Ok(device_operation_result) => {
                    let json =
                        serde_json::to_string(&device_operation_result).unwrap_or("".to_string());
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), json),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_change_family_admin(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let user_id: String = args
        .get("user_id")?
        .ok_or(Error::new(Status::InvalidArg, "need user_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.reassign_family_administrator(user_id).await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_exit_family_as_member(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.exit_family_as_member().await {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_query_first_member(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.query_first_member().await {
                Ok(family_member_info) => {
                    let json = serde_json::to_string(&family_member_info).unwrap_or("".to_string());
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), json),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_add_virtual_member(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let member_name: String = args
        .get("member_name")?
        .ok_or(Error::new(Status::InvalidArg, "need member_name"))?;
    let member_id: String = args
        .get("member_id")?
        .ok_or(Error::new(Status::InvalidArg, "need member_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        match UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_family_by_id(family_id.as_str())
        {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.add_virtual_member(member_name, member_id).await {
                Ok(()) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_delete_family_member(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let member_id: String = args
        .get("member_id")?
        .ok_or(Error::new(Status::InvalidArg, "need member_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.delete_family_member(member_id).await {
                Ok(()) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_reply_family_invite(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let invite_code: String = args
        .get("invite_code")?
        .ok_or(Error::new(Status::InvalidArg, "need invite_code"))?;
    let member_name: String = args
        .get("member_name")?
        .ok_or(Error::new(Status::InvalidArg, "need member_name"))?;
    let agree: bool = args
        .get("agree")?
        .ok_or(Error::new(Status::InvalidArg, "need agree"))?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let result = UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .reply_family_invite_member(invite_code, family_id, agree, member_name)
            .await;
        match result {
            Ok(()) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_admin_invite_member(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let user_id: String = args
        .get("user_id")?
        .ok_or(Error::new(Status::InvalidArg, "need user_id"))?;
    let nickname: String = args
        .get("nickname")?
        .ok_or(Error::new(Status::InvalidArg, "need nickname"))?;
    let member_role: String = args.get("member_role")?.unwrap_or("".to_string());
    let member_type: i32 = args
        .get("member_type")?
        .unwrap_or("2".to_string())
        .parse()
        .unwrap_or(2);

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family
                .admin_invite_member(user_id, nickname, member_role, member_type)
                .await
            {
                Ok(_) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_reply_join_family(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let application_id: String = args
        .get("application_id")?
        .ok_or(Error::new(Status::InvalidArg, "need application_id"))?;
    let agree: bool = args
        .get("agree")?
        .ok_or(Error::new(Status::InvalidArg, "need agree"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let result = UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .reply_join_family(application_id, agree)
            .await;
        match result {
            Ok(()) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), "".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}

pub fn cmd_delete_family_as_admin(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family.delete_family_as_admin().await {
                Ok(()) => {
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), "success".to_string()),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_move_devices_to_other_room(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let room_id: String = args
        .get("room_id")?
        .ok_or(Error::new(Status::InvalidArg, "need room_id"))?;
    let device_infos: String = args
        .get("device_infos")?
        .ok_or(Error::new(Status::InvalidArg, "need device_infos"))?;
    let device_list: Vec<DeviceInfo> = serde_json::from_str(&device_infos)
        .map_err(|e| Error::new(Status::InvalidArg, e.to_string()))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => {
                let room = get_room_by_id(&family, &room_id);
                match room {
                    Err(e) => {
                        tsfn.call(
                            (PARAMS_ERROR.to_string(), e.to_string()),
                            ThreadsafeFunctionCallMode::Blocking,
                        );
                    }
                    Ok(room) => {
                        match family.move_devices_to_other_room(&room, &device_list).await {
                            Ok(device_operation_result) => {
                                let json = serde_json::to_string(&device_operation_result)
                                    .unwrap_or("".to_string());
                                tsfn.call(
                                    (SUCCESS_CODE.to_string(), json),
                                    ThreadsafeFunctionCallMode::Blocking,
                                );
                            }
                            Err(err) => {
                                let (error_code, error_message) =
                                    get_error_code_and_message(&err.into());
                                tsfn.call(
                                    (error_code, error_message),
                                    ThreadsafeFunctionCallMode::Blocking,
                                );
                            }
                        }
                    }
                }
            }
        }
    });
    Ok(())
}

fn get_room_by_id(family: &Family, room_id: &str) -> Result<Room> {
    Ok(family
        .family_info
        .floor_infos
        .clone()
        .ok_or(Error::new(Status::InvalidArg, "floor not found"))?
        .iter()
        .flat_map(|floor| floor.floor_rooms.clone())
        .find(|room| room.room_id == room_id)
        .ok_or(Error::new(Status::InvalidArg, "room not found"))?)
}

pub fn cmd_move_devices_to_other_family(
    args: JsObject,
    function: Function<(String, String)>,
) -> Result<()> {
    let family_id: String = args
        .get("family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need family_id"))?;
    let target_family_id: String = args
        .get("target_family_id")?
        .ok_or(Error::new(Status::InvalidArg, "need target_family_id"))?;
    let device_infos: String = args
        .get("device_infos")?
        .ok_or(Error::new(Status::InvalidArg, "need device_infos"))?;
    let device_list: Vec<DeviceInfo> = serde_json::from_str(&device_infos)
        .map_err(|e| Error::new(Status::InvalidArg, e.to_string()))?;
    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let option_family = fetch_family_by_id(&family_id);
        match option_family {
            None => {
                tsfn.call(
                    (PARAMS_ERROR.to_string(), "family not found".to_string()),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Some(family) => match family
                .move_devices_to_other_family(device_list, target_family_id)
                .await
            {
                Ok(device_operation_result) => {
                    let json =
                        serde_json::to_string(&device_operation_result).unwrap_or("".to_string());
                    tsfn.call(
                        (SUCCESS_CODE.to_string(), json),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
                Err(err) => {
                    let (error_code, error_message) = get_error_code_and_message(&err.into());
                    tsfn.call(
                        (error_code, error_message),
                        ThreadsafeFunctionCallMode::Blocking,
                    );
                }
            },
        }
    });
    Ok(())
}

pub fn cmd_modify_user_avatar(args: JsObject, function: Function<(String, String)>) -> Result<()> {
    let image_path: String = args
        .get("image_path")?
        .ok_or(Error::new(Status::InvalidArg, "need avatar_path"))?;

    let tsfn = function.build_threadsafe_function().build()?;
    get_runtime().spawn(async move {
        let result = UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .modify_user_avatar(image_path)
            .await;
        match result {
            Ok(avatar_url) => {
                tsfn.call(
                    (SUCCESS_CODE.to_string(), avatar_url),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
            Err(err) => {
                let (error_code, error_message) = get_error_code_and_message(&err.into());
                tsfn.call(
                    (error_code, error_message),
                    ThreadsafeFunctionCallMode::Blocking,
                );
            }
        }
    });
    Ok(())
}
fn get_error_code_and_message(err: &UserDomainError) -> (String, String) {
    match err {
        UserDomainError::IllegalParameters(_) => (
            PARAMS_ERROR.to_string(),
            format!("IllegalParameters: {}", err),
        ),
        UserDomainError::UserTaskJoinError(_) => {
            ("101".to_string(), format!("UserTaskJoinError: {}", err))
        }
        UserDomainError::UserTaskFaild(_) => ("102".to_string(), format!("UserTaskFaild: {}", err)),
        UserDomainError::HttpTokenIvalidate(_) => {
            ("103".to_string(), format!("HttpTokenIvalidate: {}", err))
        }
        UserDomainError::HttpRequstFaild(e) => match e {
            RequestError::ResponseError(api_response) => (
                api_response.ret_code.as_str().to_string(),
                api_response.ret_info.clone(),
            ),
            _ => ("104".to_string(), format!("HttpTokenIvalidate: {}", err)),
        },
        UserDomainError::RefreshUserFaild => {
            ("105".to_string(), format!("RefreshUserFaild: {}", err))
        }
        UserDomainError::ParametersError(_) => (
            PARAMS_ERROR.to_string(),
            format!("ParametersError: {}", err),
        ),
        UserDomainError::UserStateUnLogin => (
            UNLOGING_CODE.to_string(),
            format!("UserStateUnLogin: {}", err),
        ),
    }
}
