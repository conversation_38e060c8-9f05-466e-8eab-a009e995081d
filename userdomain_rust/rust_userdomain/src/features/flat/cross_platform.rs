use crate::api::device::Device;
use crate::api::error::UserDomainError;
use crate::api::event::UserDomainEvent;
use crate::api::event::UserDomainEvent::*;

use crate::api::family::Family;
use crate::api::user_domain::{UserDomainPlatform, UserDomainState};
use crate::api::user_domain_manager::UserDomainManager;
use crate::data_source::device_data_source::DeviceDataSourceImpl;
use crate::data_source::family_data_source::FamilyDataSourceImpl;
use crate::data_source::user_data_source::UserDataSourceImpl;
use crate::features::flat::user_domain_generated::com::haier::uhome::uplus::rust::userdomain::*;
use crate::models::address_args::AddressArgs;
use crate::models::address_info::{Address, AddressInfo};
use crate::models::auth_data::AuthData;
use crate::models::device_card_args::{AggCardItemArgs, AggregationSwitchArgs, FamilyAggItemArgs};
use crate::models::device_info::{DeviceInfo, DeviceOwnerInfo, DevicePermission};
use crate::models::family_args::{CreateFamilyRoomArgs, FamilyArgs, RoomOrderArgs};
use crate::models::family_info::FamilyInfo;
use crate::models::floor_args::FloorArgs;
use crate::models::floor_info::FloorInfo;
use crate::models::location::Location;
use crate::models::member_info::{FamilyMemberInfo, MemberInfo};
use crate::models::room::Room;
use crate::models::room_args::RoomArgs;
use crate::models::user_args::UserArgs;
use crate::models::user_info::UserInfo;
use crate::models::virtual_member_args::VirtualMemberArgs;
use crate::server_apis::family_apis::operate_family_device_api::DeviceResult;
use fbs::{
    BoolWrapper, BoolWrapperArgs, FBSAddress, FBSAddressArgs, FBSAddressInfo, FBSAddressInfoArgs,
    FBSAddressInfoList, FBSAddressInfoListArgs, FBSAuthData, FBSAuthDataArgs, FBSDevice,
    FBSDeviceArgs, FBSDeviceAuth, FBSDeviceAuthArgs, FBSDeviceEntry, FBSDeviceEntryArgs,
    FBSDeviceInfo, FBSDeviceInfoArgs, FBSDeviceList, FBSDeviceListArgs, FBSDeviceMap,
    FBSDeviceMapArgs, FBSDeviceOperationResult, FBSDeviceOperationResultArgs, FBSDeviceOwnerInfo,
    FBSDeviceOwnerInfoArgs, FBSDevicePermission, FBSDevicePermissionArgs, FBSDeviceResult,
    FBSDeviceResultArgs, FBSFamily, FBSFamilyArgs, FBSFamilyEntry, FBSFamilyEntryArgs,
    FBSFamilyInfo, FBSFamilyInfoArgs, FBSFamilyList, FBSFamilyListArgs, FBSFamilyMap,
    FBSFamilyMapArgs, FBSFamilyMemberInfo, FBSFamilyMemberInfoArgs, FBSFloorInfo, FBSFloorInfoArgs,
    FBSLocation, FBSLocationArgs, FBSMemberInfo, FBSMemberInfoArgs, FBSRoom, FBSRoomArgs,
    FBSShareDeviceCardInfo, FBSShareDeviceCardInfoArgs, FBSUserInfo, FBSUserInfoArgs,
    FBSUserdomainMessage, FBSUserdomainMessageArgs, Int32Wrapper, Int32WrapperArgs,
    NoneWrapper, NoneWrapperArgs, StrWrapper, StrWrapperArgs, UserDomainFlat,
    UserDomainFlatArgs, UserdomainContainer,
};
use flatbuffers::{FlatBufferBuilder, ForwardsUOffset, UnionWIPOffset, Vector, WIPOffset};
use log::error;
use log::{debug, info, warn};
use request_rust::request::error::RequestError;
use request_rust::setting::request_setting::{AppNetworkEnv, RequestConfigManager};
use request_rust::Certificate;
use serde_json::Value;
use std::collections::HashMap;
use std::fs;
use std::io;
use task_manager::platform::function::PlatformConsumer;
use crate::features::flat::user_domain_generated::com::haier::uhome::uplus::rust::userdomain::fbs::{FBSRoomList, FBSRoomListArgs};

static EMPTY: String = String::new();
const ACTION: &str = "action";
const DEFAULT_SIZE: usize = 1024;
const SUCCESS_CODE: &str = "000000";
const UNLOGING_CODE: &str = "110001";
const FAMILY_NOT_FOUND: &str = "110003";
const TARGET_FAMILY_NOT_FOUND: &str = "110009";
const DEVICE_NOT_FOUND: &str = "110007";
const BATCH_DEVICE_NOT_FOUND: &str = "110010";
const FAMILY_ROOM_NOT_FOUND: &str = "110004";
const PARAMS_ERROR: &str = "900003";

macro_rules! require_params {
    ($params:expr, $($param:expr),+) => {
        for &param in &[$($param),+] {
            if !$params.contains_key(param) {
                 warn!("userdomain: required parameter '{}' is missing", param);
                return invalid_arg_result(&format!("{} is required", param));
            }
        }
    };
}

pub fn lib_userdomain_cross_platform(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("userdomain: executing action: {}", action);
    match action {
        "init" => init(params),
        "update_oauth_data" => update_oauth_data(params),
        "get_oauth_data" => get_oauth_data(),
        "cancel_login" => cancel_login(),
        "is_refresh_completed" => is_refresh_completed(),
        "is_refresh_device_list_completed" => is_refresh_device_list_completed(),
        "is_refresh_family_list_completed" => is_refresh_family_list_completed(),
        "is_refresh_user_completed" => is_refresh_user_completed(),
        "get_state" => get_state(),
        "get_http_request_retry_delay" => get_http_request_retry_delay(),
        "set_http_request_retry_delay" => set_http_request_retry_delay(params),
        "set_user_domain_platform" => set_user_domain_platform(params),
        "get_user_domain_platform" => get_user_domain_platform(),
        "set_refresh_family_list_enable" => set_refresh_family_list_enable(params),
        "is_refresh_family_list_enable" => is_refresh_family_list_enable(),
        "set_refresh_device_list_enable" => set_refresh_device_list_enable(params),
        "is_refresh_device_list_enable" => is_refresh_device_list_enable(),
        "set_pre_refresh_device_list" => set_pre_refresh_device_list(params),
        "is_pre_refresh_device_list" => is_pre_refresh_device_list(),
        "get_user_info" => get_user_info(),
        "get_iot_user_id" => get_iot_user_id(),
        "get_uc_user_id" => get_uc_user_id(),
        "get_current_family" => get_current_family(),
        "get_devices_map_by_user" => get_devices_map_by_user(),
        "get_familys_map_by_user" => get_familys_map_by_user(),
        "get_family_by_id" => get_family_by_id(params),
        "get_device_by_id" => get_device_by_id(params),
        "get_device_support_shared" => get_device_support_shared(params),
        "get_devices_map_by_family_id" => get_devices_map_by_family_id(params),
        "get_device_list_by_family_id" => get_device_list_by_family_id(params),
        "get_default_address" => get_default_address(),
        "get_address_list" => get_address_list(),
        "remove_observer" => remove_observer(params),
        "get_member_type" => get_member_type(params),
        "get_join_time" => get_join_time(params),
        _ => {
            warn!("userdomain: unsupported action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub async fn lib_userdomain_cross_platform_async(params: HashMap<String, String>) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("userdomain: executing async action: {}", action);
    match action {
        "auto_refresh_token" => return auto_refresh_token().await,
        "schedule_refresh_token" => return schedule_refresh_token().await,
        _ => {}
    }
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    match action {
        "set_current_family" => set_current_family(params).await,
        "refresh_user_info" => refresh_user_info().await,
        "refresh_device_list" => refresh_device_list().await,
        "refresh_family_list" => refresh_family_list().await,
        "refresh_address_list" => refresh_address_list().await,
        "modify_user_info" => modify_user_info(params).await,
        "refresh_user" => refresh_user().await,
        "logout" => logout().await,
        "create_address" => create_address(params).await,
        "edit_address" => edit_address(params).await,
        "delete_address" => delete_address(params).await,
        "update_device_name" => update_device_name(params).await,
        "update_device_name_and_check" => update_device_name_and_check(params).await,
        "query_family_info" => query_family_info(params).await,
        "query_room_list" => query_room_list(params).await,
        "create_floor" => create_floor(params).await,
        "delete_floor" => delete_floor(params).await,
        "modify_family_info" => modify_family_info(params).await,
        "create_family" => create_family(params).await,
        "add_room" => add_room(params).await,
        "remove_room" => remove_room(params).await,
        "update_room_name" => update_room_name(params).await,
        "exit_family_as_admin" => exit_family_as_admin(params).await,
        "unbind_devices" => unbind_devices(params).await,
        "change_family_admin" => change_family_admin(params).await,
        "exit_family_as_member" => exit_family_as_member(params).await,
        "query_first_member" => query_first_member(params).await,
        "add_virtual_member" => add_virtual_member(params).await,
        "modify_virtual_member" => modify_virtual_member(params).await,
        "modify_virtual_member_role" => modify_virtual_member_role(params).await,
        "modify_member_role" => modify_member_role(params).await,
        "save_rooms_order" => save_rooms_order(params).await,
        "delete_family_member" => delete_family_member(params).await,
        "get_group_device_list" => get_group_device_list(params).await,
        "reply_family_invite" => reply_family_invite(params).await,
        "reply_join_family" => reply_join_family(params).await,
        "admin_invite_member" => admin_invite_member(params).await,
        "delete_family_as_admin" => delete_family_as_admin(params).await,
        "move_devices_to_other_room" => move_devices_to_other_room(params).await,
        "move_devices_to_other_family" => move_devices_to_other_family(params).await,
        "modify_user_avatar" => modify_user_avatar(params).await,
        "get_user_info_async" => get_user_info_async().await,
        "confirm_device_sharing_relation" => confirm_device_sharing_relation(params).await,
        "cancel_device_sharing_relation" => cancel_device_sharing_relation(params).await,
        "modify_device_card_status" => modify_device_card_status(params).await,
        "modify_device_aggregation" => modify_device_aggregation(params).await,
        "modify_aggregation_switch" => modify_aggregation_switch(params).await,
        "modify_member_type" => modify_member_type(params).await,
        _ => {
            warn!("Unsupported async action: {}", action);
            invalid_arg_result("unsupported action")
        }
    }
}

pub fn lib_userdomain_cross_platform_consumer_data(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8> {
    let action = params.get(ACTION).unwrap_or(&EMPTY).as_str();
    info!("userdomain: executing action: {}", action);
    match action {
        "add_observer" => add_observer(consumer),
        _ => invalid_arg_result("unsupported action"),
    }
}

fn add_observer(consumer: impl PlatformConsumer + 'static) -> Vec<u8> {
    let observer = move |event: UserDomainEvent| {
        let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
        let (code, container_type, container) = match event {
            MessageRefreshTokenSuccess(oauth_data) => {
                let auth_data = create_fbs_auth_data(&mut builder, &oauth_data);
                (
                    9,
                    UserdomainContainer::FBSAuthData,
                    Some(auth_data.as_union_value()),
                )
            }
            MessageRefreshTokenFailed => (
                6,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageTokenInvalid => (
                2,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageTokenMismatchDevice => (
                1,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageLogout => (
                3,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageCancelLogin => (
                4,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageRefreshComplete => (
                7,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageRefreshFailure => (
                8,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageUserInfoRefreshSuccess(user_info) => {
                let user_data = create_fbs_user_info(&mut builder, &user_info);
                (
                    10,
                    UserdomainContainer::FBSUserInfo,
                    Some(user_data.as_union_value()),
                )
            }
            MessageUserInfoRefreshFailed => (
                11,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageDeviceListRefreshSuccess(device_list) => {
                let fbs_device_list = device_list
                    .iter()
                    .map(|x| create_fbs_device(&mut builder, x))
                    .collect::<Vec<_>>();
                let fbs_device = builder.create_vector(&fbs_device_list);
                let fbs_device_list_wip = FBSDeviceList::create(
                    &mut builder,
                    &FBSDeviceListArgs {
                        devices: Some(fbs_device),
                    },
                );
                (
                    12,
                    UserdomainContainer::FBSDeviceList,
                    Some(fbs_device_list_wip.as_union_value()),
                )
            }
            MessageDeviceListRefreshFailed => (
                13,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageFamilyListRefreshSuccess(family_list) => {
                let fbs_family_list = family_list
                    .iter()
                    .map(|x| create_fbs_family(&mut builder, x))
                    .collect::<Vec<_>>();
                let fbs_familys = builder.create_vector(&fbs_family_list);
                let fbs_family_list_wip = FBSFamilyList::create(
                    &mut builder,
                    &FBSFamilyListArgs {
                        familys: Some(fbs_familys),
                    },
                );
                (
                    14,
                    UserdomainContainer::FBSFamilyList,
                    Some(fbs_family_list_wip.as_union_value()),
                )
            }
            MessageFamilyListRefreshFailed => (
                15,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            MessageCurrentFamilyChanged(family) => {
                let family_data = create_fbs_family(&mut builder, &family);
                (
                    16,
                    UserdomainContainer::FBSFamily,
                    Some(family_data.as_union_value()),
                )
            }
            MessageRefreshDeviceListCache => (
                17,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
            _ => (
                -1,
                UserdomainContainer::NoneWrapper,
                Some(NoneWrapper::create(&mut builder, &NoneWrapperArgs {}).as_union_value()),
            ),
        };

        let data = FBSUserdomainMessageArgs {
            code,
            container_type,
            container,
        };
        let userdomain_message = FBSUserdomainMessage::create(&mut builder, &data);
        builder.finish(userdomain_message, None);
        let data = builder.finished_data().to_vec();
        consumer.accept(data);
    };

    let observer_id = UserDomainManager::get_instance()
        .get_user_domain()
        .add_observer(observer);
    string_result(observer_id)
}

fn init(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "app_id",
        "app_key",
        "app_version",
        "client_id",
        "network_env",
        "gray_mode",
        "platform"
    );

    let network_env =
        int_to_app_network_env(params.get("network_env").unwrap().parse().unwrap_or(2));
    info!("init network_env {:?}", network_env);

    let platform = match params.get("platform").unwrap().as_str() {
        "harmony" => UserDomainPlatform::Harmony,
        "homelandSYN" => UserDomainPlatform::HomelandSYN,
        "homelandShop" => UserDomainPlatform::HomelandShop,
        "homelandLite" => UserDomainPlatform::HomelandLite,
        "southEastAsia" => UserDomainPlatform::SouthEastAsia,
        _ => UserDomainPlatform::Homeland,
    };
    let mut cert_chain: Vec<Certificate> = vec![];
    #[cfg(feature = "ohos")]
    {
        use crate::features::ohos_cert::{get_ohos_cert_paths, load_cert_chain};
        let paths = get_ohos_cert_paths();
        cert_chain = load_cert_chain(paths).unwrap_or_else(|e| {
            warn!("ohos load_cert_chain failed, network is unavailable. {}", e);
            vec![]
        });
    }

    RequestConfigManager::get_instance().write_settings(|setting| {
        setting.set_app_id(params.get("app_id").unwrap().to_string());
        setting.set_app_key(params.get("app_key").unwrap().to_string());
        setting.set_app_version(params.get("app_version").unwrap().to_string());
        setting.set_client_id(params.get("client_id").unwrap().to_string());
        setting.set_root_certificates(cert_chain);
        setting.set_network_env(network_env);
    });

    UserDomainManager::get_instance()
        .get_setting()
        .set_gray_mode(
            params
                .get("gray_mode")
                .unwrap()
                .eq_ignore_ascii_case("true"),
        );
    UserDomainManager::get_instance()
        .get_setting()
        .set_user_domain_platform(platform);

    UserDomainManager::get_instance().init_user_domain(
        Box::new(UserDataSourceImpl::new()),
        Box::new(FamilyDataSourceImpl::new()),
        Box::new(DeviceDataSourceImpl::new()),
    );
    debug!("init success");
    success_result()
}

fn update_oauth_data(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "access_token",
        "expires_in",
        "refresh_token",
        "token_type",
        "uhome_access_token",
        "uhome_user_id",
        "uc_user_id"
    );

    let auth_data = AuthData::new(
        params.get("access_token").unwrap().to_string(),
        params
            .get("expires_in")
            .unwrap()
            .parse()
            .unwrap_or(0)
            .into(),
        params.get("refresh_token").unwrap().to_string(),
        params.get("scope").unwrap_or(&EMPTY).to_string(),
        params.get("token_type").unwrap().to_string(),
        params.get("uhome_access_token").unwrap().to_string(),
        params.get("uhome_user_id").unwrap().to_string(),
        0,
        params.get("uc_user_id").unwrap().to_string(),
    );
    let result = UserDomainManager::get_instance()
        .get_user_domain()
        .update_oauth_data(auth_data);
    debug!("update_oauth_data result: {}", result);
    if result {
        success_result()
    } else {
        failure_result("update_oauth_data failed", "-1")
    }
}

fn get_oauth_data() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let oauth_data = UserDomainManager::get_instance()
        .get_user_domain()
        .get_oauth_data();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let auth_data = create_fbs_auth_data(&mut builder, &oauth_data);
    data_result(
        &mut builder,
        auth_data.as_union_value(),
        UserdomainContainer::FBSAuthData,
    )
}

fn cancel_login() -> Vec<u8> {
    UserDomainManager::get_instance()
        .get_user_domain()
        .cancel_login();
    success_result()
}

fn is_refresh_completed() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .is_refresh_completed(),
    )
}

fn is_refresh_device_list_completed() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .is_refresh_device_list_completed(),
    )
}

fn is_refresh_family_list_completed() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .is_refresh_family_list_completed(),
    )
}

fn is_refresh_user_completed() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .is_refresh_user_completed(),
    )
}

fn get_state() -> Vec<u8> {
    i32_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .state()
            .value(),
    )
}

fn get_http_request_retry_delay() -> Vec<u8> {
    i32_result(
        UserDomainManager::get_instance()
            .get_setting()
            .get_http_request_retry_delay(),
    )
}

fn set_http_request_retry_delay(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "http_request_retry_delay");
    let delay = params
        .get("http_request_retry_delay")
        .unwrap()
        .parse()
        .unwrap_or(3);
    UserDomainManager::get_instance()
        .get_setting()
        .set_http_request_retry_delay(delay);
    success_result()
}

fn set_user_domain_platform(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "user_domain_platform");
    let platform = match params
        .get("user_domain_platform")
        .unwrap()
        .parse()
        .unwrap_or(0)
    {
        0 => UserDomainPlatform::Homeland,
        1 => UserDomainPlatform::SouthEastAsia,
        2 => UserDomainPlatform::HomelandSYN,
        3 => UserDomainPlatform::HomelandShop,
        4 => UserDomainPlatform::Harmony,
        5 => UserDomainPlatform::HomelandLite,
        _ => UserDomainPlatform::Unknow,
    };
    UserDomainManager::get_instance()
        .get_setting()
        .set_user_domain_platform(platform);
    success_result()
}

fn get_user_domain_platform() -> Vec<u8> {
    let platform = match UserDomainManager::get_instance()
        .get_setting()
        .get_user_domain_platform()
    {
        UserDomainPlatform::Homeland => 0,
        UserDomainPlatform::SouthEastAsia => 1,
        UserDomainPlatform::HomelandSYN => 2,
        UserDomainPlatform::HomelandShop => 3,
        UserDomainPlatform::Harmony => 4,
        UserDomainPlatform::HomelandLite => 5,
        _ => 6,
    };
    i32_result(platform)
}

fn is_refresh_device_list_enable() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_setting()
            .is_refresh_device_list_enable(),
    )
}

fn is_refresh_family_list_enable() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_setting()
            .is_refresh_family_list_enable(),
    )
}

fn set_refresh_device_list_enable(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "refresh_device_list_enable");
    let enable = params
        .get("refresh_device_list_enable")
        .unwrap()
        .parse()
        .unwrap_or(true);
    UserDomainManager::get_instance()
        .get_setting()
        .set_refresh_device_list_enable(enable);
    success_result()
}
fn set_pre_refresh_device_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "pre_refresh_device_list");
    let enabled = params
        .get("pre_refresh_device_list")
        .unwrap()
        .parse()
        .unwrap_or(false);
    UserDomainManager::get_instance()
        .get_setting()
        .set_pre_refresh_device_list(enabled);
    success_result()
}
fn is_pre_refresh_device_list() -> Vec<u8> {
    bool_result(
        UserDomainManager::get_instance()
            .get_setting()
            .is_pre_refresh_device_list(),
    )
}

fn set_refresh_family_list_enable(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "refresh_family_list_enable");
    let enable = params
        .get("refresh_family_list_enable")
        .unwrap()
        .parse()
        .unwrap_or(true);
    UserDomainManager::get_instance()
        .get_setting()
        .set_refresh_family_list_enable(enable);
    success_result()
}

fn get_user_info() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let user_info = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_user_info = create_fbs_user_info(&mut builder, &user_info);
    data_result(
        &mut builder,
        fbs_user_info.as_union_value(),
        UserdomainContainer::FBSUserInfo,
    )
}

fn get_iot_user_id() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    string_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_iot_user_id(),
    )
}

fn get_uc_user_id() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    string_result(
        UserDomainManager::get_instance()
            .get_user_domain()
            .get_user()
            .get_uc_user_id(),
    )
}

fn get_current_family() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_current_family();
    if family.is_none() {
        return failure_result("current family not found", FAMILY_NOT_FOUND);
    }
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_family = create_fbs_family(&mut builder, &family.unwrap());
    data_result(
        &mut builder,
        fbs_family.as_union_value(),
        UserdomainContainer::FBSFamily,
    )
}

fn get_familys_map_by_user() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let familys_map = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_familys_map();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_familys_map = create_fbs_familys_map(&mut builder, &familys_map);
    data_result(
        &mut builder,
        fbs_familys_map.as_union_value(),
        UserdomainContainer::FBSFamilyMap,
    )
}

fn get_family_by_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let family_id = params.get("family_id").unwrap_or(&EMPTY);
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    }
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_family = create_fbs_family(&mut builder, &family.unwrap());
    data_result(
        &mut builder,
        fbs_family.as_union_value(),
        UserdomainContainer::FBSFamily,
    )
}

fn get_default_address() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let default_address = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info()
        .get_default_address();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_address = match default_address {
        Some(address) => create_fbs_address_info(&mut builder, &address),
        None => FBSAddressInfo::create(&mut builder, &FBSAddressInfoArgs::default()),
    };
    data_result(
        &mut builder,
        fbs_address.as_union_value(),
        UserdomainContainer::FBSAddressInfo,
    )
}

fn remove_observer(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "observer_id");
    let observer_id = params.get("observer_id").unwrap();
    UserDomainManager::get_instance()
        .get_user_domain()
        .remove_observer(observer_id);
    success_result()
}

fn get_address_list() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let address_list = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info()
        .get_address_list();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_address_list = address_list
        .iter()
        .map(|x| create_fbs_address_info(&mut builder, x))
        .collect::<Vec<_>>();
    let fbs_address = builder.create_vector(&fbs_address_list);
    let fbs_addr_info_list = FBSAddressInfoList::create(
        &mut builder,
        &FBSAddressInfoListArgs {
            addresses: Some(fbs_address),
        },
    );
    data_result(
        &mut builder,
        fbs_addr_info_list.as_union_value(),
        UserdomainContainer::FBSAddressInfoList,
    )
}

fn get_device_by_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let device_id = params.get("device_id").unwrap();
    let device = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_device_by_id(device_id);
    if device.is_none() {
        return failure_result("device not found", DEVICE_NOT_FOUND);
    }
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_device = create_fbs_device(&mut builder, &device.unwrap());
    data_result(
        &mut builder,
        fbs_device.as_union_value(),
        UserdomainContainer::FBSDevice,
    )
}

/**
 * 获取设备是否支持共享
 * @param device_id 设备ID
 * @return 是否支持共享
 */
fn get_device_support_shared(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "device_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let device_id = params.get("device_id").unwrap();
    let device = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_device_by_id(device_id);
    if device.is_none() {
        return failure_result("device not found", DEVICE_NOT_FOUND);
    }

    let is_supported = device.unwrap().support_shared();
    bool_result(is_supported)
}

fn get_devices_map_by_user() -> Vec<u8> {
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let devices_map = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_devices_map();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_devices_map = create_fbs_devices_map(&mut builder, &devices_map);
    data_result(
        &mut builder,
        fbs_devices_map.as_union_value(),
        UserdomainContainer::FBSDeviceMap,
    )
}

fn get_devices_map_by_family_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let family_id = params.get("family_id").unwrap();
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    }
    let device_map = family.unwrap().get_devices_map();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_devices_map = create_fbs_devices_map(&mut builder, &device_map);
    data_result(
        &mut builder,
        fbs_devices_map.as_union_value(),
        UserdomainContainer::FBSDeviceMap,
    )
}

fn get_device_list_by_family_id(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }
    let family_id = params.get("family_id").unwrap();
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    }
    let device_list = family.unwrap().get_device_list();
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let fbs_device_list = device_list
        .iter()
        .map(|x| create_fbs_device(&mut builder, x))
        .collect::<Vec<_>>();
    let fbs_device = builder.create_vector(&fbs_device_list);
    let fbs_wip = FBSDeviceList::create(
        &mut builder,
        &FBSDeviceListArgs {
            devices: Some(fbs_device),
        },
    );
    data_result(
        &mut builder,
        fbs_wip.as_union_value(),
        UserdomainContainer::FBSDeviceList,
    )
}

async fn set_current_family(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap_or(&EMPTY);
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    }
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .set_current_family(family_id.to_string())
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("set_current_family error:{}", msg), code)
        }
    }
}

async fn auto_refresh_token() -> Vec<u8> {
    UserDomainManager::get_instance()
        .get_user_domain()
        .auto_refresh_token()
        .await;
    success_result()
}

async fn refresh_user_info() -> Vec<u8> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .refresh_user_info()
        .await
    {
        Ok(user_info) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let fbs_user_info = create_fbs_user_info(&mut builder, &user_info);
            data_result(
                &mut builder,
                fbs_user_info.as_union_value(),
                UserdomainContainer::FBSUserInfo,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("refresh_user_info error:{}", msg), code)
        }
    }
}

async fn refresh_device_list() -> Vec<u8> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .refresh_device_list()
        .await
    {
        Ok(device_list) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let mut devices: Vec<WIPOffset<FBSDevice>> = Vec::new();
            for device in device_list {
                let fbs_device = create_fbs_device(&mut builder, &device);
                devices.push(fbs_device);
            }
            let devices = builder.create_vector(&devices);
            let device_list_args = FBSDeviceListArgs {
                devices: Some(devices),
            };
            let device_list = FBSDeviceList::create(&mut builder, &device_list_args);
            data_result(
                &mut builder,
                device_list.as_union_value(),
                UserdomainContainer::FBSDeviceList,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("refresh_device_list error:{}", msg), code)
        }
    }
}

async fn refresh_family_list() -> Vec<u8> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .refresh_family_list()
        .await
    {
        Ok(family_list) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let mut families: Vec<WIPOffset<FBSFamily>> = Vec::new();
            for family in family_list {
                let fbs_family = create_fbs_family(&mut builder, &family);
                families.push(fbs_family);
            }
            let families = builder.create_vector(&families);
            let family_list_args = FBSFamilyListArgs {
                familys: Some(families),
            };
            let family_list = FBSFamilyList::create(&mut builder, &family_list_args);
            data_result(
                &mut builder,
                family_list.as_union_value(),
                UserdomainContainer::FBSFamilyList,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("refresh_family_list error:{}", msg), code)
        }
    }
}

async fn refresh_address_list() -> Vec<u8> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .refresh_address_list()
        .await
    {
        Ok(address_list) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let mut infos: Vec<WIPOffset<FBSAddressInfo>> = Vec::new();
            for info in address_list {
                let fbs_info = create_fbs_address_info(&mut builder, &info);
                infos.push(fbs_info);
            }
            let info_list = builder.create_vector(&infos);
            let info_list_args = FBSAddressInfoListArgs {
                addresses: Some(info_list),
            };
            let address_list = FBSAddressInfoList::create(&mut builder, &info_list_args);
            data_result(
                &mut builder,
                address_list.as_union_value(),
                UserdomainContainer::FBSAddressInfoList,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("refresh_address_list error:{}", msg), code)
        }
    }
}

async fn modify_user_info(params: HashMap<String, String>) -> Vec<u8> {
    let user_info = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info();
    let given_name = match params.get("givenName") {
        Some(given_name) => given_name.to_string(),
        None => user_info.given_name.unwrap_or("".to_string()),
    };
    let nickname = match params.get("nickname") {
        Some(nickname) => nickname.to_string(),
        None => user_info.nickname.unwrap_or("".to_string()),
    };
    let family_num = match params.get("familyNum") {
        Some(family_num) => family_num.to_string(),
        None => user_info.family_num.unwrap_or("".to_string()),
    };
    let gender = match params.get("gender") {
        Some(gender) => gender.to_string(),
        None => user_info.gender.unwrap_or("".to_string()),
    };
    let marriage = match params.get("marriage") {
        Some(marriage) => marriage.to_string(),
        None => user_info.marriage.unwrap_or("".to_string()),
    };
    let birthday = match params.get("birthday") {
        Some(birthday) => birthday.to_string(),
        None => user_info.birthday.unwrap_or("".to_string()),
    };
    let education = match params.get("education") {
        Some(education) => education.to_string(),
        None => user_info.education.unwrap_or("".to_string()),
    };
    let avatar_url = match params.get("avatarUrl") {
        Some(avatar_url) => avatar_url.to_string(),
        None => user_info.avatar_url.unwrap_or("".to_string()),
    };
    let income = match params.get("income") {
        Some(income) => income.to_string(),
        None => user_info.income.unwrap_or("".to_string()),
    };
    let extra_phone = match params.get("extraPhone") {
        Some(extra_phone) => extra_phone.to_string(),
        None => user_info.extra_phone.unwrap_or("".to_string()),
    };
    let height = match params.get("height") {
        Some(height) => height.to_string(),
        None => user_info.height.unwrap_or("".to_string()),
    };
    let weight = match params.get("weight") {
        Some(weight) => weight.to_string(),
        None => user_info.weight.unwrap_or("".to_string()),
    };
    let privacy_country_code = match params.get("privacyCountryCode") {
        Some(privacy_country_code) => privacy_country_code.to_string(),
        None => user_info.privacy_country_code.unwrap_or("".to_string()),
    };
    let user_args = UserArgs {
        user_id: user_info.user_id,
        given_name,
        nickname,
        family_num,
        gender,
        marriage,
        birthday,
        education,
        avatar_url,
        income,
        extra_phone,
        height,
        weight,
        privacy_country_code: Some(privacy_country_code),
    };
    let result = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .modify_user_info(user_args)
        .await;
    match result {
        Ok(()) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("modify_user_info error:{}", msg), code)
        }
    }
}

async fn create_family(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_name");

    let mut room_args = Vec::new();
    if let Some(json) = params.get("room_names") {
        let v: Value = serde_json::from_str(json).unwrap_or(Value::Null);
        if let Some(rooms) = v.as_array() {
            for room in rooms {
                let room_class = room["room_class"].as_str().unwrap_or_default().to_string();
                let room_name = room["room_name"].as_str().unwrap_or_default().to_string();
                let room = CreateFamilyRoomArgs {
                    room_class,
                    room_name,
                };
                room_args.push(room);
            }
        }
    }

    let family_args = FamilyArgs {
        name: params.get("family_name").unwrap().to_string(),
        position: params.get("position").unwrap_or(&EMPTY).to_string(),
        latitude: params.get("latitude").unwrap_or(&EMPTY).to_string(),
        longitude: params.get("longitude").unwrap_or(&EMPTY).to_string(),
        city_code: params.get("city_code").unwrap_or(&EMPTY).to_string(),
        room_names: room_args,
    };
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .create_family(family_args)
        .await
    {
        Ok(s) => string_result(s),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("create_family error:{}", msg), code)
        }
    }
}

async fn refresh_user() -> Vec<u8> {
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .refresh_user()
        .await
    {
        Ok(()) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("refresh_user error:{}", msg), code)
        }
    }
}

async fn modify_family_info(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "family_name");
    let family_args = FamilyArgs {
        name: params.get("family_name").unwrap().to_string(),
        position: params.get("position").unwrap_or(&EMPTY).to_string(),
        latitude: params.get("latitude").unwrap_or(&EMPTY).to_string(),
        longitude: params.get("longitude").unwrap_or(&EMPTY).to_string(),
        city_code: params.get("city_code").unwrap_or(&EMPTY).to_string(),
        room_names: vec![],
    };
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        None => failure_result("family not found", FAMILY_NOT_FOUND),
        Some(family) => match family.modify_family_info(family_args).await {
            Ok(()) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("modify_family_info error:{}", msg), code)
            }
        },
    }
}

async fn add_room(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "room_name", "family_id");

    let room_name = params.get("room_name").unwrap().to_string();
    let floor_order_id = params
        .get("floor_order_id")
        .unwrap_or(&String::new())
        .to_string();
    let room_class = params
        .get("room_class")
        .unwrap_or(&"客厅".to_string())
        .to_string();

    let room_args = RoomArgs {
        room_name,
        floor_order_id,
        room_class,
    };
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family.add_room(room_args).await {
            Ok(room) => {
                // 序列化Room结果
                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
                let fbs_room = create_fbs_room(&mut builder, &room);
                data_result(
                    &mut builder,
                    fbs_room.as_union_value(),
                    UserdomainContainer::FBSRoom,
                )
            }
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("add_room error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn remove_room(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "room_id", "family_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .remove_room(params.get("room_id").unwrap().to_string())
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("remove_room error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn update_room_name(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "room_id", "room_name");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .update_room_name(
                params.get("room_id").unwrap().to_string(),
                params.get("room_name").unwrap().to_string(),
            )
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("update_room_name error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn exit_family_as_admin(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "user_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .exit_family_as_admin(params.get("user_id").unwrap().to_string())
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("exit_family_as_admin error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn unbind_devices(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "device_infos");
    let device_infos = params.get("device_infos").unwrap();
    let device_id_list: Vec<String> = match serde_json::from_str(device_infos) {
        Ok(list) => list,
        Err(e) => {
            return failure_result(
                &format!("unbind_devices error:{}", e),
                BATCH_DEVICE_NOT_FOUND,
            );
        }
    };
    let device_list: Vec<DeviceInfo> = device_id_list
        .iter()
        .filter_map(|id| {
            UserDomainManager::get_instance()
                .get_user_domain()
                .get_user()
                .get_device_by_id(id)
                .map(|device| device.device_info)
        })
        .collect();
    if device_list.is_empty() {
        return failure_result("unbind_devices ", BATCH_DEVICE_NOT_FOUND);
    }
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family.unbind_devices(&device_list).await {
            Ok(result) => {
                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
                let success_device_results =
                    create_fbs_device_results(&mut builder, &result.success_devices);
                let success_devices_vector = builder.create_vector(&success_device_results);
                let failure_device_results =
                    create_fbs_device_results(&mut builder, &result.failure_devices);
                let failure_devices_vector = builder.create_vector(&failure_device_results);
                let fbs_result = FBSDeviceOperationResult::create(
                    &mut builder,
                    &FBSDeviceOperationResultArgs {
                        success_devices: Some(success_devices_vector),
                        failure_devices: Some(failure_devices_vector),
                    },
                );
                data_result(
                    &mut builder,
                    fbs_result.as_union_value(),
                    UserdomainContainer::FBSDeviceOperationResult,
                )
            }
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("unbind_devices error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn change_family_admin(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "user_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .reassign_family_administrator(params.get("user_id").unwrap().to_string())
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("change_family_admin error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn exit_family_as_member(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family.exit_family_as_member().await {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("exit_family_as_member error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn query_first_member(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family.query_first_member().await {
            Ok(family) => {
                let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
                let fbs_info = create_fbs_family_member_info(&mut builder, &family);
                data_result(
                    &mut builder,
                    fbs_info.as_union_value(),
                    UserdomainContainer::FBSFamilyMemberInfo,
                )
            }
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("query_first_member error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn add_virtual_member(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "member_name", "member_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .add_virtual_member(
                params.get("member_name").unwrap().to_string(),
                params.get("member_id").unwrap().to_string(),
            )
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("add_virtual_member error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn modify_virtual_member(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "family_id",
        "member_name",
        "member_id",
        "avatar_url",
        "birthday",
        "is_creator"
    );
    let virtual_member_args = VirtualMemberArgs {
        member_id: params.get("member_id").unwrap().to_string(),
        member_name: params.get("member_name").unwrap().to_string(),
        avatar_url: params.get("avatar_url").unwrap().to_string(),
        is_creator: params.get("is_creator").unwrap().parse().unwrap_or(false),
        birthday: params.get("birthday").unwrap().to_string(),
    };
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family.modify_virtual_member(virtual_member_args).await {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("modify_virtual_member error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn modify_virtual_member_role(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "member_role", "member_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .modify_virtual_member_role(
                params.get("member_id").unwrap().to_string(),
                params.get("member_role").unwrap().to_string(),
            )
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("modify_virtual_member_role error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn modify_member_role(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "member_role", "member_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .modify_member_role(
                params.get("member_id").unwrap().to_string(),
                params.get("member_role").unwrap().to_string(),
            )
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("modify_member_role error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn save_rooms_order(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "room_order_args");
    let family_id = params.get("family_id").unwrap();
    // 解析RoomOrderArgs参数
    let room_order_args: Vec<RoomOrderArgs> =
        match serde_json::from_str(params.get("room_order_args").unwrap()) {
            Ok(args) => args,
            Err(e) => {
                return failure_result(
                    &format!("save_rooms_order parse error:{}", e),
                    PARAMS_ERROR,
                );
            }
        };

    let option_family = fetch_family_by_id(family_id);
    match option_family {
        Some(family) => match family.save_rooms_order(room_order_args).await {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&msg, code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn delete_family_member(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "member_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => match family
            .delete_family_member(params.get("member_id").unwrap().to_string())
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("modify_member_role error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn get_group_device_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let option_family = fetch_family_by_id(params.get("family_id").unwrap());
    match option_family {
        Some(family) => {
            let device_list = family.get_group_device_list().await;
            match device_list {
                Ok(list) => {
                    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
                    let fbs_device_list = list
                        .iter()
                        .map(|x| create_fbs_device(&mut builder, x))
                        .collect::<Vec<_>>();
                    let fbs_device = builder.create_vector(&fbs_device_list);
                    let fbs_wip = FBSDeviceList::create(
                        &mut builder,
                        &FBSDeviceListArgs {
                            devices: Some(fbs_device),
                        },
                    );
                    data_result(
                        &mut builder,
                        fbs_wip.as_union_value(),
                        UserdomainContainer::FBSDeviceList,
                    )
                }
                Err(e) => {
                    let (code, msg) = get_error_code_and_message(&e);
                    failure_result(&format!("get_group_device_list error:{}", msg), code)
                }
            }
        }
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn reply_family_invite(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "invite_code", "member_name", "agree");
    let family_id = params.get("family_id").unwrap().to_string();
    let invite_code = params.get("invite_code").unwrap().to_string();
    let member_name = params.get("member_name").unwrap().to_string();
    let agree = params.get("agree").unwrap().parse().unwrap_or(false);
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .reply_family_invite_member(invite_code, family_id, agree, member_name)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("reply_family_invite error:{}", msg), code)
        }
    }
}

async fn reply_join_family(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "application_id", "agree");
    let application_id = params.get("application_id").unwrap().to_string();
    let agree = params.get("agree").unwrap().parse().unwrap_or(false);
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .reply_join_family(application_id, agree)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("reply_join_family error:{}", msg), code)
        }
    }
}

async fn admin_invite_member(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "user_id", "nickname", "member_type");
    let family = fetch_family_by_id(params.get("family_id").unwrap());
    let user_id = params.get("user_id").unwrap().to_string();
    let nickname = params.get("nickname").unwrap().to_string();
    let member_role = params
        .get("member_role")
        .unwrap_or(&String::new())
        .to_string();
    let member_type = params
        .get("member_type")
        .unwrap()
        .parse::<i32>()
        .unwrap_or(2);

    match family {
        Some(family) => match family
            .admin_invite_member(user_id, nickname, member_role, member_type)
            .await
        {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("admin_invite_member error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn delete_family_as_admin(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap();
    let family = fetch_family_by_id(family_id);
    match family {
        Some(family) => match family.delete_family_as_admin().await {
            Ok(_) => success_result(),
            Err(e) => {
                let (code, msg) = get_error_code_and_message(&e);
                failure_result(&format!("delete_family_as_admin error:{}", msg), code)
            }
        },
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

async fn move_devices_to_other_room(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "new_room_id", "device_infos");
    let family_id = params.get("family_id").unwrap();
    let new_room_id = params.get("new_room_id").unwrap();
    let device_infos = params.get("device_infos").unwrap();
    let family = fetch_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    };
    let family = family.unwrap();
    let room = find_room_by_id(&family, new_room_id);
    if room.is_none() {
        return failure_result("room not found", FAMILY_ROOM_NOT_FOUND);
    }
    let device_id_list: Vec<String> = match serde_json::from_str(device_infos) {
        Ok(list) => list,
        Err(e) => {
            return failure_result(
                &format!("move_devices_to_other_room error:{}", e),
                BATCH_DEVICE_NOT_FOUND,
            );
        }
    };
    let device_list: Vec<DeviceInfo> = device_id_list
        .iter()
        .filter_map(|id| {
            UserDomainManager::get_instance()
                .get_user_domain()
                .get_user()
                .get_device_by_id(id)
                .map(|device| device.device_info)
        })
        .collect();
    if device_list.is_empty() {
        return failure_result(
            "move_devices_to_other_room not find devices",
            BATCH_DEVICE_NOT_FOUND,
        );
    }
    let room = room.unwrap();
    match family.move_devices_to_other_room(&room, &device_list).await {
        Ok(result) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let success_device_results =
                create_fbs_device_results(&mut builder, &result.success_devices);
            let success_devices_vector = builder.create_vector(&success_device_results);
            let failure_device_results =
                create_fbs_device_results(&mut builder, &result.failure_devices);
            let failure_devices_vector = builder.create_vector(&failure_device_results);
            let fbs_result = FBSDeviceOperationResult::create(
                &mut builder,
                &FBSDeviceOperationResultArgs {
                    success_devices: Some(success_devices_vector),
                    failure_devices: Some(failure_devices_vector),
                },
            );
            data_result(
                &mut builder,
                fbs_result.as_union_value(),
                UserdomainContainer::FBSDeviceOperationResult,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("move_devices_to_other_room error:{}", msg), code)
        }
    }
}

fn find_room_by_id(family: &Family, room_id: &str) -> Option<Room> {
    family.family_info.floor_infos.as_ref().and_then(|floors| {
        floors
            .iter()
            .flat_map(|floor| &floor.floor_rooms)
            .find(|room| room.room_id == room_id)
            .cloned()
    })
}

async fn move_devices_to_other_family(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "target_family_id", "device_infos");
    let family_id = params.get("family_id").unwrap();
    let target_family_id = params.get("target_family_id").unwrap();
    let device_infos = params.get("device_infos").unwrap();
    let family = fetch_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    };
    let target_family = fetch_family_by_id(target_family_id);
    if target_family.is_none() {
        return failure_result("target family not found", TARGET_FAMILY_NOT_FOUND);
    };
    let device_id_list: Vec<String> = match serde_json::from_str(device_infos) {
        Ok(list) => list,
        Err(e) => {
            return failure_result(
                &format!("move_devices_to_other_family error:{}", e),
                BATCH_DEVICE_NOT_FOUND,
            );
        }
    };
    let device_list: Vec<DeviceInfo> = device_id_list
        .iter()
        .filter_map(|id| {
            UserDomainManager::get_instance()
                .get_user_domain()
                .get_user()
                .get_device_by_id(id)
                .map(|device| device.device_info)
        })
        .collect();
    if device_list.is_empty() {
        return failure_result(
            "move_devices_to_other_family not find devices",
            BATCH_DEVICE_NOT_FOUND,
        );
    }
    match family
        .unwrap()
        .move_devices_to_other_family(device_list, target_family_id.to_string())
        .await
    {
        Ok(result) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let success_device_results =
                create_fbs_device_results(&mut builder, &result.success_devices);
            let success_devices_vector = builder.create_vector(&success_device_results);
            let failure_device_results =
                create_fbs_device_results(&mut builder, &result.failure_devices);
            let failure_devices_vector = builder.create_vector(&failure_device_results);
            let fbs_result = FBSDeviceOperationResult::create(
                &mut builder,
                &FBSDeviceOperationResultArgs {
                    success_devices: Some(success_devices_vector),
                    failure_devices: Some(failure_devices_vector),
                },
            );
            data_result(
                &mut builder,
                fbs_result.as_union_value(),
                UserdomainContainer::FBSDeviceOperationResult,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("move_devices_to_other_family error:{}", msg), code)
        }
    }
}

async fn modify_user_avatar(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "image_path");
    let image_path = params.get("image_path").unwrap().to_string();
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .modify_user_avatar(image_path)
        .await
    {
        Ok(cloud_path) => string_result(cloud_path),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&msg, code)
        }
    }
}

async fn get_user_info_async() -> Vec<u8> {
    let user_info = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_user_info();
    if user_info.user_id.is_empty() {
        refresh_user_info().await
    } else {
        get_user_info()
    }
}

/**
 * 确认设备共享关系
 * @param share_uuid 共享ID
 */
async fn confirm_device_sharing_relation(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "share_uuid");
    let share_uuid = params.get("share_uuid").unwrap().to_string();
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .confirm_device_sharing_relation(share_uuid)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(
                &format!("confirm_device_sharing_relation error:{}", msg),
                code,
            )
        }
    }
}

/**
 * 取消设备共享关系
 * @param share_uuids 共享ID列表
 */
async fn cancel_device_sharing_relation(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "share_uuids");
    let share_uuids_str = params.get("share_uuids").unwrap();
    let share_uuids: Vec<String> = match serde_json::from_str(share_uuids_str) {
        Ok(list) => list,
        Err(e) => {
            return failure_result(
                &format!("cancel_device_sharing_relation error:{}", e),
                PARAMS_ERROR,
            );
        }
    };

    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .cancel_device_sharing_relation(share_uuids)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(
                &format!("cancel_device_sharing_relation error:{}", msg),
                code,
            )
        }
    }
}

async fn schedule_refresh_token() -> Vec<u8> {
    let auth_data = UserDomainManager::get_instance()
        .get_user_domain()
        .get_oauth_data();
    match UserDomainManager::get_instance()
        .get_user_domain()
        .schedule_refresh_token(&auth_data)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("schedule_refresh_token error:{}", msg), code)
        }
    }
}

fn create_fbs_device_results<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_results: &[DeviceResult],
) -> Vec<WIPOffset<FBSDeviceResult<'a>>> {
    device_results
        .iter()
        .map(|it| create_fbs_device_result(builder, it))
        .collect()
}

fn create_fbs_device_result<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device_result: &DeviceResult,
) -> WIPOffset<FBSDeviceResult<'a>> {
    let result = FBSDeviceResultArgs {
        device_id: Some(builder.create_string(&device_result.device_id)),
        device_name: Some(builder.create_string(&device_result.device_name)),
        reason_code: Some(builder.create_string(&device_result.reason_code)),
    };
    FBSDeviceResult::create(builder, &result)
}

async fn logout() -> Vec<u8> {
    let _ = UserDomainManager::get_instance()
        .get_user_domain()
        .logout()
        .await;
    success_result()
}

async fn create_address(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "cityId",
        "city",
        "district",
        "districtId",
        "province",
        "provinceId",
        "reviceName",
        "reviceMobile",
        "source"
    );
    let address_args = create_address_args(params);
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .create_address(address_args)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("create_address error:{}", msg), code)
        }
    }
}

async fn edit_address(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(
        params,
        "cityId",
        "city",
        "district",
        "districtId",
        "province",
        "provinceId",
        "reviceName",
        "reviceMobile",
        "source",
        "addressId"
    );
    let address_args = create_address_args(params);
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .edit_address(address_args)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("edit_address error:{}", msg), code)
        }
    }
}

async fn delete_address(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "addressId");
    let address_id = params.get("addressId").unwrap();
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .delete_address(address_id.to_string())
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("delete_address error:{}", msg), code)
        }
    }
}

async fn update_device_name(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "deviceId", "deviceName");
    let device_id = params.get("deviceId").unwrap();
    let device_name = params.get("deviceName").unwrap();
    let device = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_device_by_id(device_id);
    if device.is_none() {
        return failure_result("device_id not found", DEVICE_NOT_FOUND);
    }
    match device
        .unwrap()
        .update_device_name(device_name.to_string())
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("update_device_name error:{}", msg), code)
        }
    }
}

async fn update_device_name_and_check(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "deviceId", "deviceName", "bindType", "checkLevel");
    let device_id = params.get("deviceId").unwrap();
    let device_name = params.get("deviceName").unwrap();
    let bind_type = params.get("bindType").unwrap();
    let check_level = params.get("checkLevel").unwrap().parse().unwrap_or(false);
    let device = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_device_by_id(device_id);
    if device.is_none() {
        return failure_result("device_id not found", DEVICE_NOT_FOUND);
    }
    match device
        .unwrap()
        .update_device_name_and_check(device_name.to_string(), bind_type.to_string(), check_level)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("update_device_name_and_check error:{}", msg), code)
        }
    }
}

async fn query_family_info(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    let family_id = params.get("family_id").unwrap();
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family_id not found", FAMILY_NOT_FOUND);
    }
    match family.unwrap().query_family_info().await {
        Ok(family_info) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let fbs_family_info = create_fbs_family_info(&mut builder, &family_info);
            data_result(
                &mut builder,
                fbs_family_info.as_union_value(),
                UserdomainContainer::FBSFamilyInfo,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("query_family_info error:{}", msg), code)
        }
    }
}

async fn query_room_list(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "floor_id");
    let family_id = params.get("family_id").unwrap();
    let floor_id = params.get("floor_id").unwrap();
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family_id not found", FAMILY_NOT_FOUND);
    }
    match family.unwrap().query_room_list(floor_id.to_string()).await {
        Ok(rooms) => {
            let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
            let mut fbs_rooms = Vec::<WIPOffset<FBSRoom>>::new();
            for room in &rooms {
                let r = create_fbs_room(&mut builder, room);
                fbs_rooms.push(r);
            }
            let fbs_rooms = builder.create_vector(&fbs_rooms);
            let fbs_wip = FBSRoomList::create(
                &mut builder,
                &FBSRoomListArgs {
                    rooms: Some(fbs_rooms),
                },
            );
            data_result(
                &mut builder,
                fbs_wip.as_union_value(),
                UserdomainContainer::FBSRoomList,
            )
        }
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("query_room_list error:{}", msg), code)
        }
    }
}

async fn create_floor(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "floor_order_id");
    let family_id = params.get("family_id").unwrap();
    let floor_name = params.get("floor_name").unwrap_or(&EMPTY).to_string();
    let floor_order_id = params.get("floor_order_id").unwrap().to_string();
    let floor_id = params.get("floor_id").unwrap_or(&EMPTY).to_string();
    let floor_picture = params.get("floor_picture").unwrap_or(&EMPTY).to_string();
    let floor_label = params.get("floor_label").unwrap_or(&EMPTY).to_string();
    let floor_logo = params.get("floor_logo").unwrap_or(&EMPTY).to_string();
    let floor_class = params.get("floor_class").unwrap_or(&EMPTY).to_string();

    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family_id not found", FAMILY_NOT_FOUND);
    }
    let floor_args = FloorArgs {
        floor_name,
        floor_order_id,
        floor_picture,
        floor_label,
        floor_logo,
        floor_class,
        floor_id,
    };
    match family.unwrap().create_floor(floor_args).await {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("create_floor error:{}", msg), code)
        }
    }
}

async fn delete_floor(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "floor_id");
    let family_id = params.get("family_id").unwrap();
    let floor_id = params.get("floor_id").unwrap();
    let family = UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family_id not found", FAMILY_NOT_FOUND);
    }
    match family.unwrap().delete_floor(floor_id.to_string()).await {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("delete_floor error:{}", msg), code)
        }
    }
}

fn create_address_args(params: HashMap<String, String>) -> AddressArgs {
    AddressArgs {
        city_id: params.get("cityId").unwrap().to_string(),
        city: params.get("city").unwrap().to_string(),
        country_code: params.get("contryCode").unwrap_or(&EMPTY).to_string(),
        district: params.get("district").unwrap().to_string(),
        district_id: params.get("districtId").unwrap().to_string(),
        province: params.get("province").unwrap().to_string(),
        province_id: params.get("provinceId").unwrap().to_string(),
        line1: params.get("line1").unwrap_or(&EMPTY).to_string(),
        line2: params.get("line2").unwrap_or(&EMPTY).to_string(),
        postcode: params.get("postcode").unwrap_or(&EMPTY).to_string(),
        town: params.get("town").unwrap_or(&EMPTY).to_string(),
        town_id: params.get("townId").unwrap_or(&EMPTY).to_string(),
        email: params.get("email").unwrap_or(&EMPTY).to_string(),
        is_service: params.get("isService").unwrap().parse().unwrap_or(false),
        is_default: params.get("isDefault").unwrap().parse().unwrap_or(false),
        receiver_name: params.get("reviceName").unwrap().to_string(),
        receiver_mobile: params.get("reviceMobile").unwrap().to_string(),
        tag: params.get("tag").unwrap_or(&EMPTY).to_string(),
        source: params.get("source").unwrap().to_string(),
        user_id: params.get("userId").unwrap_or(&EMPTY).to_string(),
        address_id: params.get("addressId").unwrap_or(&EMPTY).to_string(),
    }
}

fn create_fbs_auth_data<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    auth_data: &AuthData,
) -> WIPOffset<FBSAuthData<'a>> {
    let auth_data_args = FBSAuthDataArgs {
        access_token: Some(builder.create_string(&auth_data.access_token)),
        expires_in: auth_data.expires_in as i64,
        refresh_token: Some(builder.create_string(&auth_data.refresh_token)),
        scope: Some(builder.create_string(&auth_data.scope)),
        token_type: Some(builder.create_string(&auth_data.token_type)),
        uhome_access_token: Some(builder.create_string(&auth_data.uhome_access_token)),
        uhome_user_id: Some(builder.create_string(&auth_data.uhome_user_id)),
        create_time: auth_data.create_time as i64,
        uc_user_id: Some(builder.create_string(&auth_data.uc_user_id)),
    };
    FBSAuthData::create(builder, &auth_data_args)
}

fn create_fbs_user_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    user_info: &UserInfo,
) -> WIPOffset<FBSUserInfo<'a>> {
    let fbs_user_info_args = FBSUserInfoArgs {
        given_name: user_info
            .given_name
            .as_ref()
            .map(|it| builder.create_string(it)),
        user_id: Some(builder.create_string(&user_info.user_id)),
        username: user_info
            .username
            .as_ref()
            .map(|it| builder.create_string(it)),
        gender: user_info
            .gender
            .as_ref()
            .map(|it| builder.create_string(it)),
        nickname: user_info
            .nickname
            .as_ref()
            .map(|it| builder.create_string(it)),
        mobile: user_info
            .mobile
            .as_ref()
            .map(|it| builder.create_string(it)),
        email: user_info.email.as_ref().map(|it| builder.create_string(it)),
        family_num: user_info
            .family_num
            .as_ref()
            .map(|it| builder.create_string(it)),
        birthday: user_info
            .birthday
            .as_ref()
            .map(|it| builder.create_string(it)),
        marriage: user_info
            .marriage
            .as_ref()
            .map(|it| builder.create_string(it)),
        education: user_info
            .education
            .as_ref()
            .map(|it| builder.create_string(it)),
        avatar_url: user_info
            .avatar_url
            .as_ref()
            .map(|it| builder.create_string(it)),
        extra_phone: user_info
            .extra_phone
            .as_ref()
            .map(|it| builder.create_string(it)),
        income: user_info
            .income
            .as_ref()
            .map(|it| builder.create_string(it)),
        weight: user_info
            .weight
            .as_ref()
            .map(|it| builder.create_string(it)),
        height: user_info
            .height
            .as_ref()
            .map(|it| builder.create_string(it)),
        country_code: user_info
            .country_code
            .as_ref()
            .map(|it| builder.create_string(it)),
        privacy_country_code: user_info
            .privacy_country_code
            .as_ref()
            .map(|it| builder.create_string(it)),
        signature: user_info
            .signature
            .as_ref()
            .map(|it| builder.create_string(it)),
        reg_client_id: user_info
            .reg_client_id
            .as_ref()
            .map(|it| builder.create_string(it)),
    };
    FBSUserInfo::create(builder, &fbs_user_info_args)
}

fn create_fbs_device<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    device: &Device,
) -> WIPOffset<FBSDevice<'a>> {
    let device_id = builder.create_string(&device.device_info.device_id);
    let device_name = builder.create_string(&device.device_info.device_name);
    let device_type = create_fbs_option_string(builder, &device.device_info.device_type);
    let family_id = builder.create_string(&device.device_info.family_id);
    let owner_id = builder.create_string(&device.device_info.owner_id);
    let permission = create_fbs_permission(builder, &device.device_info.permission);
    let wifi_type = create_fbs_option_string(builder, &device.device_info.wifi_type);
    let device_net_type = create_fbs_option_string(builder, &device.device_info.device_net_type);
    let bind_time = builder.create_string(&device.device_info.bind_time);
    let owner_info = create_fbs_owner_info(builder, &device.device_info.owner_info);
    let dev_name = builder.create_string(&device.device_info.dev_name);
    let sub_device_ids = create_strings(builder, &device.device_info.sub_device_ids);
    let parent_id = create_fbs_option_string(builder, &device.device_info.parent_id);
    let device_role = create_fbs_option_string(builder, &device.device_info.device_role);
    let device_role_type = create_fbs_option_string(builder, &device.device_info.device_role_type);
    let apptype_name = builder.create_string(&device.device_info.apptype_name);
    let apptype_code = builder.create_string(&device.device_info.apptype_code);
    let category_grouping = builder.create_string(&device.device_info.category_grouping);
    let barcode = create_fbs_option_string(builder, &device.device_info.barcode);
    let bind_type = create_fbs_option_string(builder, &device.device_info.bind_type);
    let brand = builder.create_string(&device.device_info.brand);
    let image_addr1 = builder.create_string(&device.device_info.image_addr1);
    let card_page_img = builder.create_string(&device.device_info.card_page_img);
    let model = builder.create_string(&device.device_info.model);
    let prod_no = builder.create_string(&device.device_info.prod_no);
    let room_name = builder.create_string(&device.device_info.room_name);
    let room_id = builder.create_string(&device.device_info.room_id);
    let access_type = create_fbs_option_string(builder, &device.device_info.access_type);
    let config_type = builder.create_string(&device.device_info.config_type);
    let communication_mode =
        create_fbs_option_string(builder, &device.device_info.communication_mode);
    let device_floor_id = builder.create_string(&device.device_info.device_floor_id);
    let device_floor_order_id = builder.create_string(&device.device_info.device_floor_order_id);
    let device_floor_name = builder.create_string(&device.device_info.device_floor_name);
    let apptype_icon = builder.create_string(&device.device_info.apptype_icon);
    let device_group_id = create_fbs_option_string(builder, &device.device_info.device_group_id);
    let device_group_type =
        create_fbs_option_string(builder, &device.device_info.device_group_type);
    let two_groping_name = builder.create_string(&device.device_info.two_groping_name);
    let aggregation_parent_id = builder.create_string(&device.device_info.aggregation_parent_id);
    let device_aggregate_type = builder.create_string(&device.device_info.device_aggregate_type);

    // 创建 ShareDeviceCardInfo 数组
    let share_device_card_info_vec = device
        .device_info
        .share_device_card_info
        .iter()
        .map(|card_info| {
            let family_id = builder.create_string(&card_info.family_id);
            FBSShareDeviceCardInfo::create(
                builder,
                &FBSShareDeviceCardInfoArgs {
                    family_id: Some(family_id),
                    card_sort: card_info.card_sort,
                    card_status: card_info.card_status,
                },
            )
        })
        .collect::<Vec<_>>();
    let share_device_card_info = builder.create_vector(&share_device_card_info_vec);
    let fbs_device_info = FBSDeviceInfo::create(
        builder,
        &FBSDeviceInfoArgs {
            device_id: Some(device_id),
            device_name: Some(device_name),
            device_type,
            family_id: Some(family_id),
            owner_id: Some(owner_id),
            permission: Some(permission),
            wifi_type,
            device_net_type,
            bind_time: Some(bind_time),
            is_online: device.device_info.is_online,
            owner_info: Some(owner_info),
            dev_name: Some(dev_name),
            sub_device_ids: Some(sub_device_ids),
            parent_id,
            device_role,
            device_role_type,
            apptype_name: Some(apptype_name),
            apptype_code: Some(apptype_code),
            category_grouping: Some(category_grouping),
            barcode,
            bind_type,
            brand: Some(brand),
            image_addr1: Some(image_addr1),
            card_page_img: Some(card_page_img),
            card_sort: device.device_info.card_sort,
            card_status: device.device_info.card_status,
            aggregation_parent_id: Some(aggregation_parent_id),
            support_aggregation_flag: device.device_info.support_aggregation_flag,
            device_aggregate_type: Some(device_aggregate_type),
            model: Some(model),
            prod_no: Some(prod_no),
            room_name: Some(room_name),
            room_id: Some(room_id),
            access_type,
            config_type: Some(config_type),
            communication_mode,
            device_floor_id: Some(device_floor_id),
            device_floor_order_id: Some(device_floor_order_id),
            device_floor_name: Some(device_floor_name),
            apptype_icon: Some(apptype_icon),
            device_group_id,
            device_group_type,
            no_keep_alive: device.device_info.no_keep_alive,
            two_groping_name: Some(two_groping_name),
            support_flag: device.device_info.support_flag,
            shared_device_flag: device.device_info.shared_device_flag,
            share_device_card_info: Some(share_device_card_info),
            attachment_sort_code: device.device_info.attachment_sort_code,
            device_share_support_flag: device.device_info.device_share_support_flag,
            rebind: device.device_info.rebind,
        },
    );
    FBSDevice::create(
        builder,
        &FBSDeviceArgs {
            device_info: Some(fbs_device_info),
        },
    )
}

fn create_fbs_permission<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    permission: &DevicePermission,
) -> WIPOffset<FBSDevicePermission<'a>> {
    let auth_type = builder.create_string(&permission.auth_type);
    let auth = FBSDeviceAuth::create(
        builder,
        &FBSDeviceAuthArgs {
            control: permission.auth.control,
            set: permission.auth.set,
            view: permission.auth.view,
        },
    );
    FBSDevicePermission::create(
        builder,
        &FBSDevicePermissionArgs {
            auth: Some(auth),
            auth_type: Some(auth_type),
        },
    )
}

fn create_fbs_owner_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    owner_info: &DeviceOwnerInfo,
) -> WIPOffset<FBSDeviceOwnerInfo<'a>> {
    let user_id = builder.create_string(&owner_info.user_id);
    let mobile = builder.create_string(&owner_info.mobile);
    let user_nick_name = builder.create_string(&owner_info.user_nick_name);
    let uc_user_id = builder.create_string(&owner_info.uc_user_id);
    FBSDeviceOwnerInfo::create(
        builder,
        &FBSDeviceOwnerInfoArgs {
            user_id: Some(user_id),
            mobile: Some(mobile),
            user_nick_name: Some(user_nick_name),
            uc_user_id: Some(uc_user_id),
        },
    )
}

fn create_fbs_option_string<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    value: &Option<String>,
) -> Option<WIPOffset<&'a str>> {
    value.clone().map(|it| builder.create_string(&it))
}

fn create_strings<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    values: &Vec<String>,
) -> WIPOffset<Vector<'a, ForwardsUOffset<&'a str>>> {
    let mut offsets = Vec::new();
    for value in values {
        offsets.push(builder.create_string(value));
    }
    builder.create_vector(&offsets)
}

fn create_fbs_family<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    family: &Family,
) -> WIPOffset<FBSFamily<'a>> {
    let fbs_family_args = FBSFamilyArgs {
        family_info: Some(create_fbs_family_info(builder, &family.family_info)),
    };
    FBSFamily::create(builder, &fbs_family_args)
}

fn create_fbs_family_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    family_info: &FamilyInfo,
) -> WIPOffset<FBSFamilyInfo<'a>> {
    let mut fbs_members = Vec::<WIPOffset<FBSFamilyMemberInfo>>::new();
    if let Some(members) = &family_info.members {
        for member in members {
            let m = create_fbs_family_member_info(builder, member);
            fbs_members.push(m);
        }
    }
    let mut fbs_floor_infos = Vec::<WIPOffset<FBSFloorInfo>>::new();
    if let Some(floor_infos) = &family_info.floor_infos {
        for floor_info in floor_infos {
            let f = create_fbs_floor_info(builder, floor_info);
            fbs_floor_infos.push(f);
        }
    }
    let fbs_family_info_args = FBSFamilyInfoArgs {
        family_id: Some(builder.create_string(&family_info.family_id)),
        family_name: Some(builder.create_string(&family_info.family_name)),
        family_position: family_info
            .family_position
            .as_ref()
            .map(|it| builder.create_string(it)),
        create_time: Some(builder.create_string(&family_info.create_time)),
        app_id: Some(builder.create_string(&family_info.app_id)),
        members: Some(builder.create_vector(&fbs_members)),
        first_member: Some(create_fbs_member_info(
            builder,
            Some(&family_info.first_member),
        )),
        owner_id: Some(builder.create_string(&family_info.owner_id)),
        owner: Some(create_fbs_member_info(builder, family_info.owner.as_ref())),
        floor_infos: Some(builder.create_vector(&fbs_floor_infos)),
        is_default_family: family_info.is_default_family,
        location_change_flag: family_info.location_change_flag,
        total_device_count: Some(builder.create_string(&family_info.total_device_count)),
        family_location: Some(create_fbs_family_location(
            builder,
            &family_info.family_location,
        )),
    };
    FBSFamilyInfo::create(builder, &fbs_family_info_args)
}

fn create_fbs_member_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    member: Option<&MemberInfo>,
) -> WIPOffset<FBSMemberInfo<'a>> {
    if member.is_none() {
        return FBSMemberInfo::create(builder, &FBSMemberInfoArgs::default());
    }
    let member_info = member.unwrap();
    let fbs_member_info_args = FBSMemberInfoArgs {
        iot_user_id: Some(builder.create_string(&member_info.iot_user_id)),
        name: member_info
            .name
            .as_ref()
            .map(|it| builder.create_string(it)),
        user_avatar: member_info
            .user_avatar
            .as_ref()
            .map(|it| builder.create_string(it)),
        user_mobile: member_info
            .user_mobile
            .as_ref()
            .map(|it| builder.create_string(it)),
        uc_user_id: member_info
            .uc_user_id
            .as_ref()
            .map(|it| builder.create_string(it)),
        host_user_id: member_info
            .host_user_id
            .as_ref()
            .map(|it| builder.create_string(it)),
        user_birthday: member_info
            .user_birthday
            .as_ref()
            .map(|it| builder.create_string(it)),
        is_virtual_member: member_info.is_virtual_member,
    };
    FBSMemberInfo::create(builder, &fbs_member_info_args)
}

fn create_fbs_family_member_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    member: &FamilyMemberInfo,
) -> WIPOffset<FBSFamilyMemberInfo<'a>> {
    let fbs_family_member_info_args = FBSFamilyMemberInfoArgs {
        family_id: Some(builder.create_string(&member.family_id)),
        join_time: Some(builder.create_string(&member.join_time)),
        member_name: Some(builder.create_string(&member.member_name)),
        member_role: member
            .member_role
            .as_ref()
            .map(|it| builder.create_string(it)),
        share_device_count: member.share_device_count,
        member_info: Some(create_fbs_member_info(builder, Some(&member.member_info))),
        member_type: member.member_type,
    };
    FBSFamilyMemberInfo::create(builder, &fbs_family_member_info_args)
}

fn create_fbs_family_location<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    location: &Location,
) -> WIPOffset<FBSLocation<'a>> {
    let fbs_location_args = FBSLocationArgs {
        longitude: location.longitude.unwrap_or(0.0),
        latitude: location.latitude.unwrap_or(0.0),
        city_code: location
            .city_code
            .as_ref()
            .map(|it| builder.create_string(it)),
    };
    FBSLocation::create(builder, &fbs_location_args)
}

fn create_fbs_floor_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    floor_info: &FloorInfo,
) -> WIPOffset<FBSFloorInfo<'a>> {
    let mut fbs_rooms = Vec::<WIPOffset<FBSRoom<'a>>>::new();
    for room in &floor_info.floor_rooms {
        let a = create_fbs_room(builder, room);
        fbs_rooms.push(a);
    }
    let fbs_floor_info_args = FBSFloorInfoArgs {
        floor_id: Some(builder.create_string(&floor_info.floor_id)),
        floor_name: Some(builder.create_string(&floor_info.floor_name)),
        floor_class: Some(builder.create_string(&floor_info.floor_class)),
        floor_label: floor_info
            .floor_label
            .as_ref()
            .map(|it| builder.create_string(it)),
        floor_logo: floor_info
            .floor_logo
            .as_ref()
            .map(|it| builder.create_string(it)),
        floor_picture: floor_info
            .floor_picture
            .as_ref()
            .map(|it| builder.create_string(it)),
        floor_order_id: Some(builder.create_string(&floor_info.floor_order_id)),
        floor_rooms: Some(builder.create_vector(&fbs_rooms)),
        floor_create_time: floor_info
            .floor_create_time
            .as_ref()
            .map(|it| builder.create_string(it)),
    };
    FBSFloorInfo::create(builder, &fbs_floor_info_args)
}

fn create_fbs_room<'a>(builder: &mut FlatBufferBuilder<'a>, room: &Room) -> WIPOffset<FBSRoom<'a>> {
    let fbs_room_args = FBSRoomArgs {
        room_id: Some(builder.create_string(&room.room_id)),
        room_name: Some(builder.create_string(&room.room_name)),
        room_class: room.room_class.as_ref().map(|it| builder.create_string(it)),
        room_label: room.room_label.as_ref().map(|it| builder.create_string(it)),
        room_logo: room.room_logo.as_ref().map(|it| builder.create_string(it)),
        room_picture: room
            .room_picture
            .as_ref()
            .map(|it| builder.create_string(it)),
        sort_code: room.sort_code.as_ref().map(|it| builder.create_string(it)),
        floor_order_id: room
            .floor_order_id
            .as_ref()
            .map(|it| builder.create_string(it)),
        floor_id: room.floor_id.as_ref().map(|it| builder.create_string(it)),
    };
    FBSRoom::create(builder, &fbs_room_args)
}

fn create_fbs_address_info<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    address_info: &AddressInfo,
) -> WIPOffset<FBSAddressInfo<'a>> {
    let fbs_address_info_args = FBSAddressInfoArgs {
        address: Some(create_fbs_address(builder, &address_info.address)),
        email: create_fbs_option_string(builder, &address_info.email),
        address_id: Some(builder.create_string(&address_info.address_id)),
        is_default: address_info.is_default,
        is_service: address_info.is_service,
        receiver_mobile: Some(builder.create_string(&address_info.receiver_mobile)),
        receiver_name: Some(builder.create_string(&address_info.receiver_name)),
        source: Some(builder.create_string(&address_info.source)),
        tag: create_fbs_option_string(builder, &address_info.tag),
        user_id: Some(builder.create_string(&address_info.user_id)),
    };
    FBSAddressInfo::create(builder, &fbs_address_info_args)
}

fn create_fbs_familys_map<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    map: &HashMap<String, Family>,
) -> WIPOffset<FBSFamilyMap<'a>> {
    let mut fbs_map = Vec::<WIPOffset<FBSFamilyEntry>>::new();
    for (key, value) in map {
        let fbs_map_entry_args = FBSFamilyEntryArgs {
            key: Some(builder.create_string(key)),
            value: Some(create_fbs_family(builder, value)),
        };
        fbs_map.push(FBSFamilyEntry::create(builder, &fbs_map_entry_args));
    }
    let fbs_map_args = FBSFamilyMapArgs {
        entries: Some(builder.create_vector(&fbs_map)),
    };
    FBSFamilyMap::create(builder, &fbs_map_args)
}

fn create_fbs_devices_map<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    map: &HashMap<String, Device>,
) -> WIPOffset<FBSDeviceMap<'a>> {
    let mut fbs_map = Vec::<WIPOffset<FBSDeviceEntry>>::new();
    for (key, value) in map {
        let fbs_map_entry_args = FBSDeviceEntryArgs {
            key: Some(builder.create_string(key)),
            value: Some(create_fbs_device(builder, value)),
        };
        fbs_map.push(FBSDeviceEntry::create(builder, &fbs_map_entry_args));
    }
    let fbs_map_args = FBSDeviceMapArgs {
        entries: Some(builder.create_vector(&fbs_map)),
    };
    FBSDeviceMap::create(builder, &fbs_map_args)
}

fn create_fbs_address<'a>(
    builder: &mut FlatBufferBuilder<'a>,
    address: &Address,
) -> WIPOffset<FBSAddress<'a>> {
    let fbs_address_args = FBSAddressArgs {
        city: Some(builder.create_string(&address.city)),
        city_id: Some(builder.create_string(&address.city_id)),
        country_code: create_fbs_option_string(builder, &address.country_code),
        district: Some(builder.create_string(&address.district)),
        district_id: Some(builder.create_string(&address.district_id)),
        line1: Some(builder.create_string(&address.line1)),
        line2: create_fbs_option_string(builder, &address.line2),
        postcode: create_fbs_option_string(builder, &address.postcode),
        province: Some(builder.create_string(&address.province)),
        province_id: Some(builder.create_string(&address.province_id)),
        town: create_fbs_option_string(builder, &address.town),
        town_id: create_fbs_option_string(builder, &address.town_id),
    };
    FBSAddress::create(builder, &fbs_address_args)
}

fn int_to_app_network_env(value: i32) -> AppNetworkEnv {
    match value {
        0 => AppNetworkEnv::Development,
        1 => AppNetworkEnv::Acceptance,
        _ => AppNetworkEnv::Production,
    }
}

fn invalid_arg_result(error_message: &str) -> Vec<u8> {
    failure_result(error_message, PARAMS_ERROR)
}

fn failure_result(error_message: &str, error_code: &str) -> Vec<u8> {
    error!(
        "userdomain ffi errormessage:{},error_code:{}",
        error_message, error_code
    );
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let error_message = builder.create_string(error_message);
    let error_code = builder.create_string(error_code);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let userdomain_result = UserDomainFlat::create(
        &mut builder,
        &UserDomainFlatArgs {
            container_type: UserdomainContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(error_code),
            error: Some(error_message),
        },
    );
    builder.finish(userdomain_result, None);
    builder.finished_data().to_vec()
}

fn bool_result(value: bool) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let bool_result = BoolWrapper::create(&mut builder, &BoolWrapperArgs { value });
    data_result(
        &mut builder,
        bool_result.as_union_value(),
        UserdomainContainer::BoolWrapper,
    )
}

fn i32_result(value: i32) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let state = Int32Wrapper::create(&mut builder, &Int32WrapperArgs { value });
    data_result(
        &mut builder,
        state.as_union_value(),
        UserdomainContainer::Int32Wrapper,
    )
}

fn string_result(result: String) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let value = builder.create_string(&result);
    let string_result = StrWrapper::create(&mut builder, &StrWrapperArgs { value: Some(value) });
    data_result(
        &mut builder,
        string_result.as_union_value(),
        UserdomainContainer::StrWrapper,
    )
}

fn data_result(
    builder: &mut FlatBufferBuilder,
    data: WIPOffset<UnionWIPOffset>,
    container_type: UserdomainContainer,
) -> Vec<u8> {
    let code = builder.create_string(SUCCESS_CODE);
    let userdomain_result = UserDomainFlat::create(
        builder,
        &UserDomainFlatArgs {
            container_type,
            container: Some(data),
            code: Some(code),
            error: None,
        },
    );
    builder.finish(userdomain_result, None);
    builder.finished_data().to_vec()
}

fn success_result() -> Vec<u8> {
    debug!("Operation successful");
    let mut builder = FlatBufferBuilder::with_capacity(DEFAULT_SIZE);
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});
    let code = builder.create_string(SUCCESS_CODE);
    let userdomain_result = UserDomainFlat::create(
        &mut builder,
        &UserDomainFlatArgs {
            container_type: UserdomainContainer::NoneWrapper,
            container: Some(none_wrapper.as_union_value()),
            code: Some(code),
            error: None,
        },
    );
    builder.finish(userdomain_result, None);
    builder.finished_data().to_vec()
}

fn fetch_family_by_id(family_id: &str) -> Option<Family> {
    UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .get_family_by_id(family_id)
}

/**
 * 获取当前用户在指定家庭中的成员类型
 * 0-创建者，1-管理员，2-成员
 * @param params 参数，包含family_id
 * @return 成员类型，默认为2（普通成员）
 */
fn get_member_type(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }

    let family_id = params.get("family_id").unwrap_or(&EMPTY);
    let option_family = fetch_family_by_id(family_id);

    match option_family {
        Some(family) => {
            let member_type = family.get_member_type();
            i32_result(member_type)
        }
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

/**
 * 获取当前用户加入指定家庭的时间
 * @param params 参数，包含family_id
 * @return 加入时间，默认为空字符串
 */
fn get_join_time(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");
    if UserDomainManager::get_instance().get_user_domain().state() == UserDomainState::UnLogin {
        return failure_result("user not login", UNLOGING_CODE);
    }

    let family_id = params.get("family_id").unwrap_or(&EMPTY);
    let option_family = fetch_family_by_id(family_id);

    match option_family {
        Some(family) => {
            let join_time = family.get_join_time();
            string_result(join_time)
        }
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}

fn get_error_code_and_message(err: &UserDomainError) -> (&str, String) {
    match err {
        UserDomainError::IllegalParameters(_) => {
            (PARAMS_ERROR, format!("IllegalParameters: {}", err))
        }
        UserDomainError::UserTaskJoinError(_) => ("101", format!("UserTaskJoinError: {}", err)),
        UserDomainError::UserTaskFaild(_) => ("102", format!("UserTaskFaild: {}", err)),
        UserDomainError::HttpTokenIvalidate(_) => ("103", format!("HttpTokenIvalidate: {}", err)),
        UserDomainError::HttpRequstFaild(e) => match e {
            RequestError::ResponseError(api_response) => (
                api_response.ret_code.as_str(),
                api_response.ret_info.clone(),
            ),
            _ => ("104", format!("HttpTokenIvalidate: {}", err)),
        },
        UserDomainError::RefreshUserFaild => ("105", format!("RefreshUserFaild: {}", err)),
        UserDomainError::ParametersError(_) => (PARAMS_ERROR, format!("ParametersError: {}", err)),
        UserDomainError::UserStateUnLogin => (UNLOGING_CODE, format!("UserStateUnLogin: {}", err)),
    }
}

async fn modify_device_card_status(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id");

    let family_id = params.get("family_id").unwrap();

    // 使用辅助函数获取家庭
    let family = fetch_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    }

    // 创建参数对象
    let mut args = crate::models::device_card_args::DeviceCardStatusArgs {
        family_id: family_id.clone(),
        card_order_list: None,
        big_card_list: None,
        middle_card_list: None,
        small_card_list: None,
    };

    // 解析各个列表参数
    if let Some(order_list_str) = params.get("order_list") {
        if let Ok(list) = serde_json::from_str::<Vec<String>>(order_list_str) {
            args.card_order_list = Some(list);
        }
    }

    if let Some(big_card_list_str) = params.get("big_card_list") {
        if let Ok(list) = serde_json::from_str::<Vec<String>>(big_card_list_str) {
            args.big_card_list = Some(list);
        }
    }

    if let Some(middle_card_list_str) = params.get("middle_card_list") {
        if let Ok(list) = serde_json::from_str::<Vec<String>>(middle_card_list_str) {
            args.middle_card_list = Some(list);
        }
    }

    if let Some(small_card_list_str) = params.get("small_card_list") {
        if let Ok(list) = serde_json::from_str::<Vec<String>>(small_card_list_str) {
            args.small_card_list = Some(list);
        }
    }

    match family.unwrap().modify_device_card_status(args).await {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("modify_device_card_status error:{}", msg), code)
        }
    }
}

async fn modify_device_aggregation(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "agg_cards");

    let family_id = params.get("family_id").unwrap();

    // 使用辅助函数获取家庭
    let family = fetch_family_by_id(family_id);
    if family.is_none() {
        return failure_result("family not found", FAMILY_NOT_FOUND);
    }

    // 解析 agg_cards 参数
    let agg_cards_str = params.get("agg_cards").unwrap();
    let agg_card_items: Result<Vec<AggCardItemArgs>, _> = serde_json::from_str(agg_cards_str);

    if agg_card_items.is_err() {
        return failure_result(
            &format!(
                "Invalid agg_cards format: {}",
                agg_card_items.err().unwrap()
            ),
            PARAMS_ERROR,
        );
    }

    let agg_card_items = agg_card_items.unwrap();

    // 过滤掉无效的项
    let agg_card_items: Vec<_> = agg_card_items
        .into_iter()
        .filter(|item| !item.agg_type.is_empty())
        .collect();

    if agg_card_items.is_empty() {
        return failure_result("No valid aggregation items", PARAMS_ERROR);
    }

    // 使用结构化参数调用方法
    match family
        .unwrap()
        .modify_device_aggregation(agg_card_items)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("modify_device_aggregation error:{}", msg), code)
        }
    }
}

async fn modify_aggregation_switch(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "source", "family_agg");

    let source = params.get("source").unwrap().to_string();

    // 解析 family_agg 参数
    let family_agg_str = params.get("family_agg").unwrap();
    let family_agg_items: Result<Vec<FamilyAggItemArgs>, _> = serde_json::from_str(family_agg_str);

    if family_agg_items.is_err() {
        return failure_result(
            &format!(
                "Invalid family_agg format: {}",
                family_agg_items.err().unwrap()
            ),
            PARAMS_ERROR,
        );
    }

    let family_agg_items = family_agg_items.unwrap();

    // 过滤掉无效的项
    let family_agg_items: Vec<_> = family_agg_items
        .into_iter()
        .filter(|item| !item.family_id.is_empty())
        .collect();

    if family_agg_items.is_empty() {
        return failure_result("No valid family aggregation items", PARAMS_ERROR);
    }

    let args = AggregationSwitchArgs {
        source,
        family_agg: family_agg_items,
    };

    // 调用方法
    match UserDomainManager::get_instance()
        .get_user_domain()
        .get_user()
        .modify_aggregation_switch(args)
        .await
    {
        Ok(_) => success_result(),
        Err(e) => {
            let (code, msg) = get_error_code_and_message(&e);
            failure_result(&format!("modify_aggregation_switch error:{}", msg), code)
        }
    }
}

/**
 * 修改家庭成员类型（异步版本）
 * @param params 参数，包含family_id, member_id, member_type
 * @return 操作结果
 */
async fn modify_member_type(params: HashMap<String, String>) -> Vec<u8> {
    require_params!(params, "family_id", "member_id", "member_type");

    let family_id = params.get("family_id").unwrap_or(&EMPTY);
    let member_id = params.get("member_id").unwrap_or(&EMPTY);
    let member_type_str = params.get("member_type").unwrap_or(&EMPTY);

    // 解析成员类型
    let member_type = match member_type_str.parse::<i32>() {
        Ok(mt) => mt,
        Err(_) => {
            return failure_result("invalid member_type", PARAMS_ERROR);
        }
    };

    let option_family = fetch_family_by_id(family_id);

    match option_family {
        Some(family) => {
            match family
                .modify_member_type(member_id.to_string(), member_type)
                .await
            {
                Ok(_) => success_result(),
                Err(e) => {
                    let (code, msg) = get_error_code_and_message(&e);
                    failure_result(&msg, code)
                }
            }
        }
        None => failure_result("family not found", FAMILY_NOT_FOUND),
    }
}
