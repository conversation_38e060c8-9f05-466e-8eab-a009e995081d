use std::{fs, io};
use reqwest::Certificate;

pub fn load_cert_chain(cert_paths: Vec<String>) -> io::Result<Vec<Certificate>> {
    cert_paths
        .iter()
        .map(|path| {
            let pem_data = fs::read(path)?;
            Certificate::from_pem(&pem_data)
                .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))
        })
        .collect()
}

pub fn get_ohos_cert_paths() -> Vec<String> {
    let dir_path = "/system/etc/security/certificates/";
    let mut cert_paths = vec![];
    if let Ok(entries) = fs::read_dir(dir_path) {
        cert_paths = entries
            .filter(|it| it.is_ok())
            .map(|it| it.unwrap().path())
            .filter(|it| it.extension().is_some())
            .map(|it| it.to_str().unwrap().to_string())
            .collect();
    }
    cert_paths
}