Feature: Family.class
  功能范围:
  通过组件家庭相关接口，使用者可实现邀请新成员、成员管理、退出/解散家庭、刷新家庭详细信息、房间管理、家庭设备管理等功能。
  - 邀请新成员加入家庭，通过手机号邀请新用户加入家庭。
  - 成员管理功能，通过目标成员ID完成移除家庭成员、更换家庭管理员操作。
  - 退出/解散家庭，家庭成员可以退出家庭，家庭管理员可以解散某一个家庭。
  - 刷新家庭详细信息，家庭管理员可以更新家庭的名称、位置(经度、纬度、城市代码)等信息。
  - 房间管理功能，为某个家庭添加或移除房间，还可以更改某个房间的名称。
  - 家庭设备管理功能，可将家庭中的设备进行解绑，将家庭中的设备从一个房间移动到另一个房间，还可以将设备从一个家庭移动到另一个家庭中，而且，还可以把设备从家庭中移除。

  外部依赖：
  - 家庭数据源
  - 缓存数据代理
  - 事件处理器代理

  接口说明:
  1、获取家庭基本信息对象
  家庭基本信息对象包括家庭名称、家庭ID、创建时间、家庭位置、家庭成员列表等家庭基本信息。
  2、获取家庭ID
  家庭的唯一标识
  3、获取家庭成员列表
  列表中的元素为家庭中的成员基本信息对象。
  4、获取家庭所有者信息对象
  5、获取家庭房间列表
  列表中的元素为家庭中的房间信息对象。
  6、获取家庭下的设备列表
  列表中的元素为家庭中的设备信息对象。
  7、获取分享到家庭的设备列表
  该列表中的设备，均为用户分享到家庭的设备。
  8、刷新家庭详细信息
  判断是否正在刷新当前家庭信息，
  如果正在刷新则将回调添加到回调列表中，等正在执行的刷新完成后统一返回结果，
  如果没有刷新则执行刷新家庭信息请求，
  成功后，更新内存和缓存中的家庭信息，回调成功结果，回调结果中返回请求的家庭信息
  失败后，回调失败结果。
  9、添加房间
  对其参数进行校验，校验不通过，回调添加房间失败，
  校验通过，判断添加房间结果，
  若成功后，触发刷新房间列表事件，回调成功结果，
  若失败，回调失败结果。
  10、删除房间
  对其参数进行校验，校验不通过，回调删除房间失败，
  校验通过，判断删除房间结果，若失败，回调失败结果，
  若成功后，删除内存中的房间，然后删除缓存房间、通知家庭列表更新成功，回调成功结果。
  11、编辑房间名称
  对其参数进行校验，校验不通过，回调编辑房间失败，
  校验通过，判断编辑房间结果，若失败，回调失败结果，
  若成功后，修改内存中的房间名称，然后缓存房间名称、通知家庭列表更新成功，回调成功结果。
  12、家庭管理员邀请家庭成员
  对其参数进行校验，校验不通过，回调邀请成员失败，
  校验通过，回调邀请成员结果。
  13、家庭管理员删除家庭成员
  对其参数进行校验，校验不通过，回调删除成员失败，
  校验通过，判断删除成员结果，若失败，回调失败结果，
  若成功后，触发刷新家庭列表事件，触发刷新设备列表事件，回调成功结果。
  14、家庭管理员批量解绑设备
  对其参数进行校验，校验不通过，回调解绑设备失败，
  校验通过，判断解绑设备结果，若失败，回调失败结果，
  若成功后，删除内存中的设备，然后删除缓存设备、通知设备列表更新成功，回调成功结果。
  15、批量移动设备到新的房间
  对其参数进行校验，校验不通过，回调移动设备失败，
  校验通过，判断移动设备结果，若失败，回调失败结果，
  若成功后，触发刷新设备列表事件，回调成功结果。
  16、家庭批量移除设备
  对其参数进行校验，校验不通过，回调移除设备失败，
  校验通过，判断移除设备结果，若失败，回调失败结果，
  若成功后，删除内存中的设备，然后删除缓存设备、通知设备列表更新成功，回调成功结果。
  17、批量移动设备到新的家庭
  对其参数进行校验，校验不通过，回调移动设备失败，
  校验通过，判断移动设备结果，若失败，回调失败结果，
  若成功后，触发刷新设备列表事件，回调成功结果。
  18、管理员退出家庭
  对其参数进行校验，校验不通过，回调管理员退出家庭失败，
  校验通过，判断管理员退出家庭结果，若失败，回调失败结果，
  若成功后，删除内存和缓存中与家庭相关的家庭信息、设备列表信息，触发用户的设备列表刷新和家庭列表刷新，回调成功结果。
  19、家庭成员退出家庭
  对其参数进行校验，校验不通过，回调成员退出家庭失败，
  校验通过，判断成员退出家庭结果，若失败，回调失败结果，
  若成功后，触发刷新家庭列表事件，触发刷新设备列表事件，回调成功结果。
  20、家庭管理员删除家庭
  对其参数进行校验，校验不通过，回调删除家庭失败，
  校验通过，判断删除家庭结果，若失败，回调失败结果，
  若成功后，删除内存和缓存中与家庭相关的家庭信息、设备列表信息，触发刷新家庭列表事件，触发刷新设备列表事件，回调成功结果。
  21、更换家庭管理员
  对其参数进行校验，校验不通过，回调更换家庭管理员失败，
  校验通过，判断更换家庭管理员结果，若失败，回调失败结果，
  若成功后，触发刷新家庭详情事件，回调成功结果。
  22、编辑家庭信息
  家庭管理员可以编辑某一个家庭的名称、经纬度和城市代码信息。
  对其参数进行校验，校验不通过，回调失败结果，
  校验通过，判断编辑家庭信息结果，若失败，回调失败结果，
  若成功后，更新内存和缓存中的家庭信息，通知家庭列表更新成功，回调成功结果。
  23、刷新房间列表
  判断是否立即从家庭数据源获取最新数据，
  需要立即获取或者缓存中没有数据，则从家庭数据源获取楼层ID对应的房间列表，
  如果不需要立即获取数据并且缓存中有房间列表，直接回调成功结果。
  24、创建楼层
  对其参数进行校验，校验不通过，回调创建楼层失败，
  校验通过，判断创建楼层结果，若失败，回调失败结果，
  若成功后，触发刷新家庭列表事件，回调成功结果。
  25、删除楼层
  对其参数进行校验，校验不通过，回调删除楼层失败，
  校验通过，判断删除楼层结果，若失败，回调失败结果，
  若成功后，删除内存中的楼层，然后删除缓存楼层、通知家庭列表更新成功，回调成功结果。
  26、编辑楼层
  对其参数进行校验，校验不通过，回调编辑房间失败，
  校验通过，判断编辑房间结果，若失败，回调失败结果，
  若成功后，修改内存中的楼层，然后修改缓存楼层、通知家庭列表更新成功，回调成功结果。
  27、查询第一个加入家庭的成员信息
  查询失败，回调失败结果，
  查询成功，返回成员信息，回调成功结果。
  28、虚拟成员加入家庭
  对其参数进行校验，校验不通过，回调成员加入家庭失败，
  校验通过，判断虚拟成员加入家庭结果，如果失败，回调失败结果，
  若成功后，触发刷新家庭详情事件，回调成功结果。
  29、编辑虚拟角色信息
  对其参数进行校验，校验不通过，回调编辑虚拟角色失败，
  校验通过，判断编辑虚拟角色结果，若失败，回调失败结果，
  若成功后，更新内存中对应角色的头像地址，昵称和生日值，回调成功结果。
  30、编辑家庭成员身份
  对其参数进行校验，校验不通过，回调编辑家庭成员身份失败，
  校验通过，判断编辑家庭成员身份结果，若失败，回调失败结果，
  若成功后，更新内存中对应成员的身份，回调成功结果。
  31、编辑家庭虚拟成员身份
  对其参数进行校验，校验不通过，回调编辑家庭成员身份失败，
  校验通过，判断编辑家庭成员身份结果，若失败，回调失败结果，
  若成功后，更新内存中对应虚拟成员的身份，回调成功结果
  32、保存房间顺序
  对其参数进行校验，校验不通过，回调保存房间顺序失败，
  校验通过，判断保存房间顺序结果，若失败，回调失败结果，
  若成功后，更新内存中对应家庭房间顺序，回调成功结果。

  Background: 组件初始化
    Given 缓存数据代理使用"模拟的"
    Given 时间代理使用"模拟的"
    Given 缓存数据的用户鉴权信息数据如下:
      | accessToken       | refreshToken       | uhomeAccessToken        | expiresIn | scope | tokenType       | uhomeUserId        | createTime          | ucUserId        |
      | fake_access_token | fake_refresh_token | fake_uhome_access_token | 857662    |       | fake_token_type | fake_uhome_user_id | 2020-07-20 15:27:47 | fake_uc_user_id |
    Given 用户数据源代理使用"模拟的"
    Given 家庭数据源代理使用"模拟的"
    Given 设备数据源代理使用"模拟的"
    Given 缓存数据代理存储接口执行结果为"成功"
    Given 缓存数据代理的地址列表数据如下:
      | addressId        | email       | is_default | is_service | receiverName        | receiverMobile        | source      | tag       | user_id       | addressObject                                                                                                                                                                                                                                                                 |
      | fake_address_id1 | fake_email1 | 1          | 1          | fake_receiver_name1 | fake_receiver_mobile1 | fake_source | fake_tag1 | fake_user_id1 | {"provinceId":"fake_province_id","province":"fake_province","cityId":"fake_city_id","districtId":"fake_district_id","city":"fake_city","district":"fake_district","countryCode":"fake_country_code","postcode":"fake_postcode1","town":"fake_town1","townId":"fake_town_id1"} |
      | fake_address_id2 | fake_email2 | 0          | 1          | fake_receiver_name2 | fake_receiver_mobile2 | fake_source | fake_tag2 | fake_user_id1 | {"provinceId":"fake_province_id","province":"fake_province","cityId":"fake_city_id","districtId":"fake_district_id","city":"fake_city","district":"fake_district","countryCode":"fake_country_code","postcode":"fake_postcode2","town":"fake_town2","townId":"fake_town_id2"} |
      | fake_address_id3 | fake_email3 | 0          | 1          | fake_receiver_name3 | fake_receiver_mobile3 | fake_source | fake_tag3 | fake_user_id2 | {"provinceId":"fake_province_id","province":"fake_province","cityId":"fake_city_id","districtId":"fake_district_id","city":"fake_city","district":"fake_district","countryCode":"fake_country_code","postcode":"fake_postcode3","town":"fake_town3","townId":"fake_town_id3"} |
    Given 缓存数据代理的用户信息如下:
      | userId        | username       | email       | givenName        | mobile       | nickname       | gender | marriage | birthday   | avatarUrl        | familyNum | education | extraPhone       | income | height | weight | countryCode       |
      | fake_user_id1 | fake_username1 | fake_email1 | fake_given_name1 | fake_mobile1 | fake_nickname1 | male   | 1        | 2020-07-16 | fake_avatar_url1 | 2         | 10        | fake_extra_phone | 18     | 170    | 70     | fake_country_code |
    Given 缓存数据代理的设备列表信息如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
      | fake_device_id3 | fake_device_name3 | fake_dev_name3 | fake_device_type3 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:43 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name3 | fake_room_id3 | {"roomId":"fake_room_id3","roomName":"fake_room_name3","roomClass":"fake_room_class3","roomLabel":"fake_room_label3","roomLogo":"fake_room_logo3","roomPicture":"fake_room_pic_3"} | 1       | 13         | 0          | fake_floor_id3 | fake_floor_order_id3 | fake_floor_name | fake_net_type |
      | fake_device_id4 | fake_device_name4 | fake_dev_name4 | fake_device_type4 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:44 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name4 | fake_room_id4 | {"roomId":"fake_room_id4","roomName":"fake_room_name4","roomClass":"fake_room_class4","roomLabel":"fake_room_label4","roomLogo":"fake_room_logo4","roomPicture":"fake_room_pic_4"} | 1       | 13         | 0          | fake_floor_id4 | fake_floor_order_id4 | fake_floor_name | fake_net_type |
      | fake_device_id5 | fake_device_name5 | fake_dev_name5 | fake_device_type5 | fake_family_id3 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:45 | 0        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name5 | fake_room_id5 | {"roomId":"fake_room_id5","roomName":"fake_room_name5","roomClass":"fake_room_class5","roomLabel":"fake_room_label5","roomLogo":"fake_room_logo5","roomPicture":"fake_room_pic_5"} | 1       | 13         | 0          | fake_floor_id5 | fake_floor_order_id5 | fake_floor_name | fake_net_type |
    Given 缓存数据代理的家庭列表信息如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Given 家庭"fake_family_id1"所关联的楼层信息如下:
      | floorId        | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       | floorCreateTime        | refRoomObjects                                                                                                                                                                                                                                                                                                                                                        |
      | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture | fake_floor_create_time | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"}#{"roomId":"fake_room_id6","roomName":"fake_room_name6","roomClass":"fake_room_class6","roomLabel":"fake_room_label6","roomLogo":"fake_room_logo6","roomPicture":"fake_room_pic_6"} |
      | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture | fake_floor_create_time | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"}                                                                                                                                                                                    |
    Given 家庭"fake_family_id1"的家庭成员信息如下:
      | familyId        | joinTime            | memberName     | memberInfoObject                                                                                                                   | shareDeviceCount | memberRole       |
      | fake_family_id1 | 2019-12-10 15:27:41 | fake_username1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1","birthday":"fake_birthday1"} | 3                | fake_memberRole1 |
      | fake_family_id1 | 2019-12-10 15:27:42 | fake_username2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2","birthday":"fake_birthday2"} | 0                | fake_memberRole2 |
    Given 组件已正常初始化
    Given 设置当前系统时间为"2020-07-20 15:30:47"

    Given 设备数据源的设备列表"mockDeviceList"数据如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
      | fake_device_id3 | fake_device_name3 | fake_dev_name3 | fake_device_type3 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:43 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name3 | fake_room_id3 | {"roomId":"fake_room_id3","roomName":"fake_room_name3","roomClass":"fake_room_class3","roomLabel":"fake_room_label3","roomLogo":"fake_room_logo3","roomPicture":"fake_room_pic_3"} | 1       | 13         | 0          | fake_floor_id3 | fake_floor_order_id3 | fake_floor_name | fake_net_type |
      | fake_device_id4 | fake_device_name4 | fake_dev_name4 | fake_device_type4 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:44 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name4 | fake_room_id4 | {"roomId":"fake_room_id4","roomName":"fake_room_name4","roomClass":"fake_room_class4","roomLabel":"fake_room_label4","roomLogo":"fake_room_logo4","roomPicture":"fake_room_pic_4"} | 1       | 13         | 0          | fake_floor_id4 | fake_floor_order_id4 | fake_floor_name | fake_net_type |
      | fake_device_id5 | fake_device_name5 | fake_dev_name5 | fake_device_type5 | fake_family_id3 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:45 | 0        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name5 | fake_room_id5 | {"roomId":"fake_room_id5","roomName":"fake_room_name5","roomClass":"fake_room_class5","roomLabel":"fake_room_label5","roomLogo":"fake_room_logo5","roomPicture":"fake_room_pic_5"} | 1       | 13         | 0          | fake_floor_id5 | fake_floor_order_id5 | fake_floor_name | fake_net_type |

    Given 家庭数据源的家庭列表"mockFamilyList"数据如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |

    Given 用户数据源的用户信息"mockUserInfo"数据如下:
      | userId        | username       | email       | givenName        | mobile       | nickname       | gender | marriage | birthday   | avatarUrl        | familyNum | education | extraPhone       | income | height | weight | countryCode       |
      | fake_user_id1 | fake_username1 | fake_email1 | fake_given_name1 | fake_mobile1 | fake_nickname1 | male   | 1        | 2020-07-16 | fake_avatar_url1 | 2         | 10        | fake_extra_phone | 18     | 170    | 70     | fake_country_code |

  Scenario: [3001]家庭数据源的刷新家庭详细信息接口执行失败的情况下，失败回调被执行。
    Given 家庭数据源的刷新家庭详细信息接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的刷新家庭详细信息接口
    Then 使用者收到刷新家庭详细信息接口的回调结果为"失败"
    Then 缓存数据代理存储接口被调用"0"次
    Then 家庭数据源的刷新家庭详细信息接口被调用"1"次,入参家庭ID的值为"fake_family_id1"

  Scenario: [3002]家庭数据源的刷新家庭详细信息接口执行成功的情况下，成功回调被执行，缓存数据存储的值和数据源提供数据保持一致。
    Given 家庭数据源的刷新家庭详细信息接口返回的数据如下:
      | familyId        | familyName          | ownerInfoObject                                                                                        | refFloorObjects               | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_nameNew | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
    When 使用者调用ID为"fake_family_id1"的家庭对象的刷新家庭详细信息接口
    Then 使用者收到刷新家庭详细信息接口的回调结果为"成功",家庭详细信息如下:
      | familyId        | familyName          | ownerInfoObject                                                                                        | refFloorObjects               | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_nameNew | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
    Then 家庭数据源的刷新家庭详细信息接口被调用"1"次,入参家庭ID的值为"fake_family_id1"
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName          | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_nameNew | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2   | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3   |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |

  Scenario: [3003]当添加的房间参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的添加家庭房间接口,参数如下:
      | roomName | floorOrderId | roomClass        |
      |          | 1            | fake_room_class1 |
    Then 使用者收到添加家庭房间接口的回调结果为"失败"
    Then 家庭数据源的添加家庭房间接口被调用"0"次

  Scenario: [3004]家庭数据源的添加家庭房间接口执行失败的情况下，失败回调被执行。
    Given 家庭数据源的添加房间接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的添加家庭房间接口,参数如下:
      | roomName        | floorOrderId | roomClass        |
      | fake_roomName11 | 1            | fake_room_class1 |
    Then 使用者收到添加家庭房间接口的回调结果为"失败"
    Then 家庭数据源的添加家庭房间接口被调用"1"次,参数如下:
      | familyId        | roomName        | floorOrderId | roomClass        |
      | fake_family_id1 | fake_roomName11 | 1            | fake_room_class1 |

  Scenario: [3005]家庭数据源的添加家庭房间接口执行成功的情况下，成功回调被执行，且触发房间列表刷新
    Given 家庭数据源的添加房间接口执行结果为"成功",返回Room对象如下:
      | roomId         | roomName        | roomClass        | floorOrderId | floorId        |
      | fake_room_id99 | fake_roomName11 | fake_room_class1 | 1            | fake_floor_id1 |
    When 使用者调用ID为"fake_family_id1"的家庭对象的添加家庭房间接口,参数如下:
      | roomName        | floorOrderId | roomClass        |
      | fake_roomName11 | 1            | fake_room_class1 |
    Then 使用者收到添加家庭房间接口的回调结果为"成功",返回Room对象如下:
      | roomId         | roomName        | roomClass        | floorOrderId | floorId        |
      | fake_room_id99 | fake_roomName11 | fake_room_class1 | 1            | fake_floor_id1 |
    Then 家庭数据源的添加家庭房间接口被调用"1"次,参数如下:
      | familyId        | roomName        | floorOrderId | roomClass        |
      | fake_family_id1 | fake_roomName11 | 1            | fake_room_class1 |
    Then 家庭数据源的刷新家庭房间列表接口被调用"1"次,家庭ID为"fake_family_id1",楼层ID为"fake_floor_id1"

  Scenario Outline: [3006]当删除的房间参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除家庭房间接口,删除的房间ID为"<roomId>"
    Then 使用者收到删除房间接口的回调结果为"失败"
    Then 家庭数据源的删除房间接口被调用"0"次
    Examples:
      | roomId   |
      | 空字符串 |


  Scenario: [3007]家庭数据源的删除家庭房间接口执行失败的情况下，失败回调被执行。
    Given 家庭数据源的删除房间接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除家庭房间接口,删除的房间ID为"fake_room_id1"
    Then 使用者收到删除房间接口的回调结果为"失败"
    Then 缓存数据代理存储接口被调用"0"次
    Then 家庭数据源的删除房间接口被调用"1"次,参数如下:
      | familyId        | roomId        |
      | fake_family_id1 | fake_room_id1 |

  Scenario: [3008]家庭数据源的删除家庭房间接口执行成功的情况下，成功回调被执行，且触发家庭列表刷新通知
    Given 家庭数据源的删除房间接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除家庭房间接口,删除的房间ID为"fake_room_id1"
    Then 使用者收到删除房间接口的回调结果为"成功"
    Then 家庭数据源的删除房间接口被调用"1"次,参数如下:
      | familyId        | roomId        |
      | fake_family_id1 | fake_room_id1 |
    Then 家庭ID为"fake_family_id1"的家庭对象房间列表数据如下:
      | floorId        | roomId        | roomName        | roomClass        | roomLabel        | roomLogo        | roomPicture     |
      | fake_floor_id1 | fake_room_id6 | fake_room_name6 | fake_room_class6 | fake_room_label6 | fake_room_logo6 | fake_room_pic_6 |
      | fake_floor_id2 | fake_room_id2 | fake_room_name2 | fake_room_class2 | fake_room_label2 | fake_room_logo2 | fake_room_pic_2 |
    Then 事件处理器收到"notifyRefreshFamilyListSuccess"

  Scenario Outline: [3009]当编辑家庭房间名称参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭房间名称接口,编辑房间ID为"<roomId>"的房间名称为"<roomName>"
    Then 使用者收到编辑房间名称接口的回调结果为"失败"
    Then 家庭数据源的编辑房间名称接口被调用"0"次
    Examples:
      | roomId        | roomName         |
      |               | fake_room_name11 |
      | fake_room_id1 |                  |


  Scenario: [3010]家庭数据源的编辑家庭房间名称接口执行失败的情况下，失败回调被执行。
    Given 家庭数据源的编辑房间名称接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭房间名称接口,编辑房间ID为"fake_room_id1"的房间名称为"fake_room_name11"
    Then 使用者收到编辑房间名称接口的回调结果为"失败"
    Then 家庭数据源的编辑房间名称接口被调用"1"次,参数如下:
      | familyId        | floorId        | roomId        | roomName         |
      | fake_family_id1 | fake_floor_id1 | fake_room_id1 | fake_room_name11 |

  Scenario: [3011]家庭数据源的编辑家庭房间名称接口执行成功的情况下，缓存数据存储的房间名称，已更改为新名称。
    Given 家庭数据源的编辑房间名称接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭房间名称接口,编辑房间ID为"fake_room_id1"的房间名称为"fake_room_name11"
    Then 使用者收到编辑房间名称接口的回调结果为"成功"
    Then 家庭数据源的编辑房间名称接口被调用"1"次,参数如下:
      | familyId        | floorId        | roomId        | roomName         |
      | fake_family_id1 | fake_floor_id1 | fake_room_id1 | fake_room_name11 |
    Then 家庭ID为"fake_family_id1"的家庭对象房间列表数据如下:
      | floorId        | roomId        | roomName         | roomClass        | roomLabel        | roomLogo        | roomPicture     |
      | fake_floor_id1 | fake_room_id1 | fake_room_name11 | fake_room_class1 | fake_room_label1 | fake_room_logo1 | fake_room_pic_1 |
      | fake_floor_id2 | fake_room_id2 | fake_room_name2  | fake_room_class2 | fake_room_label2 | fake_room_logo2 | fake_room_pic_2 |
      | fake_floor_id1 | fake_room_id6 | fake_room_name6  | fake_room_class6 | fake_room_label6 | fake_room_logo6 | fake_room_pic_6 |

  Scenario Outline: [3012]当邀请家庭成员参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的邀请家庭成员接口,参数如下:
      | userId   | nickname   | memberRole   | memberType   |
      | <userId> | <nickname> | <memberRole> | <memberType> |
    Then 使用者收到邀请家庭成员接口的回调结果为"失败"
    Then 家庭数据源的邀请家庭成员接口被调用"0"次
    Examples:
      | userId   | nickname  | memberRole | memberType |
      | 空字符串 | fake_nick | admin      | 2          |
      | fake_uid | 空字符串  | admin      | 2          |


  Scenario: [3013]家庭数据源的邀请家庭成员接口执行失败的情况下，失败回调被执行。
    Given 家庭数据源的邀请家庭成员接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的邀请家庭成员接口,参数如下:
      | userId        | nickname       | memberRole | memberType |
      | fake_user_id3 | fake_nickname3 | admin      | 1          |
    Then 使用者收到邀请家庭成员接口的回调结果为"失败"
    Then 家庭数据源的邀请家庭成员接口被调用"1"次,参数如下:
      | familyId        | userId        | nickname       | memberRole | memberType |
      | fake_family_id1 | fake_user_id3 | fake_nickname3 | admin      | 1          |

  Scenario: [3014]家庭数据源的邀请家庭成员接口执行成功的情况下，成功回调被执行。
    Given 家庭数据源的邀请家庭成员接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的邀请家庭成员接口,参数如下:
      | userId        | nickname       | memberRole | memberType |
      | fake_user_id3 | fake_nickname3 | member     | 2          |
    Then 使用者收到邀请家庭成员接口的回调结果为"成功"
    Then 家庭数据源的邀请家庭成员接口被调用"1"次,参数如下:
      | familyId        | userId        | nickname       | memberRole | memberType |
      | fake_family_id1 | fake_user_id3 | fake_nickname3 | member     | 2          |

  Scenario Outline: [3015]当删除家庭成员参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除家庭成员接口,删除ID为"<memberId>"的家庭成员
    Then 使用者收到删除家庭成员接口的回调结果为"失败"
    Then 家庭数据源的删除家庭成员接口被调用"0"次
    Examples:
      | memberId |
      | 空字符串 |


  Scenario: [3016]家庭数据源的删除家庭成员接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的删除家庭成员接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除家庭成员接口,删除ID为"fake_user_id2"的家庭成员
    Then 使用者收到删除家庭成员接口的回调结果为"失败"
    Then 家庭数据源的删除家庭成员接口被调用"1"次,参数如下:
      | familyId        | memberId      |
      | fake_family_id1 | fake_user_id2 |

  Scenario: [3017]家庭数据源的删除家庭成员接口执行成功的情况下，触发用户的家庭列表和设备列表的刷新，且成功回调被执行
    Given 家庭数据源的删除家庭成员接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除家庭成员接口,删除ID为"fake_user_id2"的家庭成员
    Then 使用者收到删除家庭成员接口的回调结果为"成功"
    Then 家庭数据源的删除家庭成员接口被调用"1"次,参数如下:
      | familyId        | memberId      |
      | fake_family_id1 | fake_user_id2 |
    Then 事件处理器收到"operateRefreshFamilyList"
    Then 事件处理器收到"operateRefreshDeviceList"
    Then 家庭数据源刷新家庭列表接口被调用"1"次
    Then 设备数据源刷新设备列表接口被调用"1"次

  Scenario:[3018]当批量解绑设备参数设备数组为空时，解绑结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的批量解绑设备接口,参数如下:
      | deviceId | deviceName | devName | deviceType | familyId | ownerId | permissionObject | wifiType | bindTime | isOnline | ownerInfoObject | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName | apptypeCode | categoryGrouping | barcode | bindType | brand | imageAddr1 | imageAddr2 | model | prodNo | roomName | roomId | roomObject | isOwned | accessType | configType | devFloorId | devFloorOrderId | devFloorName |
    Then 使用者收到批量解绑设备接口的回调结果为"失败"
    Then 家庭数据源的批量解绑设备接口被调用"0"次

  Scenario: [3019]家庭数据源的批量解绑设备接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的批量解绑设备接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的批量解绑设备接口,参数如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到批量解绑设备接口的回调结果为"失败"
    Then 缓存数据代理存储接口被调用"0"次
    Then 家庭数据源的批量解绑设备接口被调用"1"次,参数家庭id为"fake_family_id1",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |

  Scenario: [3020]家庭数据源的批量解绑设备接口执行成功的情况下，删除缓存中相关的设备数据，成功回调被执行
    Given 家庭数据源的批量解绑设备接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的批量解绑设备接口,参数如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到批量解绑设备接口的回调结果为"成功"
    Then 家庭数据源的批量解绑设备接口被调用"1"次,参数家庭id为"fake_family_id1",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_device_list",值如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id3 | fake_device_name3 | fake_dev_name3 | fake_device_type3 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:43 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name3 | fake_room_id3 | {"roomId":"fake_room_id3","roomName":"fake_room_name3","roomClass":"fake_room_class3","roomLabel":"fake_room_label3","roomLogo":"fake_room_logo3","roomPicture":"fake_room_pic_3"} | 1       | 13         | 0          | fake_floor_id3 | fake_floor_order_id3 | fake_floor_name | fake_net_type |
      | fake_device_id4 | fake_device_name4 | fake_dev_name4 | fake_device_type4 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:44 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name4 | fake_room_id4 | {"roomId":"fake_room_id4","roomName":"fake_room_name4","roomClass":"fake_room_class4","roomLabel":"fake_room_label4","roomLogo":"fake_room_logo4","roomPicture":"fake_room_pic_4"} | 1       | 13         | 0          | fake_floor_id4 | fake_floor_order_id4 | fake_floor_name | fake_net_type |
      | fake_device_id5 | fake_device_name5 | fake_dev_name5 | fake_device_type5 | fake_family_id3 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:45 | 0        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name5 | fake_room_id5 | {"roomId":"fake_room_id5","roomName":"fake_room_name5","roomClass":"fake_room_class5","roomLabel":"fake_room_label5","roomLogo":"fake_room_logo5","roomPicture":"fake_room_pic_5"} | 1       | 13         | 0          | fake_floor_id5 | fake_floor_order_id5 | fake_floor_name | fake_net_type |
    Then 事件处理器收到"notifyRefreshDeviceListSuccess"

  Scenario Outline: [3021]当批量移动设备到新房间参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的批量移动设备到新房间接口,房间ID为"<roomId>",设备列表如下:
      | deviceId | deviceName | devName | deviceType | familyId | ownerId | permissionObject | wifiType | bindTime | isOnline | ownerInfoObject | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName | apptypeCode | categoryGrouping | barcode | bindType | brand | imageAddr1 | imageAddr2 | model | prodNo | roomName | roomId | roomObject | isOwned | accessType | configType | devFloorId | devFloorOrderId | devFloorName |
    Then 使用者收到批量移动设备到新房间接口的回调结果为"失败"
    Then 家庭数据源的批量移动设备到新房间接口被调用"0"次
    Examples:
      | roomId        |
      |               |
      | fake_room_id2 |


  Scenario: [3022]家庭数据源的批量移动设备到新房间接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的批量移动设备到新房间接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的批量移动设备到新房间接口,房间ID为"fake_room_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到批量移动设备到新房间接口的回调结果为"失败"
    Then 家庭数据源的批量移动设备到新房间接口被调用"1"次,参数家庭id为"fake_family_id1",房间ID为"fake_room_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |

  Scenario: [3023]家庭数据源的批量移动设备到新房间接口执行成功的情况下，触发用户的设备列表的刷新，成功回调被执行
    Given 家庭数据源的批量移动设备到新房间接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的批量移动设备到新房间接口,房间ID为"fake_room_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到批量移动设备到新房间接口的回调结果为"成功"
    Then 家庭数据源的批量移动设备到新房间接口被调用"1"次,参数家庭id为"fake_family_id1",房间ID为"fake_room_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 事件处理器收到"operateRefreshDeviceList"
    Then 设备数据源刷新设备列表接口被调用"1"次

  Scenario Outline: [3027]当移动设备到新的家庭参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的移动设备到新的家庭接口,参数家庭id为"<newFamilyId>",设备列表如下:
      | deviceId | deviceName | devName | deviceType | familyId | ownerId | permissionObject | wifiType | bindTime | isOnline | ownerInfoObject | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName | apptypeCode | categoryGrouping | barcode | bindType | brand | imageAddr1 | imageAddr2 | model | prodNo | roomName | roomId | roomObject | isOwned | accessType | configType |
    Then 使用者收到批量移动设备到新的家庭接口的回调结果为"失败"
    Then 家庭数据源的批量移动设备到新的家庭接口被调用"0"次
    Examples:
      | newFamilyId     |
      |                 |
      | fake_family_id2 |
      | fake_family_id2 |


  Scenario: [3028]家庭数据源的移动设备到新的家庭接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的批量移动设备到新的家庭接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的移动设备到新的家庭接口,参数家庭id为"fake_family_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到批量移动设备到新的家庭接口的回调结果为"失败"
    Then 家庭数据源的批量移动设备到新的家庭接口被调用"1"次,原家庭id为"fake_family_id1",新家庭id为"fake_family_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |

  Scenario: [3029]家庭数据源的移动设备到新的家庭接口执行成功的情况下，触发刷新设备列表事件，成功回调被执行
    Given 家庭数据源的批量移动设备到新的家庭接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的移动设备到新的家庭接口,参数家庭id为"fake_family_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到批量移动设备到新的家庭接口的回调结果为"成功"
    Then 家庭数据源的批量移动设备到新的家庭接口被调用"1"次,原家庭id为"fake_family_id1",新家庭id为"fake_family_id2",设备列表如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 事件处理器收到"operateRefreshDeviceList"
    Then 设备数据源刷新设备列表接口被调用"1"次

  Scenario Outline: [3030]当管理员退出家庭参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的管理员退出家庭接口,指定新管理员的用户ID为"<userId>"
    Then 使用者收到管理员退出家庭接口的回调结果为"失败"
    Then 家庭数据源的管理员退出家庭接口被调用"0"次
    Examples:
      | userId   |
      | 空字符串 |


  Scenario: [3031]家庭数据源的管理员退出家庭接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的管理员退出家庭接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的管理员退出家庭接口,指定新管理员的用户ID为"fake_user_id2"
    Then 使用者收到管理员退出家庭接口的回调结果为"失败"
    Then 家庭数据源的管理员退出家庭接口被调用"1"次,参数如下:
      | familyId        | userId        |
      | fake_family_id1 | fake_user_id2 |

  Scenario: [3032]家庭数据源的管理员退出家庭接口执行成功的情况下，删除内存和缓存中与家庭信息有关的家庭列表数据和设备列表数据，触发用户的设备列表刷新和家庭列表刷新，成功回调被执行
    Given 家庭数据源的管理员退出家庭接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的管理员退出家庭接口,指定新管理员的用户ID为"fake_user_id2"
    Then 使用者收到管理员退出家庭接口的回调结果为"成功"
    Then 家庭数据源的管理员退出家庭接口被调用"1"次,参数如下:
      | familyId        | userId        |
      | fake_family_id1 | fake_user_id2 |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_device_list",值如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id3 | fake_device_name3 | fake_dev_name3 | fake_device_type3 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:43 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name3 | fake_room_id3 | {"roomId":"fake_room_id3","roomName":"fake_room_name3","roomClass":"fake_room_class3","roomLabel":"fake_room_label3","roomLogo":"fake_room_logo3","roomPicture":"fake_room_pic_3"} | 1       | 13         | 0          | fake_floor_id3 | fake_floor_order_id3 | fake_floor_name | fake_net_type |
      | fake_device_id4 | fake_device_name4 | fake_dev_name4 | fake_device_type4 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:44 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name4 | fake_room_id4 | {"roomId":"fake_room_id4","roomName":"fake_room_name4","roomClass":"fake_room_class4","roomLabel":"fake_room_label4","roomLogo":"fake_room_logo4","roomPicture":"fake_room_pic_4"} | 1       | 13         | 0          | fake_floor_id4 | fake_floor_order_id4 | fake_floor_name | fake_net_type |
      | fake_device_id5 | fake_device_name5 | fake_dev_name5 | fake_device_type5 | fake_family_id3 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:45 | 0        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name5 | fake_room_id5 | {"roomId":"fake_room_id5","roomName":"fake_room_name5","roomClass":"fake_room_class5","roomLabel":"fake_room_label5","roomLogo":"fake_room_logo5","roomPicture":"fake_room_pic_5"} | 1       | 13         | 0          | fake_floor_id5 | fake_floor_order_id5 | fake_floor_name | fake_net_type |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 事件处理器收到"operateRefreshFamilyList"
    Then 事件处理器收到"operateRefreshDeviceList"
    Then 家庭数据源刷新家庭列表接口被调用"1"次
    Then 设备数据源刷新设备列表接口被调用"1"次

  Scenario: [3033]家庭数据源的管理员删除家庭接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的管理员删除家庭接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的家庭管理员删除家庭接口
    Then 使用者收到家庭管理员删除家庭接口的回调结果为"失败"
    Then 家庭数据源的管理员删除家庭接口被调用"1"次,参数如下:
      | familyId        |
      | fake_family_id1 |

  Scenario: [3034]家庭数据源的家庭管理员删除家庭接口执行成功的情况下，删除内存和缓存中与家庭信息有关的家庭列表数据和设备列表数据，触发用户的设备列表刷新和家庭列表刷新，成功回调被执行
    Given 家庭数据源的管理员删除家庭接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的家庭管理员删除家庭接口
    Then 使用者收到家庭管理员删除家庭接口的回调结果为"成功"
    Then 家庭数据源的管理员删除家庭接口被调用"1"次,参数如下:
      | familyId        |
      | fake_family_id1 |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_device_list",值如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id3 | fake_device_name3 | fake_dev_name3 | fake_device_type3 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:43 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name3 | fake_room_id3 | {"roomId":"fake_room_id3","roomName":"fake_room_name3","roomClass":"fake_room_class3","roomLabel":"fake_room_label3","roomLogo":"fake_room_logo3","roomPicture":"fake_room_pic_3"} | 1       | 13         | 0          | fake_floor_id3 | fake_floor_order_id3 | fake_floor_name | fake_net_type |
      | fake_device_id4 | fake_device_name4 | fake_dev_name4 | fake_device_type4 | fake_family_id2 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:44 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name4 | fake_room_id4 | {"roomId":"fake_room_id4","roomName":"fake_room_name4","roomClass":"fake_room_class4","roomLabel":"fake_room_label4","roomLogo":"fake_room_logo4","roomPicture":"fake_room_pic_4"} | 1       | 13         | 0          | fake_floor_id4 | fake_floor_order_id4 | fake_floor_name | fake_net_type |
      | fake_device_id5 | fake_device_name5 | fake_dev_name5 | fake_device_type5 | fake_family_id3 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:45 | 0        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name5 | fake_room_id5 | {"roomId":"fake_room_id5","roomName":"fake_room_name5","roomClass":"fake_room_class5","roomLabel":"fake_room_label5","roomLogo":"fake_room_logo5","roomPicture":"fake_room_pic_5"} | 1       | 13         | 0          | fake_floor_id5 | fake_floor_order_id5 | fake_floor_name | fake_net_type |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 事件处理器收到"operateRefreshFamilyList"
    Then 事件处理器收到"operateRefreshDeviceList"
    Then 家庭数据源刷新家庭列表接口被调用"1"次
    Then 设备数据源刷新设备列表接口被调用"1"次

  Scenario Outline: [3035]当更换家庭管理员参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的更换家庭管理员接口,指定新管理员的用户ID为"<userId>"
    Then 使用者收到更换家庭管理员接口的回调结果为"失败"
    Then 家庭数据源的更换家庭管理员接口被调用"0"次
    Examples:
      | userId   |
      | 空字符串 |


  Scenario: [3036]家庭数据源的更换家庭管理员接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的更换家庭管理员接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的更换家庭管理员接口,指定新管理员的用户ID为"fake_user_id2"
    Then 使用者收到更换家庭管理员接口的回调结果为"失败"
    Then 家庭数据源的更换家庭管理员接口被调用"1"次,参数如下:
      | familyId        | userId        |
      | fake_family_id1 | fake_user_id2 |

  Scenario: [3037]家庭数据源的更换家庭管理员接口执行成功的情况下，成功回调被执行，触发用户的设备列表刷新和家庭列表刷新
    Given 家庭数据源的更换家庭管理员接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的更换家庭管理员接口,指定新管理员的用户ID为"fake_user_id2"
    Then 使用者收到更换家庭管理员接口的回调结果为"成功"
    Then 家庭数据源的更换家庭管理员接口被调用"1"次,参数如下:
      | familyId        | userId        |
      | fake_family_id1 | fake_user_id2 |
    Then 事件处理器收到"operateRefreshAllFamilyDetail"
    Then 家庭数据源的刷新家庭详细信息接口被调用"3"次

  Scenario Outline: [3038]当编辑家庭信息参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭信息接口,参数如下:
      | familyName   | familyLocationObject   | familyPosition   |
      | <familyName> | <familyLocationObject> | <familyPosition> |
    Then 使用者收到编辑家庭信息接口的回调结果为"失败"
    Then 家庭数据源的编辑家庭信息接口被调用"0"次
    Examples:
      | familyName | familyLocationObject                                          | familyPosition |
      |            | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | 西安市雁塔区   |


  Scenario: [3039]家庭数据源的编辑家庭信息接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的编辑家庭信息接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭信息接口,参数如下:
      | familyName         | familyLocationObject                                          | familyPosition |
      | fake_family_name11 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | 西安市雁塔区   |
    Then 使用者收到编辑家庭信息接口的回调结果为"失败"
    Then 缓存数据代理存储接口被调用"0"次,键为"userdomain_family_list"
    Then 家庭数据源的编辑家庭信息接口被调用"1"次,参数如下:
      | familyId        | familyName         | familyLocationObject                                          | familyPosition |
      | fake_family_id1 | fake_family_name11 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | 西安市雁塔区   |

  Scenario: [3040]家庭数据源的编辑家庭信息接口执行成功的情况下，修改本地家庭信息数据，成功回调被执行
    Given 家庭数据源的编辑家庭信息接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭信息接口,参数如下:
      | familyName         | familyLocationObject                                          | familyPosition |
      | fake_family_name11 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | 西安市雁塔区   |
    Then 使用者收到编辑家庭信息接口的回调结果为"成功"
    Then 家庭数据源的编辑家庭信息接口被调用"1"次,参数如下:
      | familyId        | familyName         | familyLocationObject                                          | familyPosition |
      | fake_family_id1 | fake_family_name11 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | 西安市雁塔区   |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName         | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name11 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2  | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3  |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |

  Scenario: [3041]家庭数据源的刷新家庭房间列表接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的刷新家庭房间列表接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的刷新家庭房间列表接口,参数为"fake_floor_id1"
    Then 使用者收到刷新家庭房间列表接口的回调结果为"失败"
    Then 家庭数据源的刷新家庭房间列表接口被调用"1"次,家庭ID为"fake_family_id1",楼层ID为"fake_floor_id1"

  Scenario: [3042]家庭数据源的刷新家庭房间列表接口执行成功的情况下，修改本地家庭信息数据，成功回调被执行
    Given 家庭数据源的刷新家庭房间列表接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的刷新家庭房间列表接口,参数为"fake_floor_id1"
    Then 使用者收到刷新家庭房间列表接口的回调结果为"成功"
    Then 家庭数据源的刷新家庭房间列表接口被调用"1"次,家庭ID为"fake_family_id1",楼层ID为"fake_floor_id1"
    Then 家庭ID为"fake_family_id1"的家庭对象房间列表数据如下:
      | floorId        | roomId        | roomName        | roomClass        | roomLabel        | roomLogo        | roomPicture     |
      | fake_floor_id1 | fake_room_id1 | fake_room_name1 | fake_room_class1 | fake_room_label1 | fake_room_logo1 | fake_room_pic_1 |
      | fake_floor_id2 | fake_room_id2 | fake_room_name2 | fake_room_class2 | fake_room_label2 | fake_room_logo2 | fake_room_pic_2 |
      | fake_floor_id1 | fake_room_id6 | fake_room_name6 | fake_room_class6 | fake_room_label6 | fake_room_logo6 | fake_room_pic_6 |

      | fake_floor_id5 | 0 | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区 | 1 |

  Scenario: [3044]家庭数据源的家庭成员退出家庭接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的家庭成员退出家庭接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id2"的家庭对象的成员退出家庭接口
    Then 使用者收到成员退出家庭接口的回调结果为"失败"
    Then 家庭数据源的家庭成员退出家庭接口被调用"1"次,参数如下:
      | familyId        |
      | fake_family_id2 |

  Scenario: [3045]家庭数据源的家庭退出家庭接口执行成功的情况下，删除内存和缓存中与家庭信息有关的家庭列表数据和设备列表数据，触发用户的设备列表刷新和家庭列表刷新，成功回调被执行
    Given 家庭数据源的家庭成员退出家庭接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id2"的家庭对象的成员退出家庭接口
    Then 使用者收到成员退出家庭接口的回调结果为"成功"
    Then 家庭数据源的家庭成员退出家庭接口被调用"1"次,参数如下:
      | familyId        |
      | fake_family_id2 |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_device_list",值如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
      | fake_device_id5 | fake_device_name5 | fake_dev_name5 | fake_device_type5 | fake_family_id3 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:45 | 0        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name5 | fake_room_id5 | {"roomId":"fake_room_id5","roomName":"fake_room_name5","roomClass":"fake_room_class5","roomLabel":"fake_room_label5","roomLogo":"fake_room_logo5","roomPicture":"fake_room_pic_5"} | 1       | 13         | 0          | fake_floor_id5 | fake_floor_order_id5 | fake_floor_name | fake_net_type |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 事件处理器收到"operateRefreshFamilyList"
    Then 事件处理器收到"operateRefreshDeviceList"
    Then 家庭数据源刷新家庭列表接口被调用"1"次
    Then 设备数据源刷新设备列表接口被调用"1"次

  Scenario: [3046]当创建楼层参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的创建楼层接口,参数如下:
      | floorOrderId | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       |
      |              | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture |
    Then 使用者收到创建楼层接口的回调结果为"失败"
    Then 家庭数据源的创建楼层接口被调用"0"次

  Scenario: [3047]家庭数据源的创建楼层接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的创建楼层接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的创建楼层接口,参数如下:
      | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       |
      | fake_floor_order_id1 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture |
    Then 使用者收到创建楼层接口的回调结果为"失败"
    Then 家庭数据源的创建楼层接口被调用"1"次,参数如下:
      | familyId        | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       |
      | fake_family_id1 | fake_floor_order_id1 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture |

  Scenario: [3048]家庭数据源的创建楼层接口执行成功的情况下，修改本地家庭信息数据，成功回调被执行
    Given 家庭数据源的创建楼层接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的创建楼层接口,参数如下:
      | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       |
      | fake_floor_order_id1 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture |
    Then 使用者收到创建楼层接口的回调结果为"成功"
    Then 家庭数据源的创建楼层接口被调用"1"次,参数如下:
      | familyId        | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       |
      | fake_family_id1 | fake_floor_order_id1 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture |
    Then 事件处理器收到"operateRefreshFamilyList"
    Then 家庭数据源刷新家庭列表接口被调用"1"次

  Scenario: [3049]当删除楼层参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除楼层接口,参数如下:
      | floorId |
      |         |
    Then 使用者收到删除楼层接口的回调结果为"失败"
    Then 家庭数据源的删除楼层接口被调用"0"次

  Scenario: [3050]家庭数据源的删除楼层接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的删除楼层接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除楼层接口,参数如下:
      | floorId        |
      | fake_floor_id1 |
    Then 使用者收到删除楼层接口的回调结果为"失败"
    Then 家庭数据源的删除楼层接口被调用"1"次,参数如下:
      | familyId        | floorId        |
      | fake_family_id1 | fake_floor_id1 |

  Scenario: [3051]家庭数据源的删除楼层接口执行成功的情况下，修改本地家庭信息数据，成功回调被执行
    Given 家庭数据源的删除楼层接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的删除楼层接口,参数如下:
      | floorId        |
      | fake_floor_id1 |
    Then 使用者收到删除楼层接口的回调结果为"成功"
    Then 家庭数据源的删除楼层接口被调用"1"次,参数如下:
      | familyId        | floorId        |
      | fake_family_id1 | fake_floor_id1 |
    Then 家庭ID为"fake_family_id1"的家庭对象楼层列表数据如下:
      | floorId        | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       | floorCreateTime        | refRoomObjects                                                                                                                                                                     |
      | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture | fake_floor_create_time | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id2                | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 事件处理器收到"notifyRefreshFamilyListSuccess"

  Scenario: [3055]家庭数据源的查询第一个加入家庭的成员接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的查询第一个加入家庭的成员接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的查询第一个加入家庭的成员接口
    Then 使用者收到查询第一个加入家庭的成员接口的回调结果为"失败"
    Then 家庭数据源的查询第一个加入家庭的成员接口被调用"1"次,参数家庭id为"fake_family_id1"

  Scenario: [3056]家庭数据源的查询第一个加入家庭的成员接口执行成功的情况下，成功回调被执行
    Given 家庭"fake_family_id1"的第一个加入家庭的成员信息如下:
      | userId        | name           | memberName       |
      | fake_user_id1 | fake_username1 | fake_memberName1 |
    Given 家庭数据源的查询第一个加入家庭的成员接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的查询第一个加入家庭的成员接口
    Then 家庭数据源的查询第一个加入家庭的成员接口被调用"1"次,参数家庭id为"fake_family_id1"
    Then 使用者收到查询第一个加入家庭的成员接口的回调结果为"成功"
    Then 家庭ID为"fake_family_id1"的第一个加入家庭的成员信息如下:
      | userId        | name           | memberName       |
      | fake_user_id1 | fake_username1 | fake_memberName1 |

  Scenario Outline: [3057]当虚拟用户加入家庭参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的虚拟用户加入家庭接口,成员ID为"<memberId>",成员名称为"<memberName>"
    Then 使用者收到虚拟用户加入家庭接口的回调结果为"失败"
    Then 家庭数据源的虚拟用户加入家庭接口被调用"0"次
    Examples:
      | memberId      | memberName     |
      |               | fake_username1 |
      | fake_user_id1 |                |


  Scenario: [3058]家庭数据源的虚拟用户加入家庭接口执行失败的情况下，失败回调被执行
    Given 家庭数据源的虚拟用户加入家庭接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的虚拟用户加入家庭接口,参数如下:
      | memberId      | memberName       |
      | fake_user_id1 | fake_memberName1 |
    Then 家庭数据源的虚拟用户加入家庭接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberName       |
      | fake_family_id1 | fake_user_id1 | fake_memberName1 |
    Then 使用者收到虚拟用户加入家庭接口的回调结果为"失败"

  Scenario: [3059]家庭数据源的虚拟用户加入家庭接口执行成功的情况下，发送刷新家庭详情事件，成功回调被执行
    Given 家庭数据源的虚拟用户加入家庭接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的虚拟用户加入家庭接口,参数如下:
      | memberId      | memberName       |
      | fake_user_id1 | fake_memberName1 |
    Then 家庭数据源的虚拟用户加入家庭接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberName       |
      | fake_family_id1 | fake_user_id1 | fake_memberName1 |
    Then 事件处理器收到"operateRefreshAllFamilyDetail"
    Then 家庭数据源的刷新家庭详细信息接口被调用"3"次
    Then 使用者收到虚拟用户加入家庭接口的回调结果为"成功"

  Scenario Outline: [3060]当编辑虚拟用户信息接口参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑虚拟用户接口,参数如下:
      | memberId   | memberName   | avatarUrl        | isCreate | birthday       |
      | <memberId> | <memberName> | fake_avatar_url1 | false    | fake_birthday1 |
    Then 家庭数据源的编辑虚拟用户接口被调用"0"次
    Then 使用者收到编辑虚拟用户接口的回调结果为"失败"
    Examples:
      | memberId      | memberName       |
      |               | fake_memberName1 |
      | fake_user_id1 |                  |

  Scenario: [3061]当家庭数据源的编辑虚拟用户信息接口执行失败时，回调失败
    Given 家庭数据源的编辑虚拟用户接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑虚拟用户接口,参数如下:
      | memberId      | memberName     | avatarUrl | isCreate | birthday       |
      | fake_user_id1 | fake_username1 |           | false    | fake_birthday1 |
    Then 家庭数据源的编辑虚拟用户接口被调用"1"次,参数如下:
      | memberId      | memberName     | avatarUrl | isCreate | birthday       |
      | fake_user_id1 | fake_username1 |           | false    | fake_birthday1 |
    Then 使用者收到编辑虚拟用户接口的回调结果为"失败"


  Scenario: [3062]当家庭数据源的编辑虚拟用户信息接口执行成功时，更新本地家庭中的成员信息，回调成功
    Given 家庭数据源的编辑虚拟用户接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑虚拟用户接口,参数如下:
      | memberId      | memberName       | avatarUrl          | isCreate | birthday         |
      | fake_user_id1 | fake_username1_1 | fake_avatar_url1_1 | false    | fake_birthday1_1 |
    Then 家庭数据源的编辑虚拟用户接口被调用"1"次,参数如下:
      | memberId      | memberName       | avatarUrl          | isCreate | birthday         |
      | fake_user_id1 | fake_username1_1 | fake_avatar_url1_1 | false    | fake_birthday1_1 |
    Then 家庭ID为"fake_family_id1"的家庭成员列表数据如下:
      | familyId        | joinTime            | memberName     | memberInfoObject                                                                                                                         | shareDeviceCount | memberRole       |
      | fake_family_id1 | 2019-12-10 15:27:41 | fake_username1 | {"userId":"fake_user_id1","name":"fake_username1_1","avatar":"fake_avatar_url1_1","mobile":"fake_mobile1","birthday":"fake_birthday1_1"} | 3                | fake_memberRole1 |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 使用者收到编辑虚拟用户接口的回调结果为"成功"

  Scenario Outline: [3063]当编辑家庭成员身份信息接口参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭成员身份接口,参数如下:
      | memberId   | memberRole   |
      | <memberId> | <memberRole> |
    Then 家庭数据源的编辑家庭成员身份接口被调用"0"次
    Then 使用者收到编辑家庭成员身份接口的回调结果为"失败"
    Examples:
      | memberId      | memberRole       |
      |               | fake_memberRole1 |
      | fake_user_id1 |                  |


  Scenario: [3064]当家庭数据源的编辑家庭成员身份接口执行失败时，回调失败
    Given 家庭数据源的编辑家庭成员身份接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭成员身份接口,参数如下:
      | memberId      | memberRole       |
      | fake_user_id1 | fake_memberRole1 |
    Then 家庭数据源的编辑家庭成员身份接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberRole       |
      | fake_family_id1 | fake_user_id1 | fake_memberRole1 |
    Then 使用者收到编辑家庭成员身份接口的回调结果为"失败"

  Scenario: [3065]当家庭数据源的编辑家庭成员身份信息接口执行成功时，更新本地家庭中的成员信息，回调成功
    Given 家庭数据源的编辑家庭成员身份接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭成员身份接口,参数如下:
      | memberId      | memberRole         |
      | fake_user_id1 | fake_memberRole1_1 |
    Then 家庭数据源的编辑家庭成员身份接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberRole         |
      | fake_family_id1 | fake_user_id1 | fake_memberRole1_1 |
    Then 家庭ID为"fake_family_id1"的家庭成员列表数据如下:
      | familyId        | joinTime            | memberName     | memberInfoObject                                                                                                                   | shareDeviceCount | memberRole         |
      | fake_family_id1 | 2019-12-10 15:27:41 | fake_username1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1","birthday":"fake_birthday1"} | 3                | fake_memberRole1_1 |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 使用者收到编辑家庭成员身份接口的回调结果为"成功"

  Scenario Outline: [3066]当编辑家庭虚拟成员身份信息接口参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭虚拟成员身份接口,参数如下:
      | memberId   | memberRole   |
      | <memberId> | <memberRole> |
    Then 家庭数据源的编辑家庭虚拟成员身份接口被调用"0"次
    Then 使用者收到编辑家庭虚拟成员身份接口的回调结果为"失败"
    Examples:
      | memberId      | memberRole       |
      |               | fake_memberRole1 |
      | fake_user_id1 |                  |


  Scenario: [3067]当家庭数据源的编辑虚拟家庭成员身份接口执行失败时，回调失败
    Given 家庭数据源的编辑家庭虚拟成员身份接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭虚拟成员身份接口,参数如下:
      | memberId      | memberRole       |
      | fake_user_id1 | fake_memberRole1 |
    Then 家庭数据源的编辑家庭虚拟成员身份接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberRole       |
      | fake_family_id1 | fake_user_id1 | fake_memberRole1 |
    Then 使用者收到编辑家庭虚拟成员身份接口的回调结果为"失败"

  Scenario: [3068]当家庭数据源的编辑家庭虚拟成员身份信息接口执行成功时，更新本地家庭中的虚拟成员信息，回调成功
    Given 家庭数据源的编辑家庭虚拟成员身份接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的编辑家庭虚拟成员身份接口,参数如下:
      | memberId      | memberRole         |
      | fake_user_id1 | fake_memberRole1_1 |
    Then 家庭数据源的编辑家庭虚拟成员身份接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberRole         |
      | fake_family_id1 | fake_user_id1 | fake_memberRole1_1 |
    Then 家庭ID为"fake_family_id1"的家庭虚拟成员列表数据如下:
      | familyId        | joinTime            | memberName     | memberInfoObject                                                                                                                   | shareDeviceCount | memberRole         |
      | fake_family_id1 | 2019-12-10 15:27:41 | fake_username1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1","birthday":"fake_birthday1"} | 3                | fake_memberRole1_1 |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 使用者收到编辑家庭虚拟成员身份接口的回调结果为"成功"

  Scenario: [3069]当设备数据源的获取组设备列表执行失败时，回调失败
    Given 设备数据源的获取组设备列表接口执行结果为"失败",返回组设备列表数据如下:
      | deviceId | deviceName | devName | deviceType | familyId | ownerId | permissionObject | wifiType | bindTime | isOnline | ownerInfoObject | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName | apptypeCode | categoryGrouping | barcode | bindType | brand | imageAddr1 | imageAddr2 | model | prodNo | roomName | roomId | roomObject | isOwned | accessType | configType | devFloorId | devFloorOrderId | devFloorName | deviceNetType |
    When 使用者调用ID为"fake_family_id1"的家庭对象的获取组设备列表接口,参数如下:
      | param |
    Then 设备数据源的获取组设备列表接口被调用"1"次,参数如下:
      | familyId        | filterFlag |
      | fake_family_id1 | false      |
    Then 使用者收到获取组设备列表接口的回调结果为"失败"

  Scenario: [3070]当设备数据源的获取组设备列表接口执行成功时，回调成功
    Given 设备数据源的获取组设备列表接口执行结果为"成功",返回组设备列表数据如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    When 使用者调用ID为"fake_family_id1"的家庭对象的获取组设备列表接口,参数如下:
      | param |
    Then 设备数据源的获取组设备列表接口被调用"1"次,参数如下:
      | familyId        | filterFlag |
      | fake_family_id1 | false      |
    Then 家庭ID为"fake_family_id1"的家庭组设备列表数据如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |
    Then 使用者收到获取组设备列表接口的回调结果为"成功"

  Scenario Outline: [3071]当保存房间顺序接口参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的保存房间顺序接口,参数如下:
      | rooms   | floorId   |
      | <rooms> | <floorId> |
    Then 家庭数据源的保存房间顺序接口被调用"0"次
    Then 使用者收到保存房间顺序接口的回调结果为"失败"
    Examples:
      | rooms                                 | floorId        |
      | []                                    | fake_floor_id1 |
      | ["fake_room_name6","fake_room_name1"] |                |

  Scenario: [3072]当家庭数据源的保存房间顺序接口执行失败时，回调失败
    Given 家庭数据源的保存房间顺序接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的保存房间顺序接口,参数如下:
      | rooms                                 | floorId        |
      | ["fake_room_name6","fake_room_name1"] | fake_floor_id1 |
    Then 家庭数据源的保存房间顺序接口被调用"1"次,参数如下:
      | familyId        | rooms                                 | floorId        |
      | fake_family_id1 | ["fake_room_name6","fake_room_name1"] | fake_floor_id1 |
    Then 使用者收到保存房间顺序接口的回调结果为"失败"

  Scenario: [3073]当家庭数据源的保存房间顺序接口执行成功时，更新本地家庭中的房间列表顺序，回调成功
    Given 家庭数据源的保存房间顺序接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的保存房间顺序接口,参数如下:
      | rooms                                 | floorId        |
      | ["fake_room_name6","fake_room_name1"] | fake_floor_id1 |
    Then 家庭数据源的保存房间顺序接口被调用"1"次,参数如下:
      | familyId        | rooms                                 | floorId        |
      | fake_family_id1 | ["fake_room_name6","fake_room_name1"] | fake_floor_id1 |
    Then 家庭ID为"fake_family_id1"的家庭对象楼层列表数据如下:
      | floorId        | floorOrderId         | floorName       | floorClass       | floorLabel       | floorLogo       | floorPicture       | floorCreateTime        | refRoomObjects                                                                                                                                                                                                                                                                                                                                                                                      |
      | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture | fake_floor_create_time | {"roomId":"fake_room_id6","roomName":"fake_room_name6","roomClass":"fake_room_class6","roomLabel":"fake_room_label6","roomLogo":"fake_room_logo6","roomPicture":"fake_room_pic_6","sortCode":"0"}#{"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1","sortCode":"1"} |
      | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_floor_class | fake_floor_label | fake_floor_logo | fake_floor_picture | fake_floor_create_time | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"}                                                                                                                                                                                                                  |
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 事件处理器收到"notifyRefreshFamilyListSuccess"
    Then 事件处理器收到"notifyCurrentFamilyRoomListChange"
    Then 使用者收到保存房间顺序接口的回调结果为"成功"

  Scenario: [3073a]当非当前家庭的保存房间顺序接口执行成功时，不发送当前家庭房间顺序变化事件
    Given 家庭数据源的保存房间顺序接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id2"的家庭对象的保存房间顺序接口,参数如下:
      | rooms                                 | floorId        |
      | ["fake_room_name3","fake_room_name4"] | fake_floor_id3 |
    Then 家庭数据源的保存房间顺序接口被调用"1"次,参数如下:
      | familyId        | rooms                                 | floorId        |
      | fake_family_id2 | ["fake_room_name3","fake_room_name4"] | fake_floor_id3 |
    Then 事件处理器收到"notifyRefreshFamilyListSuccess"
    Then 使用者收到保存房间顺序接口的回调结果为"成功"

  Scenario:[3074]当家庭下存在设备时,获取设备字典,返回数据不为空
    When 使用者调用ID为"fake_family_id1"的家庭对象获取设备字典接口
    Then 家庭ID为"fake_family_id1"的家庭设备字典数据如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |

  Scenario:[3075]当家庭下存在设备时,获取设备列表,返回数据不为空
    When 使用者调用ID为"fake_family_id1"的家庭对象获取设备列表接口
    Then 家庭ID为"fake_family_id1"的家庭设备列表数据如下:
      | deviceId        | deviceName        | devName        | deviceType        | familyId        | ownerId       | permissionObject                                                              | wifiType       | bindTime            | isOnline | ownerInfoObject                                                                    | subDeviceIds | parentId | deviceRole | deviceRoleType | apptypeName      | apptypeCode      | categoryGrouping       | barcode      | bindType  | brand      | imageAddr1       | imageAddr2       | model      | prodNo           | roomName        | roomId        | roomObject                                                                                                                                                                         | isOwned | accessType | configType | devFloorId     | devFloorOrderId      | devFloorName    | deviceNetType |
      | fake_device_id1 | fake_device_name1 | fake_dev_name1 | fake_device_type1 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:41 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name1 | fake_room_id1 | {"roomId":"fake_room_id1","roomName":"fake_room_name1","roomClass":"fake_room_class1","roomLabel":"fake_room_label1","roomLogo":"fake_room_logo1","roomPicture":"fake_room_pic_1"} | 1       | 13         | 0          | fake_floor_id1 | fake_floor_order_id1 | fake_floor_name | fake_net_type |
      | fake_device_id2 | fake_device_name2 | fake_dev_name2 | fake_device_type2 | fake_family_id1 | fake_user_id1 | {"authType":"fake_auth_type","auth":{"control":true,"set":false,"view":true}} | fake_wifi_type | 2019-12-10 15:27:42 | 1        | {"userId":"fake_user_id1","mobile":"fake_mobile1","userNickName":"fake_nickname1"} |              |          | 1          | 0              | fake_apptypename | fake_apptypecode | fake_category_grouping | fake_barcode | SmartLink | fake_brand | fake_image_addr1 | fake_image_addr2 | fake_model | fake_prod_number | fake_room_name2 | fake_room_id2 | {"roomId":"fake_room_id2","roomName":"fake_room_name2","roomClass":"fake_room_class2","roomLabel":"fake_room_label2","roomLogo":"fake_room_logo2","roomPicture":"fake_room_pic_2"} | 1       | 13         | 0          | fake_floor_id2 | fake_floor_order_id2 | fake_floor_name | fake_net_type |

  Scenario: [3076]当查询房间列表参数无效时,查询结果为失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的刷新家庭房间列表接口,参数为""
    Then 使用者收到刷新家庭房间列表接口的回调结果为"失败"
    Then 家庭数据源的刷新家庭房间列表接口被调用"0"次

  Scenario: [3077]当修改设备卡片状态参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改设备卡片状态接口,参数如下:
      | card_order_list | big_card_list | middle_card_list | small_card_list |
      |                 |               |                  |                 |
    Then 家庭数据源的修改设备卡片状态接口被调用"0"次
    Then 使用者收到修改设备卡片状态接口的回调结果为"失败"

  Scenario: [3078]当家庭数据源的修改设备卡片状态接口执行失败时，回调失败
    Given 家庭数据源的修改设备卡片状态接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改设备卡片状态接口,参数如下:
      | card_order_list     | big_card_list       | middle_card_list    | small_card_list     |
      | ["fake_device_id1"] | ["fake_device_id1"] | ["fake_device_id2"] | ["fake_device_id3"] |
    Then 家庭数据源的修改设备卡片状态接口被调用"1"次
    Then 使用者收到修改设备卡片状态接口的回调结果为"失败"

  Scenario: [3079]当家庭数据源的修改设备卡片状态接口执行成功时，更新本地设备卡片状态，回调成功
    Given 家庭数据源的修改设备卡片状态接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改设备卡片状态接口,参数如下:
      | card_order_list     | big_card_list       | middle_card_list    | small_card_list     |
      | ["fake_device_id1"] | ["fake_device_id1"] | ["fake_device_id2"] | ["fake_device_id3"] |
    Then 家庭数据源的修改设备卡片状态接口被调用"1"次
    Then 使用者收到修改设备卡片状态接口的回调结果为"成功"
    Then 事件处理器收到"notifyRefreshDeviceCacheSuccess"
    Then "fake_device_id1"设备的卡片状态为"2"

  Scenario: [3080]当修改设备聚合状态参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改设备聚合状态接口,参数如下:
      | agg_card |
      | []       |
    Then 家庭数据源的修改设备聚合状态接口被调用"0"次
    Then 使用者收到修改设备聚合状态接口的回调结果为"失败"

  Scenario: [3081]当家庭数据源的修改设备聚合状态接口执行失败时，回调失败
    Given 家庭数据源的修改设备聚合状态接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改设备聚合状态接口,参数如下:
      | agg_type | sort_list           | big_card_list       | small_card_list     |
      | 0        | ["fake_device_id1"] | ["fake_device_id1"] | ["fake_device_id1"] |
    Then 家庭数据源的修改设备聚合状态接口被调用"1"次
    Then 使用者收到修改设备聚合状态接口的回调结果为"失败"

  Scenario: [3082]当家庭数据源的修改设备聚合状态接口执行成功时，更新本地设备聚合状态，回调成功
    Given 家庭数据源的修改设备聚合状态接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改设备聚合状态接口,参数如下:
      | agg_type | sort_list           | big_card_list       | small_card_list |
      | 0        | ["fake_device_id1"] | ["fake_device_id1"] |                 |
    Then 家庭数据源的修改设备聚合状态接口被调用"1"次
    Then 使用者收到修改设备聚合状态接口的回调结果为"成功"
    Then 事件处理器收到"notifyRefreshDeviceCacheSuccess"

  Scenario Outline: [3083]当修改家庭成员类型参数无效时，结果失败
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改家庭成员类型接口,参数如下:
      | memberId   | memberType   |
      | <memberId> | <memberType> |
    Then 家庭数据源的修改家庭成员类型接口被调用"0"次
    Then 使用者收到修改家庭成员类型接口的回调结果为"失败"
    Examples:
      | memberId      | memberType |
      |               | 1          |
      | fake_user_id1 |            |
      | fake_user_id1 | -1         |
      | fake_user_id1 | 3          |

  Scenario: [3084]当家庭数据源的修改家庭成员类型接口执行失败时，回调失败
    Given 家庭数据源的修改家庭成员类型接口执行结果为"失败"
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改家庭成员类型接口,参数如下:
      | memberId      | memberType |
      | fake_user_id2 | 1          |
    Then 家庭数据源的修改家庭成员类型接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberType |
      | fake_family_id1 | fake_user_id2 | 1          |
    Then 使用者收到修改家庭成员类型接口的回调结果为"失败"

  Scenario: [3085]当家庭数据源的修改家庭成员类型接口执行成功时，更新本地家庭成员类型，回调成功
    Given 家庭数据源的修改家庭成员类型接口执行结果为"成功"
    When 使用者调用ID为"fake_family_id1"的家庭对象的修改家庭成员类型接口,参数如下:
      | memberId      | memberType |
      | fake_user_id2 | 1          |
    Then 家庭数据源的修改家庭成员类型接口被调用"1"次,参数如下:
      | familyId        | memberId      | memberType |
      | fake_family_id1 | fake_user_id2 | 1          |
    Then 使用者收到修改家庭成员类型接口的回调结果为"成功"
    Then 缓存数据代理存储接口被调用"1"次,键为"userdomain_family_list",值如下:
      | familyId        | familyName        | ownerInfoObject                                                                                        | refFloorIds                   | isDefault | appId       | createTime          | familyLocationObject                                          | refFamilyMemberObjectIds    | familyPosition | locationChangeFlag |
      | fake_family_id1 | fake_family_name1 | {"userId":"fake_user_id1","name":"fake_username1","avatar":"fake_avatar_url1","mobile":"fake_mobile1"} | fake_floor_id1,fake_floor_id2 | 1         | fake_app_id | 2019-12-10 15:27:47 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id1,fake_user_id2 | 西安市雁塔区   | 1                  |
      | fake_family_id2 | fake_family_name2 | {"userId":"fake_user_id2","name":"fake_username2","avatar":"fake_avatar_url2","mobile":"fake_mobile2"} | fake_floor_id3,fake_floor_id4 | 0         | fake_app_id | 2019-12-10 15:27:48 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id2,fake_user_id3 | 西安市灞桥区   | 0                  |
      | fake_family_id3 | fake_family_name3 |                                                                                                        | fake_floor_id5                | 0         | fake_app_id | 2019-12-10 15:27:49 | {"cityCode":"101110109","lat":"34.192035","lon":"108.873059"} | fake_user_id3,fake_user_id4 | 西安市高新区   | 1                  |
    Then 事件处理器收到"notifyRefreshFamilyListSuccess"