use cucumber::gherkin::Step;
use cucumber::{given, then};

use rust_userdomain::api::event::UserDomainEvent;
use rust_userdomain::api::family::Family;
use rust_userdomain::models::auth_data::AuthData;
use rust_userdomain::models::user_info::UserInfo;

use crate::utils::steps_utils::contains_event;
use crate::utils::user_domain_holder::UserDomainHolder;
use crate::MyWorld;

#[then(expr = "事件处理器收到{string}")]
async fn receive_event(_world: &mut MyWorld, origin_event: String) {
    let expect_event = match origin_event.as_str() {
        "operateRefreshTokenScheduledTask" => UserDomainEvent::OperatePlanedAuthData,
        "operateRefreshUser" => UserDomainEvent::OperateRefreshUser,
        "notifyAuthDataRefreshedSuccess" => {
            UserDomainEvent::MessageRefreshTokenSuccess(AuthData::default())
        }
        "notifyRefreshUserSuccess" => {
            UserDomainEvent::MessageUserInfoRefreshSuccess(UserInfo::default())
        }
        "notifyRefreshFamilyListSuccess" => {
            UserDomainEvent::MessageFamilyListRefreshSuccess(vec![])
        }
        "notifyRefreshFamilyListFailed" => UserDomainEvent::MessageFamilyListRefreshFailed,
        "notifyRefreshDeviceListSuccess" => {
            UserDomainEvent::MessageDeviceListRefreshSuccess(vec![])
        }
        "notifyRefreshDeviceListFailed" => UserDomainEvent::MessageDeviceListRefreshFailed,
        "notifyRefreshDeviceCacheSuccess" => UserDomainEvent::MessageRefreshDeviceListCache,
        "notifyRefreshCompleted" => UserDomainEvent::MessageRefreshComplete,
        "notifyRefreshFailed" => UserDomainEvent::MessageRefreshFailure,
        "operateRefreshDeviceList" => UserDomainEvent::OperateRefreshDeviceList,
        "operateRefreshFamilyList" => UserDomainEvent::OperateRefreshFamilyList,
        "operateRefreshAllFamilyDetail" => UserDomainEvent::OperateRefreshAllFamilyDetail,
        "notifyLogOut" => UserDomainEvent::MessageLogout,
        "notifyRefreshAddressListSuccess" => UserDomainEvent::MessageAddressListRefreshSuccess,
        "operateRefreshAddressList" => UserDomainEvent::OperateRefreshAddressList,
        "notifyCurrentFamilyChanged" => {
            UserDomainEvent::MessageCurrentFamilyChanged(Family::default())
        }
        "notifyCurrentFamilyRoomListChange" => {
            UserDomainEvent::MessageCurrentFamilyRoomListChange(String::new())
        }
        "notifyRefreshUserFailed" => UserDomainEvent::MessageUserInfoRefreshFailed,
        "notifyTokenInvalid" => UserDomainEvent::MessageTokenInvalid,
        "notifyAuthDataRefreshedFailed" => UserDomainEvent::MessageRefreshTokenFailed,
        "notifyTokenMismatchDevice" => UserDomainEvent::MessageTokenMismatchDevice,
        "notifyCancelLogin" => UserDomainEvent::MessageCancelLogin,
        _ => panic!("unknown action"),
    };
    assert!(contains_event(expect_event.value().to_string()));
}

#[then(expr = "事件处理器收到{string}次{string}")]
async fn receive_event_time(_world: &mut MyWorld, count_str: String, origin_event: String) {
    let expect_event = match origin_event.as_str() {
        "notifyRefreshDeviceListSuccess" => {
            UserDomainEvent::MessageDeviceListRefreshSuccess(vec![])
        }
        "notifyRefreshDeviceListFailed" => UserDomainEvent::MessageDeviceListRefreshFailed,
        "notifyRefreshDeviceCacheSuccess" => UserDomainEvent::MessageRefreshDeviceListCache,
        "notifyRefreshFamilyListSuccess" => {
            UserDomainEvent::MessageFamilyListRefreshSuccess(vec![])
        }
        _ => panic!("unknown action"),
    };
    let expect_count = count_str.parse::<i32>().unwrap();
    let actual_count = UserDomainHolder::get_instance().get_count(expect_event.value());
    assert_eq!(expect_count, actual_count);

    assert!(contains_event(expect_event.value().to_string()));
}
